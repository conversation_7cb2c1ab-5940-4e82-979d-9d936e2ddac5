import { useLazyGetSitesInfiniteRecordsQuery } from '@app/api/sitesApiSlice';
import { selectAppState, setSelectedSite } from '@app/appSlice';
import { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';
import InfinitySelect from '../InfinitySelect';

import logger from '@app/utils/logger';
import React, { useCallback, useEffect, useState } from 'react';
import { InfinitySelectGetOptions } from '../InfinitySelect/types';
const LIMIT = 10;

//FIXME
const SiteSelection = () => {
  const { selectedSite } = useAppSelector(selectAppState);
  const [siteIds, setSiteIds] = useState();
  const dispatch = useAppDispatch();
  const isDefault: boolean = selectedSite !== '';

  const [triggerInfiniteRecords, { isLoading, error }] = useLazyGetSitesInfiniteRecordsQuery();

  type modeType = 'multiple' | 'tags' | undefined;

  const mode: modeType = undefined;

  const queryParameters = '';

  const setSiteId = (id: string): void => {
    dispatch(setSelectedSite(id));
  };

  const getPaginatedRecords = async  (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
      const transformFilters: any = {};
      /**
       * Will add the keyvalue if dropdown still visible
       */
      if (searchValue) {
        transformFilters.search = searchValue;
      }

      if (queryParameters) {
        transformFilters.queryParameters = queryParameters;
      }

      const result = await triggerInfiniteRecords({
        limit: LIMIT,
        offset: page - 1,
        ...transformFilters,
      })
        .then((res: any) => {
            logger.info('SideSelection','getPaginatedRecords',res.data);
          if (res.data) {
            setSiteIds(res.data.records[0].id);
            return res.data;
          } else {
            logger.error('SideSelection','getPaginatedRecords',res.error);
            return []
          }
        })
        .catch((error: any) => {
            logger.error('SideSelection','getPaginatedRecords',error);

            return [];
        });
      return result;
    };

  const handleOnChange = (value: any, selectedValues?: Array<any>) => {
    setSiteId(value);
  };

  useEffect(()=>{
    getPaginatedRecords(1,'load','')
  },[])

  return (
    <div className={'yj_cp_channel_selector'}>
      {siteIds && <InfinitySelect
        getPaginatedRecords={getPaginatedRecords}
        formatValue={(value) => {
          return `${value.name}`;
        }}
        onLoaded={(isLoaded: boolean) => console.log(isLoaded)}
        isDefault={true}
        notFoundContent="No Site Available"
        notLoadContent="Failed to load values in Site dropdown"
        onChange={(value, selectedValues) => {
            console.log('value, selectedValues',value, selectedValues)
          handleOnChange(value);
        }}
        placeholder={`Site Name${isDefault}${siteIds}`}
        defaultValues={siteIds}
      />}
    </div>
  );
};

export default SiteSelection;
