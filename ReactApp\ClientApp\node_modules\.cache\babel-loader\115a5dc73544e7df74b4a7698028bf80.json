{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport n from \"prop-types\";\nimport { useId as z, classNames as b } from \"@progress/kendo-react-common\";\nconst m = o.forwardRef((e, g) => {\n    const c = o.useRef(null),\n      d = o.useCallback(() => ({\n        element: c.current\n      }), []);\n    o.useImperativeHandle(g, d);\n    const {\n        className: r,\n        style: h,\n        id: u,\n        children: y\n      } = e,\n      k = z(),\n      s = o.useMemo(() => e.orientation || l.orientation, [e.orientation]),\n      t = s === \"horizontal\",\n      a = o.useMemo(() => e.align && e.align.horizontal ? e.align.horizontal : l.hAlign, [e.align]),\n      i = o.useMemo(() => e.align && e.align.vertical ? e.align.vertical : l.vAlign, [e.align]),\n      f = o.useMemo(() => b(\"k-stack-layout\", {\n        \"k-hstack\": s === \"horizontal\",\n        \"k-vstack\": s === \"vertical\",\n        \"k-justify-content-start\": t && a === \"start\" || !t && i === \"top\",\n        \"k-justify-content-center\": t && a === \"center\" || !t && i === \"middle\",\n        \"k-justify-content-end\": t && a === \"end\" || !t && i === \"bottom\",\n        \"k-justify-content-stretch\": t && a === \"stretch\" || !t && i === \"stretch\",\n        \"k-align-items-start\": !t && a === \"start\" || t && i === \"top\",\n        \"k-align-items-center\": !t && a === \"center\" || t && i === \"middle\",\n        \"k-align-items-end\": !t && a === \"end\" || t && i === \"bottom\",\n        \"k-align-items-stretch\": !t && a === \"stretch\" || t && i === \"stretch\"\n      }, r), [s, t, a, i, r]),\n      v = {\n        gap: `${typeof e.gap == \"number\" ? e.gap + \"px\" : e.gap}`,\n        ...h\n      };\n    return /* @__PURE__ */o.createElement(\"div\", {\n      ref: c,\n      className: f,\n      style: v,\n      id: u || k\n    }, y);\n  }),\n  l = {\n    orientation: \"horizontal\",\n    hAlign: \"stretch\",\n    vAlign: \"stretch\"\n  };\nm.propTypes = {\n  className: n.string,\n  style: n.object,\n  children: n.any,\n  id: n.string,\n  orientation: n.oneOf([\"horizontal\", \"vertical\"]),\n  gap: n.oneOfType([n.string, n.number]),\n  align: n.shape({\n    vertical: n.oneOf([\"top\", \"middle\", \"bottom\", \"stretch\"]),\n    horizontal: n.oneOf([\"start\", \"center\", \"end\", \"stretch\"])\n  })\n};\nm.displayName = \"KendoReactStackLayout\";\nexport { m as StackLayout };", "map": {"version": 3, "names": ["o", "n", "useId", "z", "classNames", "b", "m", "forwardRef", "e", "g", "c", "useRef", "d", "useCallback", "element", "current", "useImperativeHandle", "className", "r", "style", "h", "id", "u", "children", "y", "k", "s", "useMemo", "orientation", "l", "t", "a", "align", "horizontal", "hAlign", "i", "vertical", "vAlign", "f", "v", "gap", "createElement", "ref", "propTypes", "string", "object", "any", "oneOf", "oneOfType", "number", "shape", "displayName", "StackLayout"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/stacklayout/StackLayout.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport n from \"prop-types\";\nimport { useId as z, classNames as b } from \"@progress/kendo-react-common\";\nconst m = o.forwardRef((e, g) => {\n  const c = o.useRef(null), d = o.useCallback(\n    () => ({\n      element: c.current\n    }),\n    []\n  );\n  o.useImperativeHandle(g, d);\n  const { className: r, style: h, id: u, children: y } = e, k = z(), s = o.useMemo(() => e.orientation || l.orientation, [e.orientation]), t = s === \"horizontal\", a = o.useMemo(\n    () => e.align && e.align.horizontal ? e.align.horizontal : l.hAlign,\n    [e.align]\n  ), i = o.useMemo(\n    () => e.align && e.align.vertical ? e.align.vertical : l.vAlign,\n    [e.align]\n  ), f = o.useMemo(\n    () => b(\n      \"k-stack-layout\",\n      {\n        \"k-hstack\": s === \"horizontal\",\n        \"k-vstack\": s === \"vertical\",\n        \"k-justify-content-start\": t && a === \"start\" || !t && i === \"top\",\n        \"k-justify-content-center\": t && a === \"center\" || !t && i === \"middle\",\n        \"k-justify-content-end\": t && a === \"end\" || !t && i === \"bottom\",\n        \"k-justify-content-stretch\": t && a === \"stretch\" || !t && i === \"stretch\",\n        \"k-align-items-start\": !t && a === \"start\" || t && i === \"top\",\n        \"k-align-items-center\": !t && a === \"center\" || t && i === \"middle\",\n        \"k-align-items-end\": !t && a === \"end\" || t && i === \"bottom\",\n        \"k-align-items-stretch\": !t && a === \"stretch\" || t && i === \"stretch\"\n      },\n      r\n    ),\n    [s, t, a, i, r]\n  ), v = {\n    gap: `${typeof e.gap == \"number\" ? e.gap + \"px\" : e.gap}`,\n    ...h\n  };\n  return /* @__PURE__ */ o.createElement(\"div\", { ref: c, className: f, style: v, id: u || k }, y);\n}), l = {\n  orientation: \"horizontal\",\n  hAlign: \"stretch\",\n  vAlign: \"stretch\"\n};\nm.propTypes = {\n  className: n.string,\n  style: n.object,\n  children: n.any,\n  id: n.string,\n  orientation: n.oneOf([\"horizontal\", \"vertical\"]),\n  gap: n.oneOfType([n.string, n.number]),\n  align: n.shape({\n    vertical: n.oneOf([\"top\", \"middle\", \"bottom\", \"stretch\"]),\n    horizontal: n.oneOf([\"start\", \"center\", \"end\", \"stretch\"])\n  })\n};\nm.displayName = \"KendoReactStackLayout\";\nexport {\n  m as StackLayout\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,KAAK,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC1E,MAAMC,CAAC,GAAGN,CAAC,CAACO,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC/B,MAAMC,CAAC,GAAGV,CAAC,CAACW,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGZ,CAAC,CAACa,WAAW,CACzC,OAAO;QACLC,OAAO,EAAEJ,CAAC,CAACK;MACb,CAAC,CAAC,EACF,EACF,CAAC;IACDf,CAAC,CAACgB,mBAAmB,CAACP,CAAC,EAAEG,CAAC,CAAC;IAC3B,MAAM;QAAEK,SAAS,EAAEC,CAAC;QAAEC,KAAK,EAAEC,CAAC;QAAEC,EAAE,EAAEC,CAAC;QAAEC,QAAQ,EAAEC;MAAE,CAAC,GAAGhB,CAAC;MAAEiB,CAAC,GAAGtB,CAAC,CAAC,CAAC;MAAEuB,CAAC,GAAG1B,CAAC,CAAC2B,OAAO,CAAC,MAAMnB,CAAC,CAACoB,WAAW,IAAIC,CAAC,CAACD,WAAW,EAAE,CAACpB,CAAC,CAACoB,WAAW,CAAC,CAAC;MAAEE,CAAC,GAAGJ,CAAC,KAAK,YAAY;MAAEK,CAAC,GAAG/B,CAAC,CAAC2B,OAAO,CAC5K,MAAMnB,CAAC,CAACwB,KAAK,IAAIxB,CAAC,CAACwB,KAAK,CAACC,UAAU,GAAGzB,CAAC,CAACwB,KAAK,CAACC,UAAU,GAAGJ,CAAC,CAACK,MAAM,EACnE,CAAC1B,CAAC,CAACwB,KAAK,CACV,CAAC;MAAEG,CAAC,GAAGnC,CAAC,CAAC2B,OAAO,CACd,MAAMnB,CAAC,CAACwB,KAAK,IAAIxB,CAAC,CAACwB,KAAK,CAACI,QAAQ,GAAG5B,CAAC,CAACwB,KAAK,CAACI,QAAQ,GAAGP,CAAC,CAACQ,MAAM,EAC/D,CAAC7B,CAAC,CAACwB,KAAK,CACV,CAAC;MAAEM,CAAC,GAAGtC,CAAC,CAAC2B,OAAO,CACd,MAAMtB,CAAC,CACL,gBAAgB,EAChB;QACE,UAAU,EAAEqB,CAAC,KAAK,YAAY;QAC9B,UAAU,EAAEA,CAAC,KAAK,UAAU;QAC5B,yBAAyB,EAAEI,CAAC,IAAIC,CAAC,KAAK,OAAO,IAAI,CAACD,CAAC,IAAIK,CAAC,KAAK,KAAK;QAClE,0BAA0B,EAAEL,CAAC,IAAIC,CAAC,KAAK,QAAQ,IAAI,CAACD,CAAC,IAAIK,CAAC,KAAK,QAAQ;QACvE,uBAAuB,EAAEL,CAAC,IAAIC,CAAC,KAAK,KAAK,IAAI,CAACD,CAAC,IAAIK,CAAC,KAAK,QAAQ;QACjE,2BAA2B,EAAEL,CAAC,IAAIC,CAAC,KAAK,SAAS,IAAI,CAACD,CAAC,IAAIK,CAAC,KAAK,SAAS;QAC1E,qBAAqB,EAAE,CAACL,CAAC,IAAIC,CAAC,KAAK,OAAO,IAAID,CAAC,IAAIK,CAAC,KAAK,KAAK;QAC9D,sBAAsB,EAAE,CAACL,CAAC,IAAIC,CAAC,KAAK,QAAQ,IAAID,CAAC,IAAIK,CAAC,KAAK,QAAQ;QACnE,mBAAmB,EAAE,CAACL,CAAC,IAAIC,CAAC,KAAK,KAAK,IAAID,CAAC,IAAIK,CAAC,KAAK,QAAQ;QAC7D,uBAAuB,EAAE,CAACL,CAAC,IAAIC,CAAC,KAAK,SAAS,IAAID,CAAC,IAAIK,CAAC,KAAK;MAC/D,CAAC,EACDjB,CACF,CAAC,EACD,CAACQ,CAAC,EAAEI,CAAC,EAAEC,CAAC,EAAEI,CAAC,EAAEjB,CAAC,CAChB,CAAC;MAAEqB,CAAC,GAAG;QACLC,GAAG,EAAE,GAAG,OAAOhC,CAAC,CAACgC,GAAG,IAAI,QAAQ,GAAGhC,CAAC,CAACgC,GAAG,GAAG,IAAI,GAAGhC,CAAC,CAACgC,GAAG,EAAE;QACzD,GAAGpB;MACL,CAAC;IACD,OAAO,eAAgBpB,CAAC,CAACyC,aAAa,CAAC,KAAK,EAAE;MAAEC,GAAG,EAAEhC,CAAC;MAAEO,SAAS,EAAEqB,CAAC;MAAEnB,KAAK,EAAEoB,CAAC;MAAElB,EAAE,EAAEC,CAAC,IAAIG;IAAE,CAAC,EAAED,CAAC,CAAC;EAClG,CAAC,CAAC;EAAEK,CAAC,GAAG;IACND,WAAW,EAAE,YAAY;IACzBM,MAAM,EAAE,SAAS;IACjBG,MAAM,EAAE;EACV,CAAC;AACD/B,CAAC,CAACqC,SAAS,GAAG;EACZ1B,SAAS,EAAEhB,CAAC,CAAC2C,MAAM;EACnBzB,KAAK,EAAElB,CAAC,CAAC4C,MAAM;EACftB,QAAQ,EAAEtB,CAAC,CAAC6C,GAAG;EACfzB,EAAE,EAAEpB,CAAC,CAAC2C,MAAM;EACZhB,WAAW,EAAE3B,CAAC,CAAC8C,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EAChDP,GAAG,EAAEvC,CAAC,CAAC+C,SAAS,CAAC,CAAC/C,CAAC,CAAC2C,MAAM,EAAE3C,CAAC,CAACgD,MAAM,CAAC,CAAC;EACtCjB,KAAK,EAAE/B,CAAC,CAACiD,KAAK,CAAC;IACbd,QAAQ,EAAEnC,CAAC,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;IACzDd,UAAU,EAAEhC,CAAC,CAAC8C,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC;EAC3D,CAAC;AACH,CAAC;AACDzC,CAAC,CAAC6C,WAAW,GAAG,uBAAuB;AACvC,SACE7C,CAAC,IAAI8C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}