export const FILE_IS_TOO_LARGE = 'File is too large. Maximum uploadable file size is 1GB. Remove the File';
export const TOO_MANY_FILES = 'Too many files.. Maximum number of uploadable files are 20';
export const LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE = 'Low file space. Please contact your administrator';
export const NETWORK_ERROR_MESSAGE = 'Network error';
export const RETRYING_MESSAGE = 'Operation could not be completed. Retrying...';
export const UPLOAD_FAILED_MESSAGE = 'Upload Failed. Retry or Remove';
export const FILE_INVALID = `Invalid file name: Only the following characters are allowed:
      - Letters (a-z, A-Z)
      - Numbers (0-9)
      - Special characters: ! ^ @ . ( ) ~ _ \\ - + = ' , $ ? " / > and £ (Pound)`;