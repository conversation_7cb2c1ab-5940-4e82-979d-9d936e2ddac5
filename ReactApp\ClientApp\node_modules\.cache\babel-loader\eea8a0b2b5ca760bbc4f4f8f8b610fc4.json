{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as T, useId as $, useDir as B, classNames as _, dispatchEvent as g, Keys as w } from \"@progress/kendo-react-common\";\nimport { POSITION_MODE_CLASSES as L, ITEM_FLOW_CLASSES as P } from \"./models/utils.mjs\";\nimport { BottomNavigationItem as A } from \"./BottomNavigationItem.mjs\";\nconst I = o.forwardRef((r, E) => {\n    const i = o.useRef(null),\n      b = o.useCallback(() => {\n        i.current && T(i.current);\n      }, []),\n      c = o.useCallback(() => ({\n        element: i.current,\n        focus: b\n      }), [b]);\n    o.useImperativeHandle(E, c);\n    const {\n        positionMode: v = s.positionMode,\n        itemFlow: C = s.itemFlow,\n        border: y = s.border,\n        className: M,\n        items: a,\n        item: N,\n        itemRender: O,\n        disabled: m,\n        style: p,\n        id: h,\n        onSelect: f,\n        onKeyDown: k\n      } = r,\n      S = $(),\n      F = B(i, r.dir),\n      n = o.useMemo(() => r.fillMode === null || r.fill === null ? null : r.fill || r.fillMode || s.fillMode, [r.fillMode, r.fill]),\n      u = o.useMemo(() => r.themeColor || s.themeColor, [r.themeColor]),\n      D = o.useMemo(() => _(\"k-bottom-nav\", L[v], P[C], {\n        [`k-bottom-nav-${n}`]: n,\n        [`k-bottom-nav-${n}-${u}`]: !!(n && u),\n        \"k-bottom-nav-border\": y,\n        \"k-disabled\": m\n      }, M), [v, u, n, C, y, m, M]),\n      d = o.useCallback((e, l) => {\n        a && !a[l].disabled && f && g(f, e, c(), {\n          itemTarget: a[l],\n          itemIndex: l\n        });\n      }, [a, f]),\n      R = o.useCallback((e, l) => {\n        d(e, l);\n      }, [d]),\n      K = o.useCallback((e, l) => {\n        switch (e.keyCode) {\n          case w.enter:\n          case w.space:\n            d(e, l), e.preventDefault();\n            break;\n        }\n        g(k, e, c(), void 0);\n      }, [d, k]);\n    return /* @__PURE__ */o.createElement(\"nav\", {\n      ref: i,\n      className: D,\n      style: p,\n      id: h || S,\n      dir: F\n    }, a && a.map((e, l) => /* @__PURE__ */o.createElement(A, {\n      ...e,\n      key: l,\n      index: l,\n      id: `${h || S}-${l}`,\n      disabled: m || e.disabled,\n      selected: e.selected,\n      dataItem: e,\n      item: N,\n      render: O,\n      onSelect: R,\n      onKeyDown: K\n    })));\n  }),\n  s = {\n    themeColor: \"primary\",\n    fillMode: \"flat\",\n    itemFlow: \"vertical\",\n    positionMode: \"fixed\",\n    border: !0\n  };\nI.propTypes = {\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  dir: t.string,\n  themeColor: t.oneOf([\"primary\", \"secondary\", \"tertiary\", \"info\", \"success\", \"warning\", \"error\", \"dark\", \"light\", \"inverse\"]),\n  fill: t.oneOf([\"solid\", \"flat\"]),\n  fillMode: t.oneOf([\"solid\", \"flat\"]),\n  itemFlow: t.oneOf([\"vertical\", \"horizontal\"]),\n  border: t.bool,\n  disabled: t.bool,\n  selected: t.number,\n  onSelect: t.func\n};\nI.displayName = \"KendoReactBottomNavigation\";\nexport { I as BottomNavigation };", "map": {"version": 3, "names": ["o", "t", "focusFirstFocusableChild", "T", "useId", "$", "useDir", "B", "classNames", "_", "dispatchEvent", "g", "Keys", "w", "POSITION_MODE_CLASSES", "L", "ITEM_FLOW_CLASSES", "P", "BottomNavigationItem", "A", "I", "forwardRef", "r", "E", "i", "useRef", "b", "useCallback", "current", "c", "element", "focus", "useImperativeHandle", "positionMode", "v", "s", "itemFlow", "C", "border", "y", "className", "M", "items", "a", "item", "N", "itemRender", "O", "disabled", "m", "style", "p", "id", "h", "onSelect", "f", "onKeyDown", "k", "S", "F", "dir", "n", "useMemo", "fillMode", "fill", "u", "themeColor", "D", "d", "e", "l", "itemTarget", "itemIndex", "R", "K", "keyCode", "enter", "space", "preventDefault", "createElement", "ref", "map", "key", "index", "selected", "dataItem", "render", "propTypes", "string", "object", "oneOf", "bool", "number", "func", "displayName", "BottomNavigation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/bottomnavigation/BottomNavigation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as o from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as T, useId as $, useDir as B, classNames as _, dispatchEvent as g, Keys as w } from \"@progress/kendo-react-common\";\nimport { POSITION_MODE_CLASSES as L, ITEM_FLOW_CLASSES as P } from \"./models/utils.mjs\";\nimport { BottomNavigationItem as A } from \"./BottomNavigationItem.mjs\";\nconst I = o.forwardRef((r, E) => {\n  const i = o.useRef(null), b = o.useCallback(() => {\n    i.current && T(i.current);\n  }, []), c = o.useCallback(\n    () => ({\n      element: i.current,\n      focus: b\n    }),\n    [b]\n  );\n  o.useImperativeHandle(E, c);\n  const {\n    positionMode: v = s.positionMode,\n    itemFlow: C = s.itemFlow,\n    border: y = s.border,\n    className: M,\n    items: a,\n    item: N,\n    itemRender: O,\n    disabled: m,\n    style: p,\n    id: h,\n    onSelect: f,\n    onKeyDown: k\n  } = r, S = $(), F = B(i, r.dir), n = o.useMemo(() => r.fillMode === null || r.fill === null ? null : r.fill || r.fillMode || s.fillMode, [r.fillMode, r.fill]), u = o.useMemo(() => r.themeColor || s.themeColor, [r.themeColor]), D = o.useMemo(\n    () => _(\n      \"k-bottom-nav\",\n      L[v],\n      P[C],\n      {\n        [`k-bottom-nav-${n}`]: n,\n        [`k-bottom-nav-${n}-${u}`]: !!(n && u),\n        \"k-bottom-nav-border\": y,\n        \"k-disabled\": m\n      },\n      M\n    ),\n    [v, u, n, C, y, m, M]\n  ), d = o.useCallback(\n    (e, l) => {\n      a && !a[l].disabled && f && g(f, e, c(), {\n        itemTarget: a[l],\n        itemIndex: l\n      });\n    },\n    [a, f]\n  ), R = o.useCallback(\n    (e, l) => {\n      d(e, l);\n    },\n    [d]\n  ), K = o.useCallback(\n    (e, l) => {\n      switch (e.keyCode) {\n        case w.enter:\n        case w.space:\n          d(e, l), e.preventDefault();\n          break;\n      }\n      g(\n        k,\n        e,\n        c(),\n        void 0\n      );\n    },\n    [d, k]\n  );\n  return /* @__PURE__ */ o.createElement(\"nav\", { ref: i, className: D, style: p, id: h || S, dir: F }, a && a.map((e, l) => /* @__PURE__ */ o.createElement(\n    A,\n    {\n      ...e,\n      key: l,\n      index: l,\n      id: `${h || S}-${l}`,\n      disabled: m || e.disabled,\n      selected: e.selected,\n      dataItem: e,\n      item: N,\n      render: O,\n      onSelect: R,\n      onKeyDown: K\n    }\n  )));\n}), s = {\n  themeColor: \"primary\",\n  fillMode: \"flat\",\n  itemFlow: \"vertical\",\n  positionMode: \"fixed\",\n  border: !0\n};\nI.propTypes = {\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  dir: t.string,\n  themeColor: t.oneOf([\n    \"primary\",\n    \"secondary\",\n    \"tertiary\",\n    \"info\",\n    \"success\",\n    \"warning\",\n    \"error\",\n    \"dark\",\n    \"light\",\n    \"inverse\"\n  ]),\n  fill: t.oneOf([\"solid\", \"flat\"]),\n  fillMode: t.oneOf([\"solid\", \"flat\"]),\n  itemFlow: t.oneOf([\"vertical\", \"horizontal\"]),\n  border: t.bool,\n  disabled: t.bool,\n  selected: t.number,\n  onSelect: t.func\n};\nI.displayName = \"KendoReactBottomNavigation\";\nexport {\n  I as BottomNavigation\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,wBAAwB,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,EAAEC,MAAM,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,aAAa,IAAIC,CAAC,EAAEC,IAAI,IAAIC,CAAC,QAAQ,8BAA8B;AACrJ,SAASC,qBAAqB,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,QAAQ,oBAAoB;AACvF,SAASC,oBAAoB,IAAIC,CAAC,QAAQ,4BAA4B;AACtE,MAAMC,CAAC,GAAGpB,CAAC,CAACqB,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC/B,MAAMC,CAAC,GAAGxB,CAAC,CAACyB,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAG1B,CAAC,CAAC2B,WAAW,CAAC,MAAM;QAChDH,CAAC,CAACI,OAAO,IAAIzB,CAAC,CAACqB,CAAC,CAACI,OAAO,CAAC;MAC3B,CAAC,EAAE,EAAE,CAAC;MAAEC,CAAC,GAAG7B,CAAC,CAAC2B,WAAW,CACvB,OAAO;QACLG,OAAO,EAAEN,CAAC,CAACI,OAAO;QAClBG,KAAK,EAAEL;MACT,CAAC,CAAC,EACF,CAACA,CAAC,CACJ,CAAC;IACD1B,CAAC,CAACgC,mBAAmB,CAACT,CAAC,EAAEM,CAAC,CAAC;IAC3B,MAAM;QACJI,YAAY,EAAEC,CAAC,GAAGC,CAAC,CAACF,YAAY;QAChCG,QAAQ,EAAEC,CAAC,GAAGF,CAAC,CAACC,QAAQ;QACxBE,MAAM,EAAEC,CAAC,GAAGJ,CAAC,CAACG,MAAM;QACpBE,SAAS,EAAEC,CAAC;QACZC,KAAK,EAAEC,CAAC;QACRC,IAAI,EAAEC,CAAC;QACPC,UAAU,EAAEC,CAAC;QACbC,QAAQ,EAAEC,CAAC;QACXC,KAAK,EAAEC,CAAC;QACRC,EAAE,EAAEC,CAAC;QACLC,QAAQ,EAAEC,CAAC;QACXC,SAAS,EAAEC;MACb,CAAC,GAAGnC,CAAC;MAAEoC,CAAC,GAAGrD,CAAC,CAAC,CAAC;MAAEsD,CAAC,GAAGpD,CAAC,CAACiB,CAAC,EAAEF,CAAC,CAACsC,GAAG,CAAC;MAAEC,CAAC,GAAG7D,CAAC,CAAC8D,OAAO,CAAC,MAAMxC,CAAC,CAACyC,QAAQ,KAAK,IAAI,IAAIzC,CAAC,CAAC0C,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG1C,CAAC,CAAC0C,IAAI,IAAI1C,CAAC,CAACyC,QAAQ,IAAI5B,CAAC,CAAC4B,QAAQ,EAAE,CAACzC,CAAC,CAACyC,QAAQ,EAAEzC,CAAC,CAAC0C,IAAI,CAAC,CAAC;MAAEC,CAAC,GAAGjE,CAAC,CAAC8D,OAAO,CAAC,MAAMxC,CAAC,CAAC4C,UAAU,IAAI/B,CAAC,CAAC+B,UAAU,EAAE,CAAC5C,CAAC,CAAC4C,UAAU,CAAC,CAAC;MAAEC,CAAC,GAAGnE,CAAC,CAAC8D,OAAO,CAC9O,MAAMrD,CAAC,CACL,cAAc,EACdM,CAAC,CAACmB,CAAC,CAAC,EACJjB,CAAC,CAACoB,CAAC,CAAC,EACJ;QACE,CAAC,gBAAgBwB,CAAC,EAAE,GAAGA,CAAC;QACxB,CAAC,gBAAgBA,CAAC,IAAII,CAAC,EAAE,GAAG,CAAC,EAAEJ,CAAC,IAAII,CAAC,CAAC;QACtC,qBAAqB,EAAE1B,CAAC;QACxB,YAAY,EAAEU;MAChB,CAAC,EACDR,CACF,CAAC,EACD,CAACP,CAAC,EAAE+B,CAAC,EAAEJ,CAAC,EAAExB,CAAC,EAAEE,CAAC,EAAEU,CAAC,EAAER,CAAC,CACtB,CAAC;MAAE2B,CAAC,GAAGpE,CAAC,CAAC2B,WAAW,CAClB,CAAC0C,CAAC,EAAEC,CAAC,KAAK;QACR3B,CAAC,IAAI,CAACA,CAAC,CAAC2B,CAAC,CAAC,CAACtB,QAAQ,IAAIO,CAAC,IAAI5C,CAAC,CAAC4C,CAAC,EAAEc,CAAC,EAAExC,CAAC,CAAC,CAAC,EAAE;UACvC0C,UAAU,EAAE5B,CAAC,CAAC2B,CAAC,CAAC;UAChBE,SAAS,EAAEF;QACb,CAAC,CAAC;MACJ,CAAC,EACD,CAAC3B,CAAC,EAAEY,CAAC,CACP,CAAC;MAAEkB,CAAC,GAAGzE,CAAC,CAAC2B,WAAW,CAClB,CAAC0C,CAAC,EAAEC,CAAC,KAAK;QACRF,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC;MACT,CAAC,EACD,CAACF,CAAC,CACJ,CAAC;MAAEM,CAAC,GAAG1E,CAAC,CAAC2B,WAAW,CAClB,CAAC0C,CAAC,EAAEC,CAAC,KAAK;QACR,QAAQD,CAAC,CAACM,OAAO;UACf,KAAK9D,CAAC,CAAC+D,KAAK;UACZ,KAAK/D,CAAC,CAACgE,KAAK;YACVT,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAED,CAAC,CAACS,cAAc,CAAC,CAAC;YAC3B;QACJ;QACAnE,CAAC,CACC8C,CAAC,EACDY,CAAC,EACDxC,CAAC,CAAC,CAAC,EACH,KAAK,CACP,CAAC;MACH,CAAC,EACD,CAACuC,CAAC,EAAEX,CAAC,CACP,CAAC;IACD,OAAO,eAAgBzD,CAAC,CAAC+E,aAAa,CAAC,KAAK,EAAE;MAAEC,GAAG,EAAExD,CAAC;MAAEgB,SAAS,EAAE2B,CAAC;MAAEjB,KAAK,EAAEC,CAAC;MAAEC,EAAE,EAAEC,CAAC,IAAIK,CAAC;MAAEE,GAAG,EAAED;IAAE,CAAC,EAAEhB,CAAC,IAAIA,CAAC,CAACsC,GAAG,CAAC,CAACZ,CAAC,EAAEC,CAAC,KAAK,eAAgBtE,CAAC,CAAC+E,aAAa,CACxJ5D,CAAC,EACD;MACE,GAAGkD,CAAC;MACJa,GAAG,EAAEZ,CAAC;MACNa,KAAK,EAAEb,CAAC;MACRlB,EAAE,EAAE,GAAGC,CAAC,IAAIK,CAAC,IAAIY,CAAC,EAAE;MACpBtB,QAAQ,EAAEC,CAAC,IAAIoB,CAAC,CAACrB,QAAQ;MACzBoC,QAAQ,EAAEf,CAAC,CAACe,QAAQ;MACpBC,QAAQ,EAAEhB,CAAC;MACXzB,IAAI,EAAEC,CAAC;MACPyC,MAAM,EAAEvC,CAAC;MACTO,QAAQ,EAAEmB,CAAC;MACXjB,SAAS,EAAEkB;IACb,CACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAAEvC,CAAC,GAAG;IACN+B,UAAU,EAAE,SAAS;IACrBH,QAAQ,EAAE,MAAM;IAChB3B,QAAQ,EAAE,UAAU;IACpBH,YAAY,EAAE,OAAO;IACrBK,MAAM,EAAE,CAAC;EACX,CAAC;AACDlB,CAAC,CAACmE,SAAS,GAAG;EACZ/C,SAAS,EAAEvC,CAAC,CAACuF,MAAM;EACnBtC,KAAK,EAAEjD,CAAC,CAACwF,MAAM;EACfrC,EAAE,EAAEnD,CAAC,CAACuF,MAAM;EACZ5B,GAAG,EAAE3D,CAAC,CAACuF,MAAM;EACbtB,UAAU,EAAEjE,CAAC,CAACyF,KAAK,CAAC,CAClB,SAAS,EACT,WAAW,EACX,UAAU,EACV,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,CACV,CAAC;EACF1B,IAAI,EAAE/D,CAAC,CAACyF,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAChC3B,QAAQ,EAAE9D,CAAC,CAACyF,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EACpCtD,QAAQ,EAAEnC,CAAC,CAACyF,KAAK,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;EAC7CpD,MAAM,EAAErC,CAAC,CAAC0F,IAAI;EACd3C,QAAQ,EAAE/C,CAAC,CAAC0F,IAAI;EAChBP,QAAQ,EAAEnF,CAAC,CAAC2F,MAAM;EAClBtC,QAAQ,EAAErD,CAAC,CAAC4F;AACd,CAAC;AACDzE,CAAC,CAAC0E,WAAW,GAAG,4BAA4B;AAC5C,SACE1E,CAAC,IAAI2E,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}