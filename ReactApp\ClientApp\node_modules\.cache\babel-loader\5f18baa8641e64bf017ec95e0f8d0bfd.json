{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\PortalControls\\\\index.tsx\";\nimport React from 'react';\nimport { FaUser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';\nimport styles from './index.module.less';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortalControls = props => {\n  const handleCardClick = cardId => {\n    console.log(`Clicked on ${cardId}`);\n    // TODO: Implement navigation logic\n  };\n  const portalControlCards = [{\n    id: 'user-management',\n    title: 'User Management',\n    description: 'Manage portal users and assign portal roles',\n    icon: /*#__PURE__*/_jsxDEV(FaUser, {\n      className: styles.cardIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this),\n    onClick: () => handleCardClick('user-management')\n  }, {\n    id: 'security-groups',\n    title: 'Security Groups',\n    description: 'Create and manage permission-based user groups for file access',\n    icon: /*#__PURE__*/_jsxDEV(FaLock, {\n      className: styles.cardIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this),\n    onClick: () => handleCardClick('security-groups')\n  }, {\n    id: 'permissions',\n    title: 'Permissions',\n    description: 'Set security options for security groups',\n    icon: /*#__PURE__*/_jsxDEV(FaCog, {\n      className: styles.cardIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this),\n    onClick: () => handleCardClick('permissions')\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.portalControlsContainer,\n    children: portalControlCards.map(card => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.portalControlCard,\n      onClick: card.onClick,\n      role: \"button\",\n      tabIndex: 0,\n      onKeyDown: e => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          card.onClick();\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.cardContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.cardIconContainer,\n          children: card.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.cardTextContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: styles.cardTitle,\n            children: card.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: styles.cardDescription,\n            children: card.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.cardArrowContainer,\n          children: /*#__PURE__*/_jsxDEV(FaChevronRight, {\n            className: styles.cardArrow\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this)\n    }, card.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = PortalControls;\nexport default PortalControls;\nvar _c;\n$RefreshReg$(_c, \"PortalControls\");", "map": {"version": 3, "names": ["React", "FaUser", "FaLock", "FaCog", "FaChevronRight", "styles", "jsxDEV", "_jsxDEV", "PortalControls", "props", "handleCardClick", "cardId", "console", "log", "portalControlCards", "id", "title", "description", "icon", "className", "cardIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "portalControlsContainer", "children", "map", "card", "portalControlCard", "role", "tabIndex", "onKeyDown", "e", "key", "cardContent", "cardIconContainer", "cardTextContainer", "cardTitle", "cardDescription", "cardArrowContainer", "cardArrow", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/pages/PortalControls/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>og, FaChevronRight } from 'react-icons/fa';\r\nimport styles from './index.module.less';\r\n\r\ninterface PortalControlCard {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  onClick: () => void;\r\n}\r\n\r\nconst PortalControls = (props: any) => {\r\n  const handleCardClick = (cardId: string) => {\r\n    console.log(`Clicked on ${cardId}`);\r\n    // TODO: Implement navigation logic\r\n  };\r\n\r\n  const portalControlCards: PortalControlCard[] = [\r\n    {\r\n      id: 'user-management',\r\n      title: 'User Management',\r\n      description: 'Manage portal users and assign portal roles',\r\n      icon: <FaUser className={styles.cardIcon} />,\r\n      onClick: () => handleCardClick('user-management')\r\n    },\r\n    {\r\n      id: 'security-groups',\r\n      title: 'Security Groups',\r\n      description: 'Create and manage permission-based user groups for file access',\r\n      icon: <FaLock className={styles.cardIcon} />,\r\n      onClick: () => handleCardClick('security-groups')\r\n    },\r\n    {\r\n      id: 'permissions',\r\n      title: 'Permissions',\r\n      description: 'Set security options for security groups',\r\n      icon: <FaCog className={styles.cardIcon} />,\r\n      onClick: () => handleCardClick('permissions')\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className={styles.portalControlsContainer}>\r\n      {portalControlCards.map((card) => (\r\n        <div\r\n          key={card.id}\r\n          className={styles.portalControlCard}\r\n          onClick={card.onClick}\r\n          role=\"button\"\r\n          tabIndex={0}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' || e.key === ' ') {\r\n              card.onClick();\r\n            }\r\n          }}\r\n        >\r\n          <div className={styles.cardContent}>\r\n            <div className={styles.cardIconContainer}>\r\n              {card.icon}\r\n            </div>\r\n            <div className={styles.cardTextContainer}>\r\n              <h3 className={styles.cardTitle}>{card.title}</h3>\r\n              <p className={styles.cardDescription}>{card.description}</p>\r\n            </div>\r\n            <div className={styles.cardArrowContainer}>\r\n              <FaChevronRight className={styles.cardArrow} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortalControls;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,cAAc,QAAQ,gBAAgB;AACtE,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUzC,MAAMC,cAAc,GAAIC,KAAU,IAAK;EACrC,MAAMC,eAAe,GAAIC,MAAc,IAAK;IAC1CC,OAAO,CAACC,GAAG,CAAC,cAAcF,MAAM,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAMG,kBAAuC,GAAG,CAC9C;IACEC,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6CAA6C;IAC1DC,IAAI,eAAEX,OAAA,CAACN,MAAM;MAACkB,SAAS,EAAEd,MAAM,CAACe;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAAC,iBAAiB;EAClD,CAAC,EACD;IACEK,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,gEAAgE;IAC7EC,IAAI,eAAEX,OAAA,CAACL,MAAM;MAACiB,SAAS,EAAEd,MAAM,CAACe;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAAC,iBAAiB;EAClD,CAAC,EACD;IACEK,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,eAAEX,OAAA,CAACJ,KAAK;MAACgB,SAAS,EAAEd,MAAM,CAACe;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAAC,aAAa;EAC9C,CAAC,CACF;EAED,oBACEH,OAAA;IAAKY,SAAS,EAAEd,MAAM,CAACqB,uBAAwB;IAAAC,QAAA,EAC5Cb,kBAAkB,CAACc,GAAG,CAAEC,IAAI,iBAC3BtB,OAAA;MAEEY,SAAS,EAAEd,MAAM,CAACyB,iBAAkB;MACpCL,OAAO,EAAEI,IAAI,CAACJ,OAAQ;MACtBM,IAAI,EAAC,QAAQ;MACbC,QAAQ,EAAE,CAAE;MACZC,SAAS,EAAGC,CAAC,IAAK;QAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;UACtCN,IAAI,CAACJ,OAAO,CAAC,CAAC;QAChB;MACF,CAAE;MAAAE,QAAA,eAEFpB,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAAC+B,WAAY;QAAAT,QAAA,gBACjCpB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACgC,iBAAkB;UAAAV,QAAA,EACtCE,IAAI,CAACX;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNjB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACiC,iBAAkB;UAAAX,QAAA,gBACvCpB,OAAA;YAAIY,SAAS,EAAEd,MAAM,CAACkC,SAAU;YAAAZ,QAAA,EAAEE,IAAI,CAACb;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClDjB,OAAA;YAAGY,SAAS,EAAEd,MAAM,CAACmC,eAAgB;YAAAb,QAAA,EAAEE,IAAI,CAACZ;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNjB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACoC,kBAAmB;UAAAd,QAAA,eACxCpB,OAAA,CAACH,cAAc;YAACe,SAAS,EAAEd,MAAM,CAACqC;UAAU;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAtBDK,IAAI,CAACd,EAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuBT,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACmB,EAAA,GA7DInC,cAAc;AA+DpB,eAAeA,cAAc;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}