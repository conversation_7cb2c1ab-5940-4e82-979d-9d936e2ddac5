import { selectUploaderState, setSucceededFiles } from '@app/components/FileUploader/uploaderSlice';
import { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';
import { selectAppState } from '@app/appSlice';
import { Spin } from 'antd';
import { Store } from 'antd/lib/form/interface';
import React, { useEffect } from 'react';
import type {} from 'redux-thunk/extend-redux';
import { UploaderSubmit } from '.';
import { FileRecord, UploaderSubmitContainerProps } from './types';

const UploaderSubmitContainer: React.FC<UploaderSubmitContainerProps> = ({
  fileList,
  siteId,
  form,
  fileEvents,
  urlEvents,
  disabledForm,
  forManageFiles,
  onFormChange,
  onFinished,
}) => {
  const dispatch = useAppDispatch();
  const { fileAreaSettings } = useAppSelector(selectAppState);
  const { pendingSave, succeededFiles } = useAppSelector(selectUploaderState);

  useEffect(() => {
    if (succeededFiles && !!succeededFiles.length) {
      fileEvents?.onSaveSuccess(succeededFiles);
      dispatch(setSucceededFiles([]));
    }
  }, [dispatch, fileEvents, succeededFiles]);

  const onFinish = (values: Store, inputFileList: FileRecord[]) => {
    const { newTags, tags, ...rest } = values;
    onFinished({
      ...rest,
      siteId,
      files: transformFileList(inputFileList),
    });
  };

  return (
    <Spin spinning={pendingSave}>
      <UploaderSubmit
        siteId={siteId}
        permissions={fileAreaSettings}
        fileList={fileList}
        fileEvents={fileEvents}
        urlEvents={urlEvents}
        form={form}
        onFinish={onFinish}
        disabledForm={disabledForm}
        forManageFiles={forManageFiles}
        onFormChange={onFormChange}
      />
    </Spin>
  );
};

const transformFileList = (fileList: FileRecord[]) => {
  return fileList
    .filter((v) => v.checked)
    .map(({ title, referenceNumber }) => {
      return { title, referenceNumber };
    });
};

export default UploaderSubmitContainer;
