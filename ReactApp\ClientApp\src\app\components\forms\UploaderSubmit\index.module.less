@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjMoverArrow {
  align-items: center;
  display: flex;
  justify-content: center;
}

.yjResetMargin {
  margin-bottom: 0;
}

.yjInputNewTags {
  display: inline-block;
  padding-right: 5px;
}

.yjButtonNewTags {
  float: right;
  margin-left: 5px;
  margin-top: 30px;
  width: 95%;
}

.yjSecondaryGridFilearea {
  margin-bottom: 25px;

  table {

    th {
      background: @color-bg-secondary-header;
      color: @color-font-secondary-header;
      font-size: @font-size-base / 1.145;
      height: 22px;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-semibold);
    }
  }

  button {
    background: @color-bg-remove-record-icon;
    border: none;
    box-shadow: none;
    height: 20px;
    margin-right: 10px;
    width: 20px;

    &:hover {
      background: @color-bg-remove-record-icon;
      border: none;
      box-shadow: none;
    }

    &:focus {
      background: @color-bg-remove-record-icon;
      border: none;
      box-shadow: none;
    }

    span {
      color: @color-font-remove-record-icon;
      font-size: 14px;
    }
  }
}

.yjTxtNewTags {

  div:first-child {
    cursor: default;
  }
}

.yjUploaderRetentionSection {
  margin-bottom: 24px;
}

.yjUploaderRetentionDate {
  background: @color-bg;
  border: 1px solid @border-color-base;
  border-radius: 2px;
  padding: 3px 10px;
}

.yjModuleSubHeading {
  color: @color-font-form-label;
  font-size: @font-size-base / 1.145;
  height: 22px;
  text-transform: @yj-transform;

  .font-mixin(@font-primary, @yjff-semibold);
}

// Delete record file - grid (file area)

.yjDeteleFile {
  background: @color-bg-remove-record-icon;
  border: none;
  box-shadow: none;
  height: 20px;
  margin-right: 10px;
  width: 20px;

  &:hover {
    background: @color-bg-remove-record-icon;
    border: none;
    box-shadow: none;
  }

  &:focus {
    background: @color-bg-remove-record-icon;
    border: none;
    box-shadow: none;
  }

  span {
    color: @color-font-remove-record-icon;
    font-size: 14px;
    vertical-align: 3px;
  }
}