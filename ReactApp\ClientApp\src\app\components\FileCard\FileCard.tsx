import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Checkbox, Col, Collapse, Row } from 'antd';
import React from 'react';
import { FileCardProps } from './types';

const { Panel } = Collapse;

const FileCard = (props: FileCardProps) => {
  const generateHeader = () => {
    return (
      <Row style={{ width: '100%' }} key={props.fileId}>
        <Col span={16}>
          <Row gutter={10}>
            <Col>
              <Checkbox
                type="checkbox"
                className={'YJ_CP_FILECARD_CHECKBOX'}
                checked={props.isSelected}
                onClick={(e) => e.stopPropagation()}
                onChange={(e) => {
                  props.onSelectChange && props.onSelectChange(e.target.checked);
                }}
              />
            </Col>
            <Col className={'YJ_CP_FILECARD_TITLE'}>{props.title}</Col>
          </Row>
        </Col>
        <Col span={8}>
          <Row gutter={10} justify="space-between">
            <Col className={'YJ_CP_FILECARD_FILETYPE'} span={3}>
              {(() => {
                if (props.isActive !== undefined)
                  return props.isActive ? (
                    <div style={{ display: 'inline-block' }} className={'YJ_CP_FILECARD_ACTIVE'}></div>
                  ) : (
                    <div style={{ display: 'inline-block' }} className={'YJ_CP_FILECARD_DEACTIVE'}></div>
                  );
              })()}
              {props.fileType}
            </Col>
            <Col flex="auto">{props.trailingIcons}</Col>
          </Row>
        </Col>
        {props.note && (
          <Col span={24}>
            <div className={'yj_cp_fileCard_note'}>{props.note}</div>
          </Col>
        )}
      </Row>
    );
  };
  return (
    <Collapse expandIconPosition="end" expandIcon={(exProp) => (exProp.isActive ? <UpOutlined /> : <DownOutlined />)} className={'YJ_CP_FILECARD ' + props.className}>
      <Panel header={generateHeader()} key={props.fileId}>
        <div className={'YJ_FILECARD_CONTENT'}>{props.content}</div>
      </Panel>
    </Collapse>
  );
};
export default FileCard;
