import logger from '@app/utils/logger';
import { useState } from 'react';

const UseLocalStorage = (key: string, initialValue: any) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      logger.error('YJ Client Portal', 'localStorage', error);
      return initialValue;
    }
  });

  const setValue = (value: any) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      setStoredValue(valueToStore);

      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      logger.error('YJ Client Portal', 'localStorage', error);
    }
  };

  return [storedValue, setValue];
};

export default UseLocalStorage;
