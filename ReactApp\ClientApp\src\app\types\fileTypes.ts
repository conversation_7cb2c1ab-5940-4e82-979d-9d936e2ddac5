export type FileType = {
  fileId: string;
  title: string;
  type: string;
};

export type PublishedFileType = FileType & {
  year: number;
  publishedAt: string;
  publishedBy: { value: string; name: string };
  size: string;
};

export type SubmittedFilesType = {
  id: string;
  title: string;
  type: string;
  deletedAt: string;
  uploadedAt: string;
  acceptedAt: string;
  status: { value: number; name: string };
  acceptedBy: { value: string; name: string };
  deletedBy: { value: string; name: string };
  deletionNote: string;
  size: string;
};
