{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { POPUP_SETTINGS_RTL as d, POPUP_SETTINGS as e } from \"../consts.mjs\";\nimport { isIdZeroLevel as w } from \"./itemsIdsUtils.mjs\";\nfunction f(r, n, t) {\n  const o = i(r, n, t);\n  return t ? o === \"downward\" ? d.downward : d.leftward : o === \"downward\" ? e.downward : e.rightward;\n}\nfunction i(r, n, t) {\n  return w(r) ? n ? t ? \"leftward\" : \"rightward\" : \"downward\" : t ? \"leftward\" : \"rightward\";\n}\nfunction l(r) {\n  return r ? \"rtl\" : \"ltr\";\n}\nfunction P(r, n) {\n  return `${r}_${n}`;\n}\nexport { l as convertBoolDirectionToString, i as getChildrenPosition, P as getDOMElementId, f as getPopupSettings };", "map": {"version": 3, "names": ["POPUP_SETTINGS_RTL", "d", "POPUP_SETTINGS", "e", "isIdZeroLevel", "w", "f", "r", "n", "t", "o", "i", "downward", "leftward", "rightward", "l", "P", "convertBoolDirectionToString", "getChildrenPosition", "getDOMElementId", "getPopupSettings"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/misc.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { POPUP_SETTINGS_RTL as d, POPUP_SETTINGS as e } from \"../consts.mjs\";\nimport { isIdZeroLevel as w } from \"./itemsIdsUtils.mjs\";\nfunction f(r, n, t) {\n  const o = i(r, n, t);\n  return t ? o === \"downward\" ? d.downward : d.leftward : o === \"downward\" ? e.downward : e.rightward;\n}\nfunction i(r, n, t) {\n  return w(r) ? n ? t ? \"leftward\" : \"rightward\" : \"downward\" : t ? \"leftward\" : \"rightward\";\n}\nfunction l(r) {\n  return r ? \"rtl\" : \"ltr\";\n}\nfunction P(r, n) {\n  return `${r}_${n}`;\n}\nexport {\n  l as convertBoolDirectionToString,\n  i as getChildrenPosition,\n  P as getDOMElementId,\n  f as getPopupSettings\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,QAAQ,eAAe;AAC5E,SAASC,aAAa,IAAIC,CAAC,QAAQ,qBAAqB;AACxD,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAMC,CAAC,GAAGC,CAAC,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACpB,OAAOA,CAAC,GAAGC,CAAC,KAAK,UAAU,GAAGT,CAAC,CAACW,QAAQ,GAAGX,CAAC,CAACY,QAAQ,GAAGH,CAAC,KAAK,UAAU,GAAGP,CAAC,CAACS,QAAQ,GAAGT,CAAC,CAACW,SAAS;AACrG;AACA,SAASH,CAACA,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAOJ,CAAC,CAACE,CAAC,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAG,UAAU,GAAG,WAAW,GAAG,UAAU,GAAGA,CAAC,GAAG,UAAU,GAAG,WAAW;AAC5F;AACA,SAASM,CAACA,CAACR,CAAC,EAAE;EACZ,OAAOA,CAAC,GAAG,KAAK,GAAG,KAAK;AAC1B;AACA,SAASS,CAACA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACf,OAAO,GAAGD,CAAC,IAAIC,CAAC,EAAE;AACpB;AACA,SACEO,CAAC,IAAIE,4BAA4B,EACjCN,CAAC,IAAIO,mBAAmB,EACxBF,CAAC,IAAIG,eAAe,EACpBb,CAAC,IAAIc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}