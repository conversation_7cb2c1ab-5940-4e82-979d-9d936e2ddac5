import { OperationalServiceTypes } from '@iris/discovery.fe.client';

type AppConfig = {
  logLevel: string;
  discoveryURL: string;
  api: {
    [OperationalServiceTypes.UserService]: {
      userNavigationMenu: string;
      userPermissions: string;
      userPreference: string;
    };
    [OperationalServiceTypes.FileManagementService]: {
      getPublishedFilesColumns: string;
      getPublishedFiles: string;
      getPublishedFilesCount: string;
      downloadFile: string;
      downloadZipFile: string;
    };
    [OperationalServiceTypes.PortalService]: {
      getSubmittedFiles: string;
      portalFilesUpload: string;
      downloadPortalZipFile: string;
      downloadPortalFile: string;
      siteSearch: string;
      softDeleteFile:string;
    };
    [OperationalServiceTypes.FileStorageService]: {
      uploadFiles: string;
    };
    [OperationalServiceTypes.MasterDataService]: {
      themes: string;
    };
    [OperationalServiceTypes.Discovery]: {
      getTenantContexts: string;
    };
  };
  inputDebounceInterval: number;
  dateFormat: string;
  idleTime: number;
  clientId: string;
};

export default AppConfig;
