@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';
@file-path: '../../../../styles/';

.yj_cp_dragndrop_wrapper {

  .yj_cp_dragndrop {
    margin-bottom: 20px;
    background-color: #E9EAEB;
    border-radius: 20px;
    width: 100%;

    .yjDragDropText {
      color: @color-font-drag-drop-container;
      font-size: @font-size-base/1.2;
      line-height: 2;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-semibold);
    }

    .yjUploadBrowseButton {
      background: @color-bg-browse-btn;
      border-radius: 10px;
      color: @color-font-browse-btn;
      height: 28px;
      margin: 5px 0;
      padding: 0 25px;
    }

    .yjUploadLinkButton {
      border-radius: 5px;
      cursor: pointer;
      font-size: @font-size-base/1.2;
      margin: 5px 0;
      text-decoration: @yj-underline;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-bold);
    }
  }
}

.yjMoverArrow {
  align-items: center;
  display: flex;
  justify-content: center;
}

.seperator {
  border-top: 1px solid @color-border;
  padding: 15px;
}

.yjClose {
  background: none;
  font-weight: bold;
  border: none;
  box-shadow: none;
  color: #007BA0;
  float: right;
  position: absolute;
  right: 5px;
  top: 5px;

  &:active,
  &:focus,
  &:hover,
  &:visited {
    background: none;
  }
}

.yjBtnMiniFileUploader {
  background-color: #50A76E;
  color: white;
  border-radius: 5px;
}

@media (max-width: @breakpoint-tablet ) {

  .yjDragDrop {
    box-sizing: content-box;
    height: 24vh;
  }
}