{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nconst p = (s, m) => {\n  const t = n.useRef(0),\n    i = n.useRef(!1),\n    r = n.useRef(null),\n    d = e => {\n      const l = e.duration;\n      let o, u;\n      const w = t.current && 1 - t.current;\n      e.onStart && e.onStart();\n      const a = f => {\n        o || (o = f), u = f - o + 1;\n        const c = u / l + w;\n        c <= 1 ? (e.onUpdate && e.onUpdate(c), r.current = window.requestAnimationFrame(a), t.current = c) : (e.onEnd && e.onEnd(1), t.current = 0);\n      };\n      r.current = window.requestAnimationFrame(a);\n    };\n  n.useEffect(() => (d(s), () => {\n    r.current && window.cancelAnimationFrame(r.current);\n  }), m), n.useEffect(() => {\n    i.current = !0;\n  }, []);\n};\nexport { p as useAnimation };", "map": {"version": 3, "names": ["n", "p", "s", "m", "t", "useRef", "i", "r", "d", "e", "l", "duration", "o", "u", "w", "current", "onStart", "a", "f", "c", "onUpdate", "window", "requestAnimationFrame", "onEnd", "useEffect", "cancelAnimationFrame", "useAnimation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/hooks/useAnimation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nconst p = (s, m) => {\n  const t = n.useRef(0), i = n.useRef(!1), r = n.useRef(null), d = (e) => {\n    const l = e.duration;\n    let o, u;\n    const w = t.current && 1 - t.current;\n    e.onStart && e.onStart();\n    const a = (f) => {\n      o || (o = f), u = f - o + 1;\n      const c = u / l + w;\n      c <= 1 ? (e.onUpdate && e.onUpdate(c), r.current = window.requestAnimationFrame(a), t.current = c) : (e.onEnd && e.onEnd(1), t.current = 0);\n    };\n    r.current = window.requestAnimationFrame(a);\n  };\n  n.useEffect(() => (d(s), () => {\n    r.current && window.cancelAnimationFrame(r.current);\n  }), m), n.useEffect(() => {\n    i.current = !0;\n  }, []);\n};\nexport {\n  p as useAnimation\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAClB,MAAMC,CAAC,GAAGJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;IAAEC,CAAC,GAAGN,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;IAAEE,CAAC,GAAGP,CAAC,CAACK,MAAM,CAAC,IAAI,CAAC;IAAEG,CAAC,GAAIC,CAAC,IAAK;MACtE,MAAMC,CAAC,GAAGD,CAAC,CAACE,QAAQ;MACpB,IAAIC,CAAC,EAAEC,CAAC;MACR,MAAMC,CAAC,GAAGV,CAAC,CAACW,OAAO,IAAI,CAAC,GAAGX,CAAC,CAACW,OAAO;MACpCN,CAAC,CAACO,OAAO,IAAIP,CAAC,CAACO,OAAO,CAAC,CAAC;MACxB,MAAMC,CAAC,GAAIC,CAAC,IAAK;QACfN,CAAC,KAAKA,CAAC,GAAGM,CAAC,CAAC,EAAEL,CAAC,GAAGK,CAAC,GAAGN,CAAC,GAAG,CAAC;QAC3B,MAAMO,CAAC,GAAGN,CAAC,GAAGH,CAAC,GAAGI,CAAC;QACnBK,CAAC,IAAI,CAAC,IAAIV,CAAC,CAACW,QAAQ,IAAIX,CAAC,CAACW,QAAQ,CAACD,CAAC,CAAC,EAAEZ,CAAC,CAACQ,OAAO,GAAGM,MAAM,CAACC,qBAAqB,CAACL,CAAC,CAAC,EAAEb,CAAC,CAACW,OAAO,GAAGI,CAAC,KAAKV,CAAC,CAACc,KAAK,IAAId,CAAC,CAACc,KAAK,CAAC,CAAC,CAAC,EAAEnB,CAAC,CAACW,OAAO,GAAG,CAAC,CAAC;MAC7I,CAAC;MACDR,CAAC,CAACQ,OAAO,GAAGM,MAAM,CAACC,qBAAqB,CAACL,CAAC,CAAC;IAC7C,CAAC;EACDjB,CAAC,CAACwB,SAAS,CAAC,OAAOhB,CAAC,CAACN,CAAC,CAAC,EAAE,MAAM;IAC7BK,CAAC,CAACQ,OAAO,IAAIM,MAAM,CAACI,oBAAoB,CAAClB,CAAC,CAACQ,OAAO,CAAC;EACrD,CAAC,CAAC,EAAEZ,CAAC,CAAC,EAAEH,CAAC,CAACwB,SAAS,CAAC,MAAM;IACxBlB,CAAC,CAACS,OAAO,GAAG,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,SACEd,CAAC,IAAIyB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}