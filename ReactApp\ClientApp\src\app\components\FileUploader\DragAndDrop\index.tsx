import { DownloadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import { DraggerProps } from 'antd/lib/upload';
import React, { useEffect, useState } from 'react';
import { DragAndDropProps } from '../types';
import styles from './index.module.less';

const { Dragger } = Upload;

const DragAndDrop = ({ ...props }: DraggerProps & DragAndDropProps) => {
  const [browserVisibility, setBrowserVisibility] = useState(false);
  const [enableBrowse, setEnableBrowse] = useState(false);
  const [uploaderShrunkView, setUploaderShrunkView] = useState(true);

  const enableBrowserVisibility = () => {
    setBrowserVisibility(true);
    setEnableBrowse(true);
  };

  useEffect(() => {
    if (browserVisibility) {
      setEnableBrowse(true);
    } else {
      setEnableBrowse(false);
    }
  }, [browserVisibility]);
  return (
    <>
      <div
        className={styles.yj_cp_dragndrop_wrapper}
        onMouseOver={() => {
          setBrowserVisibility(false);
        }}
        onClick={() => {
          uploaderShrunkView && setUploaderShrunkView(false);
        }}
      >
        <Dragger openFileDialogOnClick={enableBrowse} {...props} className={styles.yj_cp_dragndrop} multiple showUploadList={false}>
          {uploaderShrunkView ? (
            <div data-testid="shrunkView">
              <Button
                data-testid="expandViewButton"
                icon={<DownloadOutlined />}
                className={styles.yjBtnMiniFileUploader}
                onClick={() => {
                  setUploaderShrunkView(false);
                }}
              >
                UPLOAD FILES
              </Button>
            </div>
          ) : (
            <div data-testid="expandedView">
              <Button
                data-testid="expandCloseButton"
                className={styles.yjClose}
                onClick={() => {
                  setUploaderShrunkView(true);
                }}
              >
                X
              </Button>

              <p className={styles.yjDragDropText}>Drag & Drop Here</p>
              <p className={styles.yjDragDropText}>or</p>
              <Button
                data-testid="fileUploadBrowseButton"
                onMouseLeave={() => {
                  setEnableBrowse(false);
                }}
                onMouseOver={() => {
                  enableBrowserVisibility();
                }}
                onClick={() => {
                  enableBrowserVisibility();
                }}
                type="primary"
                className={styles.yjUploadBrowseButton}
              >
                Browse
              </Button>
            </div>
          )}
        </Dragger>
      </div>
    </>
  );
};

export default DragAndDrop;
