name: $(Year:yy).$(DayOfYear)$(Rev:.r)
trigger:
  batch: true
  branches:
    include:
      - "*"
    exclude:
      - master
      - develop
      - release/*

resources:
  repositories:
    - repository: pipelineTemplates
      type: git
      name: MCP/AzurePipelineTemplates
      ref: refs/heads/master

variables:
  - group: GlobalVariablesLibrary
  - template: "VariableTemplates/vars-global.yml@pipelineTemplates"
  - name: isRelease
    value: $[contains(variables['System.PullRequest.TargetBranch'],'release/')]
  - name: isFromReleaseToDev
    value: and($[contains(variables['System.PullRequest.SourceBranch'],'release/')], $[contains(variables['System.PullRequest.TargetBranch'],'dev')])
  - name: Work_Directory
    value: "$(System.DefaultWorkingDirectory)"
  - name: Branch
    value: "$(Build.SourceBranchName)"

parameters:
  - name: publishContainers
    type: object
    default:
      - projectName: "dmsfeportal"
        dockerBuildFile: "ReactApp/Dockerfile"
        supportedPlatform: "Linux"
        deploymentPlatform: "Linux"
        bindingHttpPort: 80
        bindingHttpsPort: 0
        containerServiceFilter: "Dms:FEPortal"
        ingressHostName: "clientportal"
        deploymentTemplate: "Deployment.template"
        npmWorkingFile: ReactApp/ClientApp/.npmrc
        versionConfigFile: ReactApp/ClientApp/public/env-config-version.js

  - name: deploymentEnvironments
    type: object
    default:
      - environmentName: "Sandbox"
        deploymentEnvironment: ["Sandbox.sandbox"]
        virtualMachineEnvironment: []
        libraryEnvironment: "Sandbox"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isGitPush, 'true'))"

      - environmentName: "DEV"
        deploymentEnvironment: ["Dev.dev"]
        virtualMachineEnvironment: []
        libraryEnvironment: "DEV"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isPullRequest, 'true'), eq(variables.isRelease, 'false'))"

      - environmentName: "QA"
        deploymentEnvironment: ["QA.qa"]
        virtualMachineEnvironment: []
        libraryEnvironment: "QA"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: "STG"
        deploymentEnvironment: ["Staging.stg"]
        virtualMachineEnvironment: []
        libraryEnvironment: "STG"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "QA"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: 'DEV-EUS-PRE'
        deploymentEnvironment: ['DEV-EUS-PRE.pre']
        virtualMachineEnvironment: []
        libraryEnvironment: 'DEV-EUS-PRE-Portal'
        dependencyMatrix: ['ArtifactsBuild', 'ContainerBuild', 'STG']
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: "ProductionEUS"
        deploymentEnvironment: ["ProductionEUS.production"]
        virtualMachineEnvironment: []
        libraryEnvironment: "ProductionEUS-Portal"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "DEV-EUS-PRE"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: "ProductionUKS"
        deploymentEnvironment: ["ProductionUKS.production"]
        virtualMachineEnvironment: []
        libraryEnvironment: "ProductionUKS-Portal"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "DEV-EUS-PRE"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"  

stages:
  # - stage: Versioning
  #   jobs:
  #   - job: AssemblyVersioning
  #     condition: or(eq(variables.IsRelease, 'true'), eq(variables.isFromReleaseToDev, 'true'))
  #     pool:
  #       name: Azure Pipelines
  #       vmImage: 'ubuntu-latest'
  #     variables:
  #       - name: majorVersion
  #         ${{ if eq(variables.IsRelease, 'true') }}:
  #           value: $[replace(variables['System.PullRequest.TargetBranch'], 'refs/heads/release/', '')]
  #         ${{ else }}:
  #           value: $[replace(variables['System.PullRequest.SourceBranch'], 'refs/heads/release/', '')]
  #       - name: major
  #         ${{ if eq(variables.IsRelease, 'true') }}:
  #           value: '$(majorVersion)-rc'
  #         ${{ else }}:
  #           value: '$(majorVersion)-beta'
  #       - name: minor
  #         value: $[counter(variables['major'], 0)]
  #       - name: releaseVersion
  #         value: $(major).$(minor)

  #     steps:
  #       - task: Bash@3
  #         displayName: Generating Release Build Number
  #         inputs:
  #           targetType: 'inline'
  #           script: |
  #             echo "Setting build number to '$(releaseVersion)'."
  #             echo "##vso[build.updatebuildnumber]$(releaseVersion)"

  - stage: ArtifactsBuild
    jobs:
      - job: ArtifactsBuild
        variables:
          - name: CurrentBuildPlatform
            value: "linux"
          - name: solutionPath
            value: "FrontEnd-Portal.sln"
          - name: buildType
            value: "Debug"

        pool:
          name: Azure Pipelines
          vmImage: "windows-2022"
        steps:
          - checkout: self
            clean: true
            persistCredentials: true

          - task: npmAuthenticate@0
            inputs:
              workingFile: ReactApp/ClientApp/.npmrc

          - task: DotNetCoreCLI@2
            displayName: Build Solution
            inputs:
              command: 'build'
              projects: ${{ variables.solutionPath }}
              arguments: '--configuration $(buildType)'

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: "$(Build.SourcesDirectory)/AzurePipelines/ConfigurationTemplates"
              artifactName: configtemplates

          # - task: GitTag@7
          #   condition: eq(variables.isRelease, 'true')
          #   inputs:
          #     workingdir: '$(SYSTEM.DEFAULTWORKINGDIRECTORY)'
          #     tag: '$(build.buildNumber)'
          #     forceTagCreation: true

  - stage: ContainerBuild
    displayName: "Build Containers"
    dependsOn: ArtifactsBuild
    jobs:
      - job: LinuxContainerBuild
        variables:
          - name: currentBuildPlatform
            value: "linux"
          - name: projectName
            value: "dmsfeportal"

        pool:
          name: Azure Pipelines
          vmImage: "ubuntu-latest"
        steps:
          - checkout: self
            clean: true
            persistCredentials: true

          - ${{ each container in parameters.publishContainers }}:
              - task: npmAuthenticate@0
                inputs:
                  workingFile: ${{ container.npmWorkingFile }}

              - task: qetza.replacetokens.replacetokens-task.replacetokens@5
                displayName: "Replace tokens in ${{ container.versionConfigFile }}"
                inputs:
                  rootDirectory: ""
                  targetFiles: ${{ container.versionConfigFile }}
                  verbosity: detailed
                  tokenPattern: "doublebraces"
                  actionOnMissing: fail
                  actionOnNoFiles: fail
                  inlineVariables: |
                    version: $(Build.BuildNumber)

              - task: Docker@2
                displayName: "Compose and Push ${{ parameters.containerName }}"
                inputs:
                  containerRegistry: $(AzureContainerRegistry)
                  repository: "${{lower(container.projectName)}}-$(currentBuildPlatform)"
                  command: "buildAndPush"
                  Dockerfile: "${{ container.dockerBuildFile }}"
                  buildContext: "$(Build.Repository.LocalPath)"
                  tags: "$(Build.BuildNumber)"

  - ${{ each env in parameters.deploymentEnvironments }}:
      - stage: ${{replace(env.environmentName,'-','_')}}
        displayName: "${{ env.environmentName }} CD Pipeline"
        variables:
          - group: GlobalVariablesLibrary
          - group: ${{env.libraryEnvironment}}
          - template: VariableTemplates/vars-deploy-${{ env.environmentName }}.yml
          - name: CurrentEnvironment
            value: ${{env.environmentName}}

        dependsOn:
          - ${{ each dep in env.dependencyMatrix }}:
              - ${{replace(dep,'-','_')}}

        ${{ if ne(env.deploymentConditions, '') }}:
          condition: ${{ env.deploymentConditions }}

        jobs:
          - ${{ each deploy in env.deploymentEnvironment }}:
              - deployment: ${{replace(replace(deploy,'.','_'),'-','_')}}
                displayName: "Deploy Containers To ${{ deploy }}"
                pool:
                  name: Azure Pipelines
                  vmImage: "ubuntu-latest"
                environment: ${{ deploy }}

                workspace:
                  clean: all
                strategy:
                  runOnce:
                    deploy:
                      steps:
                        - checkout: none
                        - download: current
                          artifact: configtemplates
                        - ${{ each container in parameters.publishContainers }}:
                            - task: Kubernetes@1
                              displayName: "Deploy $(AksNamespace)-${{ container.projectName }} Cluster Config Maps"
                              inputs:
                                command: apply
                                useConfigurationFile: true
                                configurationType: inline
                                inline: |
                                  apiVersion: v1
                                  kind: ConfigMap
                                  metadata:
                                    name: $(AksNamespace)-${{ container.projectName }}-config
                                    namespace: $(AksNamespace)
                                  data:
                                    env-config.js: |
                                      window._env_ = {
                                        REACT_APP_DISCOVERY_URL: "https://$(DiscoveryAdvertisedAddress)",
                                        REACT_APP_LOG_LEVEL: "$(LogLevel)",
                                      };

                            - template: DeploymentTemplates/Deployment.yml
                              parameters:
                                targetContainer: "${{lower(container.projectName)}}-${{lower(container.deploymentPlatform)}}"
                                targetContainerTag: "$(Build.BuildNumber)"
                                serviceName: ${{lower(container.projectName)}}
                                numberOfContainerInstances: ${{variables.NumberofContainerInstances}}
                                advertisedAddress: "${{lower(container.ingressHostName)}}.$(DnsDomain)"
                                azureConfigurationLabel: $(AzureConfigurationLabel)
                                azureConfigurationFilter: ${{container.containerServiceFilter}}
                                aksDeploymentOs: ${{lower(container.deploymentPlatform)}}
                                aksDeploymentNamespace: "$(AksNamespace)"
                                aksIngressClass: "$(AzureIngressClass)"
                                aksIngressTlsIssuer: "$(GlobalTlsIssuer)"
                                containerPullAddress: "$(AzureContainerRegistryEndpoint)"
                                aksTemplateLocation: "$(Pipeline.Workspace)/configtemplates"
                                aksTemplateName: ${{container.deploymentTemplate}}
                                configMapName: "$(AksNamespace)-${{ container.projectName }}-config"
                                cpuRequests: ${{variables.ContainerCpuRequests}}
                                cpuLimit: ${{variables.ContainerCpuLimit}}
                                memoryRequests: ${{variables.ContainerMemoryRequests}}
                                memoryLimit: ${{variables.ContainerMemoryLimit}}
                                livenessProbeInitialDelaySeconds: ${{variables.LivenessProbeInitialDelaySeconds}}
                                readinessProbeInitialDelaySeconds: ${{variables.ReadinessProbeInitialDelaySeconds}}
                                configMapMountPath: "/app/wwwroot/env-config.js"
                                configMapMountSubPath: "env-config.js"
                                hpaMinReplicas: ${{ variables.ContainerHpaMinReplicas }}
                                hpaMaxReplicas: ${{ variables.ContainerHpaMaxReplicas }}
                                hpaMemoryAverageUtilization: ${{ variables.ContainerHpaMemoryAverageUtilization }}
                                hpaCpuAverageUtilization: ${{ variables.ContainerHpaCpuAverageUtilization }}
