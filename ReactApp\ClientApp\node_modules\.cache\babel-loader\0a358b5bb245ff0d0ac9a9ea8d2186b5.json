{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst s = \"splitBarLabel\",\n  a = {\n    [s]: \"split bar\"\n  };\nexport { a as messages, s as splitBarLabel };", "map": {"version": 3, "names": ["s", "a", "messages", "splitBarLabel"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/splitter/messages/index.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst s = \"splitBarLabel\", a = {\n  [s]: \"split bar\"\n};\nexport {\n  a as messages,\n  s as splitBarLabel\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,eAAe;EAAEC,CAAC,GAAG;IAC7B,CAACD,CAAC,GAAG;EACP,CAAC;AACD,SACEC,CAAC,IAAIC,QAAQ,EACbF,CAAC,IAAIG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}