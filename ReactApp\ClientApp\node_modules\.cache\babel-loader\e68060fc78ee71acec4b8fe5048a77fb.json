{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport a from \"prop-types\";\nimport { useAnimation as H } from \"@progress/kendo-react-animation\";\nimport { useRtl as K, classNames as I, getTabIndex as q } from \"@progress/kendo-react-common\";\nimport { truncateNumber as G, updateProgress as f, calculatePercentage as v } from \"../common/utils.mjs\";\nimport { usePrevious as J } from \"./hooks/usePrevious.mjs\";\nimport { DEFAULT_ANIMATION_DURATION as Q, NO_ANIMATION as X } from \"../common/constants.mjs\";\nconst R = e.forwardRef((t, S) => {\n  const {\n      animation: p = o.animation,\n      disabled: g = o.disabled,\n      reverse: C = o.reverse,\n      orientation: A,\n      labelVisible: T = o.labelVisible,\n      labelPlacement: d,\n      max: l = o.max,\n      min: n = o.min,\n      tabIndex: V,\n      className: L,\n      style: O,\n      emptyStyle: h,\n      emptyClassName: N,\n      progressStyle: w,\n      progressClassName: k\n    } = t,\n    i = e.useRef(null),\n    c = e.useRef(null),\n    m = e.useRef(null),\n    U = e.useCallback(() => {\n      i.current && i.current.focus();\n    }, []);\n  e.useImperativeHandle(S, () => ({\n    element: i.current,\n    progressStatusElement: c.current,\n    progressStatusWrapElement: m.current,\n    focus: U\n  }));\n  const s = t.value || o.value,\n    u = J(s),\n    y = t.value === null,\n    D = K(i, t.dir),\n    r = A === \"vertical\",\n    _ = G(s),\n    j = {\n      value: s\n    },\n    E = T ? t.label ? /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-progress-status\"\n    }, /* @__PURE__ */e.createElement(t.label, {\n      ...j\n    })) : /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-progress-status\"\n    }, _) : void 0,\n    B = {\n      className: I(\"k-progressbar\", {\n        \"k-progressbar-horizontal\": !r,\n        \"k-progressbar-vertical\": r,\n        \"k-progressbar-reverse\": C,\n        \"k-progressbar-indeterminate\": y,\n        \"k-disabled\": g\n      }, L),\n      ref: i,\n      dir: D,\n      tabIndex: q(V, g),\n      role: \"progressbar\",\n      \"aria-label\": t.ariaLabel,\n      \"aria-valuemin\": n,\n      \"aria-valuemax\": l,\n      \"aria-valuenow\": y ? void 0 : s,\n      \"aria-disabled\": g,\n      style: O\n    },\n    P = I(\"k-progress-status-wrap\", {\n      \"k-progress-start\": d === \"start\",\n      \"k-progress-center\": d === \"center\",\n      \"k-progress-end\": d === \"end\" || d === void 0\n    }),\n    x = typeof p != \"boolean\" && p !== void 0 ? p.duration : p ? Q : X,\n    M = e.useCallback(() => {\n      const b = v(n, l, u);\n      f(c, m, b, r);\n    }, [r, l, n, u]),\n    W = e.useCallback(b => {\n      const F = v(n, l, u + (s - u) * b);\n      f(c, m, F, r);\n    }, [n, l, u, s, r]),\n    z = e.useCallback(() => {\n      const b = v(n, l, s);\n      f(c, m, b, r);\n    }, [r, l, n, s]);\n  return H({\n    duration: x,\n    onStart: M,\n    onUpdate: W,\n    onEnd: z\n  }, [s, x]), /* @__PURE__ */e.createElement(\"div\", {\n    ...B\n  }, /* @__PURE__ */e.createElement(\"span\", {\n    className: P + (N ? \" \" + N : \"\"),\n    style: h\n  }, E), /* @__PURE__ */e.createElement(\"div\", {\n    className: \"k-progressbar-value k-selected\",\n    style: w,\n    ref: c\n  }, /* @__PURE__ */e.createElement(\"span\", {\n    className: P + (k ? \" \" + k : \"\"),\n    ref: m\n  }, E)));\n});\nR.propTypes = {\n  animation: a.any,\n  ariaLabel: a.string,\n  disabled: a.bool,\n  reverse: a.bool,\n  label: a.any,\n  labelVisible: a.bool,\n  labelPlacement: a.oneOf([\"start\", \"center\", \"end\"]),\n  max: a.number,\n  min: a.number,\n  value: a.number,\n  tabIndex: a.number,\n  emptyStyle: a.object,\n  emptyClassName: a.string,\n  progressStyle: a.object,\n  progressClassName: a.string\n};\nconst o = {\n  animation: !1,\n  min: 0,\n  max: 100,\n  value: 0,\n  disabled: !1,\n  reverse: !1,\n  labelVisible: !0\n};\nR.displayName = \"KendoProgressBar\";\nexport { R as ProgressBar };", "map": {"version": 3, "names": ["e", "a", "useAnimation", "H", "useRtl", "K", "classNames", "I", "getTabIndex", "q", "truncateNumber", "G", "updateProgress", "f", "calculatePercentage", "v", "usePrevious", "J", "DEFAULT_ANIMATION_DURATION", "Q", "NO_ANIMATION", "X", "R", "forwardRef", "t", "S", "animation", "p", "o", "disabled", "g", "reverse", "C", "orientation", "A", "labelVisible", "T", "labelPlacement", "d", "max", "l", "min", "n", "tabIndex", "V", "className", "L", "style", "O", "emptyStyle", "h", "emptyClassName", "N", "progressStyle", "w", "progressClassName", "k", "i", "useRef", "c", "m", "U", "useCallback", "current", "focus", "useImperativeHandle", "element", "progressStatusElement", "progressStatusWrapElement", "s", "value", "u", "y", "D", "dir", "r", "_", "j", "E", "label", "createElement", "B", "ref", "role", "aria<PERSON><PERSON><PERSON>", "P", "x", "duration", "M", "b", "W", "F", "z", "onStart", "onUpdate", "onEnd", "propTypes", "any", "string", "bool", "oneOf", "number", "object", "displayName", "ProgressBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-progressbars/progressbar/ProgressBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport a from \"prop-types\";\nimport { useAnimation as H } from \"@progress/kendo-react-animation\";\nimport { useRtl as K, classNames as I, getTabIndex as q } from \"@progress/kendo-react-common\";\nimport { truncateNumber as G, updateProgress as f, calculatePercentage as v } from \"../common/utils.mjs\";\nimport { usePrevious as J } from \"./hooks/usePrevious.mjs\";\nimport { DEFAULT_ANIMATION_DURATION as Q, NO_ANIMATION as X } from \"../common/constants.mjs\";\nconst R = e.forwardRef((t, S) => {\n  const {\n    animation: p = o.animation,\n    disabled: g = o.disabled,\n    reverse: C = o.reverse,\n    orientation: A,\n    labelVisible: T = o.labelVisible,\n    labelPlacement: d,\n    max: l = o.max,\n    min: n = o.min,\n    tabIndex: V,\n    className: L,\n    style: O,\n    emptyStyle: h,\n    emptyClassName: N,\n    progressStyle: w,\n    progressClassName: k\n  } = t, i = e.useRef(null), c = e.useRef(null), m = e.useRef(null), U = e.useCallback(() => {\n    i.current && i.current.focus();\n  }, []);\n  e.useImperativeHandle(\n    S,\n    () => ({\n      element: i.current,\n      progressStatusElement: c.current,\n      progressStatusWrapElement: m.current,\n      focus: U\n    })\n  );\n  const s = t.value || o.value, u = J(s), y = t.value === null, D = K(i, t.dir), r = A === \"vertical\", _ = G(s), j = {\n    value: s\n  }, E = T ? t.label ? /* @__PURE__ */ e.createElement(\"span\", { className: \"k-progress-status\" }, /* @__PURE__ */ e.createElement(t.label, { ...j })) : /* @__PURE__ */ e.createElement(\"span\", { className: \"k-progress-status\" }, _) : void 0, B = {\n    className: I(\n      \"k-progressbar\",\n      {\n        \"k-progressbar-horizontal\": !r,\n        \"k-progressbar-vertical\": r,\n        \"k-progressbar-reverse\": C,\n        \"k-progressbar-indeterminate\": y,\n        \"k-disabled\": g\n      },\n      L\n    ),\n    ref: i,\n    dir: D,\n    tabIndex: q(V, g),\n    role: \"progressbar\",\n    \"aria-label\": t.ariaLabel,\n    \"aria-valuemin\": n,\n    \"aria-valuemax\": l,\n    \"aria-valuenow\": y ? void 0 : s,\n    \"aria-disabled\": g,\n    style: O\n  }, P = I(\"k-progress-status-wrap\", {\n    \"k-progress-start\": d === \"start\",\n    \"k-progress-center\": d === \"center\",\n    \"k-progress-end\": d === \"end\" || d === void 0\n  }), x = typeof p != \"boolean\" && p !== void 0 ? p.duration : p ? Q : X, M = e.useCallback(() => {\n    const b = v(n, l, u);\n    f(c, m, b, r);\n  }, [r, l, n, u]), W = e.useCallback(\n    (b) => {\n      const F = v(n, l, u + (s - u) * b);\n      f(c, m, F, r);\n    },\n    [n, l, u, s, r]\n  ), z = e.useCallback(() => {\n    const b = v(n, l, s);\n    f(c, m, b, r);\n  }, [r, l, n, s]);\n  return H(\n    {\n      duration: x,\n      onStart: M,\n      onUpdate: W,\n      onEnd: z\n    },\n    [s, x]\n  ), /* @__PURE__ */ e.createElement(\"div\", { ...B }, /* @__PURE__ */ e.createElement(\"span\", { className: P + (N ? \" \" + N : \"\"), style: h }, E), /* @__PURE__ */ e.createElement(\"div\", { className: \"k-progressbar-value k-selected\", style: w, ref: c }, /* @__PURE__ */ e.createElement(\n    \"span\",\n    {\n      className: P + (k ? \" \" + k : \"\"),\n      ref: m\n    },\n    E\n  )));\n});\nR.propTypes = {\n  animation: a.any,\n  ariaLabel: a.string,\n  disabled: a.bool,\n  reverse: a.bool,\n  label: a.any,\n  labelVisible: a.bool,\n  labelPlacement: a.oneOf([\"start\", \"center\", \"end\"]),\n  max: a.number,\n  min: a.number,\n  value: a.number,\n  tabIndex: a.number,\n  emptyStyle: a.object,\n  emptyClassName: a.string,\n  progressStyle: a.object,\n  progressClassName: a.string\n};\nconst o = {\n  animation: !1,\n  min: 0,\n  max: 100,\n  value: 0,\n  disabled: !1,\n  reverse: !1,\n  labelVisible: !0\n};\nR.displayName = \"KendoProgressBar\";\nexport {\n  R as ProgressBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,YAAY,IAAIC,CAAC,QAAQ,iCAAiC;AACnE,SAASC,MAAM,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,QAAQ,8BAA8B;AAC7F,SAASC,cAAc,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,mBAAmB,IAAIC,CAAC,QAAQ,qBAAqB;AACxG,SAASC,WAAW,IAAIC,CAAC,QAAQ,yBAAyB;AAC1D,SAASC,0BAA0B,IAAIC,CAAC,EAAEC,YAAY,IAAIC,CAAC,QAAQ,yBAAyB;AAC5F,MAAMC,CAAC,GAAGtB,CAAC,CAACuB,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MACJC,SAAS,EAAEC,CAAC,GAAGC,CAAC,CAACF,SAAS;MAC1BG,QAAQ,EAAEC,CAAC,GAAGF,CAAC,CAACC,QAAQ;MACxBE,OAAO,EAAEC,CAAC,GAAGJ,CAAC,CAACG,OAAO;MACtBE,WAAW,EAAEC,CAAC;MACdC,YAAY,EAAEC,CAAC,GAAGR,CAAC,CAACO,YAAY;MAChCE,cAAc,EAAEC,CAAC;MACjBC,GAAG,EAAEC,CAAC,GAAGZ,CAAC,CAACW,GAAG;MACdE,GAAG,EAAEC,CAAC,GAAGd,CAAC,CAACa,GAAG;MACdE,QAAQ,EAAEC,CAAC;MACXC,SAAS,EAAEC,CAAC;MACZC,KAAK,EAAEC,CAAC;MACRC,UAAU,EAAEC,CAAC;MACbC,cAAc,EAAEC,CAAC;MACjBC,aAAa,EAAEC,CAAC;MAChBC,iBAAiB,EAAEC;IACrB,CAAC,GAAGhC,CAAC;IAAEiC,CAAC,GAAGzD,CAAC,CAAC0D,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAG3D,CAAC,CAAC0D,MAAM,CAAC,IAAI,CAAC;IAAEE,CAAC,GAAG5D,CAAC,CAAC0D,MAAM,CAAC,IAAI,CAAC;IAAEG,CAAC,GAAG7D,CAAC,CAAC8D,WAAW,CAAC,MAAM;MACzFL,CAAC,CAACM,OAAO,IAAIN,CAAC,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACNhE,CAAC,CAACiE,mBAAmB,CACnBxC,CAAC,EACD,OAAO;IACLyC,OAAO,EAAET,CAAC,CAACM,OAAO;IAClBI,qBAAqB,EAAER,CAAC,CAACI,OAAO;IAChCK,yBAAyB,EAAER,CAAC,CAACG,OAAO;IACpCC,KAAK,EAAEH;EACT,CAAC,CACH,CAAC;EACD,MAAMQ,CAAC,GAAG7C,CAAC,CAAC8C,KAAK,IAAI1C,CAAC,CAAC0C,KAAK;IAAEC,CAAC,GAAGtD,CAAC,CAACoD,CAAC,CAAC;IAAEG,CAAC,GAAGhD,CAAC,CAAC8C,KAAK,KAAK,IAAI;IAAEG,CAAC,GAAGpE,CAAC,CAACoD,CAAC,EAAEjC,CAAC,CAACkD,GAAG,CAAC;IAAEC,CAAC,GAAGzC,CAAC,KAAK,UAAU;IAAE0C,CAAC,GAAGjE,CAAC,CAAC0D,CAAC,CAAC;IAAEQ,CAAC,GAAG;MACjHP,KAAK,EAAED;IACT,CAAC;IAAES,CAAC,GAAG1C,CAAC,GAAGZ,CAAC,CAACuD,KAAK,GAAG,eAAgB/E,CAAC,CAACgF,aAAa,CAAC,MAAM,EAAE;MAAEnC,SAAS,EAAE;IAAoB,CAAC,EAAE,eAAgB7C,CAAC,CAACgF,aAAa,CAACxD,CAAC,CAACuD,KAAK,EAAE;MAAE,GAAGF;IAAE,CAAC,CAAC,CAAC,GAAG,eAAgB7E,CAAC,CAACgF,aAAa,CAAC,MAAM,EAAE;MAAEnC,SAAS,EAAE;IAAoB,CAAC,EAAE+B,CAAC,CAAC,GAAG,KAAK,CAAC;IAAEK,CAAC,GAAG;MAClPpC,SAAS,EAAEtC,CAAC,CACV,eAAe,EACf;QACE,0BAA0B,EAAE,CAACoE,CAAC;QAC9B,wBAAwB,EAAEA,CAAC;QAC3B,uBAAuB,EAAE3C,CAAC;QAC1B,6BAA6B,EAAEwC,CAAC;QAChC,YAAY,EAAE1C;MAChB,CAAC,EACDgB,CACF,CAAC;MACDoC,GAAG,EAAEzB,CAAC;MACNiB,GAAG,EAAED,CAAC;MACN9B,QAAQ,EAAElC,CAAC,CAACmC,CAAC,EAAEd,CAAC,CAAC;MACjBqD,IAAI,EAAE,aAAa;MACnB,YAAY,EAAE3D,CAAC,CAAC4D,SAAS;MACzB,eAAe,EAAE1C,CAAC;MAClB,eAAe,EAAEF,CAAC;MAClB,eAAe,EAAEgC,CAAC,GAAG,KAAK,CAAC,GAAGH,CAAC;MAC/B,eAAe,EAAEvC,CAAC;MAClBiB,KAAK,EAAEC;IACT,CAAC;IAAEqC,CAAC,GAAG9E,CAAC,CAAC,wBAAwB,EAAE;MACjC,kBAAkB,EAAE+B,CAAC,KAAK,OAAO;MACjC,mBAAmB,EAAEA,CAAC,KAAK,QAAQ;MACnC,gBAAgB,EAAEA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK;IAC9C,CAAC,CAAC;IAAEgD,CAAC,GAAG,OAAO3D,CAAC,IAAI,SAAS,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,CAAC4D,QAAQ,GAAG5D,CAAC,GAAGR,CAAC,GAAGE,CAAC;IAAEmE,CAAC,GAAGxF,CAAC,CAAC8D,WAAW,CAAC,MAAM;MAC9F,MAAM2B,CAAC,GAAG1E,CAAC,CAAC2B,CAAC,EAAEF,CAAC,EAAE+B,CAAC,CAAC;MACpB1D,CAAC,CAAC8C,CAAC,EAAEC,CAAC,EAAE6B,CAAC,EAAEd,CAAC,CAAC;IACf,CAAC,EAAE,CAACA,CAAC,EAAEnC,CAAC,EAAEE,CAAC,EAAE6B,CAAC,CAAC,CAAC;IAAEmB,CAAC,GAAG1F,CAAC,CAAC8D,WAAW,CAChC2B,CAAC,IAAK;MACL,MAAME,CAAC,GAAG5E,CAAC,CAAC2B,CAAC,EAAEF,CAAC,EAAE+B,CAAC,GAAG,CAACF,CAAC,GAAGE,CAAC,IAAIkB,CAAC,CAAC;MAClC5E,CAAC,CAAC8C,CAAC,EAAEC,CAAC,EAAE+B,CAAC,EAAEhB,CAAC,CAAC;IACf,CAAC,EACD,CAACjC,CAAC,EAAEF,CAAC,EAAE+B,CAAC,EAAEF,CAAC,EAAEM,CAAC,CAChB,CAAC;IAAEiB,CAAC,GAAG5F,CAAC,CAAC8D,WAAW,CAAC,MAAM;MACzB,MAAM2B,CAAC,GAAG1E,CAAC,CAAC2B,CAAC,EAAEF,CAAC,EAAE6B,CAAC,CAAC;MACpBxD,CAAC,CAAC8C,CAAC,EAAEC,CAAC,EAAE6B,CAAC,EAAEd,CAAC,CAAC;IACf,CAAC,EAAE,CAACA,CAAC,EAAEnC,CAAC,EAAEE,CAAC,EAAE2B,CAAC,CAAC,CAAC;EAChB,OAAOlE,CAAC,CACN;IACEoF,QAAQ,EAAED,CAAC;IACXO,OAAO,EAAEL,CAAC;IACVM,QAAQ,EAAEJ,CAAC;IACXK,KAAK,EAAEH;EACT,CAAC,EACD,CAACvB,CAAC,EAAEiB,CAAC,CACP,CAAC,EAAE,eAAgBtF,CAAC,CAACgF,aAAa,CAAC,KAAK,EAAE;IAAE,GAAGC;EAAE,CAAC,EAAE,eAAgBjF,CAAC,CAACgF,aAAa,CAAC,MAAM,EAAE;IAAEnC,SAAS,EAAEwC,CAAC,IAAIjC,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,EAAE,CAAC;IAAEL,KAAK,EAAEG;EAAE,CAAC,EAAE4B,CAAC,CAAC,EAAE,eAAgB9E,CAAC,CAACgF,aAAa,CAAC,KAAK,EAAE;IAAEnC,SAAS,EAAE,gCAAgC;IAAEE,KAAK,EAAEO,CAAC;IAAE4B,GAAG,EAAEvB;EAAE,CAAC,EAAE,eAAgB3D,CAAC,CAACgF,aAAa,CACxR,MAAM,EACN;IACEnC,SAAS,EAAEwC,CAAC,IAAI7B,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,EAAE,CAAC;IACjC0B,GAAG,EAAEtB;EACP,CAAC,EACDkB,CACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFxD,CAAC,CAAC0E,SAAS,GAAG;EACZtE,SAAS,EAAEzB,CAAC,CAACgG,GAAG;EAChBb,SAAS,EAAEnF,CAAC,CAACiG,MAAM;EACnBrE,QAAQ,EAAE5B,CAAC,CAACkG,IAAI;EAChBpE,OAAO,EAAE9B,CAAC,CAACkG,IAAI;EACfpB,KAAK,EAAE9E,CAAC,CAACgG,GAAG;EACZ9D,YAAY,EAAElC,CAAC,CAACkG,IAAI;EACpB9D,cAAc,EAAEpC,CAAC,CAACmG,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACnD7D,GAAG,EAAEtC,CAAC,CAACoG,MAAM;EACb5D,GAAG,EAAExC,CAAC,CAACoG,MAAM;EACb/B,KAAK,EAAErE,CAAC,CAACoG,MAAM;EACf1D,QAAQ,EAAE1C,CAAC,CAACoG,MAAM;EAClBpD,UAAU,EAAEhD,CAAC,CAACqG,MAAM;EACpBnD,cAAc,EAAElD,CAAC,CAACiG,MAAM;EACxB7C,aAAa,EAAEpD,CAAC,CAACqG,MAAM;EACvB/C,iBAAiB,EAAEtD,CAAC,CAACiG;AACvB,CAAC;AACD,MAAMtE,CAAC,GAAG;EACRF,SAAS,EAAE,CAAC,CAAC;EACbe,GAAG,EAAE,CAAC;EACNF,GAAG,EAAE,GAAG;EACR+B,KAAK,EAAE,CAAC;EACRzC,QAAQ,EAAE,CAAC,CAAC;EACZE,OAAO,EAAE,CAAC,CAAC;EACXI,YAAY,EAAE,CAAC;AACjB,CAAC;AACDb,CAAC,CAACiF,WAAW,GAAG,kBAAkB;AAClC,SACEjF,CAAC,IAAIkF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}