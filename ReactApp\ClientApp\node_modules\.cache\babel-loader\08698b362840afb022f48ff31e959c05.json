{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst A = 400,\n  N = 0;\nexport { A as DEFAULT_ANIMATION_DURATION, N as NO_ANIMATION };", "map": {"version": 3, "names": ["A", "N", "DEFAULT_ANIMATION_DURATION", "NO_ANIMATION"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/stepper/contants.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst A = 400, N = 0;\nexport {\n  A as DEFAULT_ANIMATION_DURATION,\n  N as NO_ANIMATION\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,CAAC;AACpB,SACED,CAAC,IAAIE,0BAA0B,EAC/BD,CAAC,IAAIE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}