import React from "react";
import { shallow } from "enzyme";

import AppModal from "../index";

describe("Common Modal component", () => {
  it("should render without crashing", () => {
    const modalComponent = shallow(<AppModal />);
    expect(modalComponent.html()).not.toBeNull();
  });

  it("should append passed in props", () => {
    const modalComponent = shallow(<AppModal visible={true} title="Unit test title" />);
    expect(modalComponent.props().visible).toBeTruthy();
    expect(modalComponent.props().title).toBe("Unit test title");
  });

  it("should provide correct style for props passes as small", () => {
    const modalComponent = shallow(<AppModal size="small" />);
    expect(modalComponent.props().className).toStrictEqual("yjCommonModalSmall");
  });

  it("should provide correct style for props passes as medium", () => {
    const modalComponent = shallow(<AppModal size="medium" />);
    expect(modalComponent.props().className).toStrictEqual("yjCommonModalMedium");
  });

  it("should provide correct style when props doesnt have a size", () => {
    const modalComponent = shallow(<AppModal />);
    expect(modalComponent.props().className).toStrictEqual("yjCommonModalLarge");
  });
});
