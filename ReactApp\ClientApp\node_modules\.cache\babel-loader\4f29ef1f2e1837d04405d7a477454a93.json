{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst A = 1e-5,\n  I = 3,\n  N = 400,\n  o = 0;\nexport { N as DEFAULT_ANIMATION_DURATION, I as LABEL_DECIMALS, A as MIN_RATIO, o as NO_ANIMATION };", "map": {"version": 3, "names": ["A", "I", "N", "o", "DEFAULT_ANIMATION_DURATION", "LABEL_DECIMALS", "MIN_RATIO", "NO_ANIMATION"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-progressbars/common/constants.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst A = 1e-5, I = 3, N = 400, o = 0;\nexport {\n  N as DEFAULT_ANIMATION_DURATION,\n  I as LABEL_DECIMALS,\n  A as MIN_RATIO,\n  o as NO_ANIMATION\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG,IAAI;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,CAAC;AACrC,SACED,CAAC,IAAIE,0BAA0B,EAC/BH,CAAC,IAAII,cAAc,EACnBL,CAAC,IAAIM,SAAS,EACdH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}