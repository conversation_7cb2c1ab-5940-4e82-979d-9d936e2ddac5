{"ast": null, "code": "import \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/menu/style\";\nimport _Menu from \"antd/es/menu\";\nimport \"antd/es/layout/style\";\nimport _Layout from \"antd/es/layout\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\SideMenu\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport { useAppSelector } from '@app/hooks/useAppSelector';\nimport UseLocalStorage from '@app/hooks/useLocalStorage';\nimport { selectAppState } from '@app/appSlice';\nimport constants from '@app/utils/Constants';\nimport { Button as KendoButton } from '@progress/kendo-react-buttons';\nimport { AiOutlineSetting } from 'react-icons/ai';\nimport SubMenu from 'antd/lib/menu/SubMenu';\nimport React, { useEffect, useState } from 'react';\nimport { matchPath, useLocation, useNavigate } from 'react-router-dom';\nimport styles from './index.module.less';\nimport { useLazyGetFirmLogoAndNameQuery } from \"@app/api/firmApiSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = _Layout;\nconst SideMenu = _ref => {\n  _s();\n  let {\n    menu\n  } = _ref;\n  const [theme, setTheme] = useState();\n  const {\n    publishedFileCount\n  } = useAppSelector(selectAppState);\n  const [collapse, setCollapse] = UseLocalStorage(constants.sideMenuCollapse, false);\n  const [hamburgerButtonClicked, setHamburgerButtonClicked] = useState(false);\n  const location = useLocation();\n  let navigate = useNavigate();\n  const [selectedRoutes, setSelectedRoutes] = useState(getMatchedPaths(menu, location.pathname));\n  const [triggerGetFirmLogoAndName] = useLazyGetFirmLogoAndNameQuery();\n  useEffect(() => {\n    triggerGetFirmLogoAndName().then(res => {\n      (res === null || res === void 0 ? void 0 : res.data) && setTheme(res.data);\n    });\n  }, []);\n  useEffect(() => {\n    // const listener = history.listen(() => {\n    //   logger.debug('YJ Client Portal', 'SideMenu', `route change identified : ${location.pathname}`);\n    setSelectedRoutes(getMatchedPaths(menu, location.pathname));\n    // });\n    return () => {\n      // listener();\n    };\n  }, [menu, location]);\n  const _toggle = () => {\n    collapse ? setCollapse(false) : setCollapse(true);\n    setHamburgerButtonClicked(!hamburgerButtonClicked);\n  };\n  const _redirect = path => {\n    navigate(path);\n  };\n  const renderSubMenu = item => {\n    return item.children && item.privileged && /*#__PURE__*/_jsxDEV(SubMenu, {\n      className: styles.yjSubMenuItem,\n      disabled: item.noRoute,\n      icon: typeof item.icon === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 49\n      }, this) : /*#__PURE__*/_jsxDEV(item.icon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 81\n      }, this),\n      title: item.title,\n      children: _menuItems(item.children)\n    }, _makeRouteHash(item.path), false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this);\n  };\n  const getMenuItemStyle = key => {\n    return location.pathname === key ? {\n      backgroundColor: `#${theme === null || theme === void 0 ? void 0 : theme.primaryColor}`,\n      transition: 'color 0.3s, background-color 0.3s'\n    } : {\n      transition: 'color 0.3s, background-color 0.3s'\n    };\n  };\n  const handleMouseEnter = e => {\n    const {\n      domEvent\n    } = e;\n    const target = domEvent.currentTarget;\n    target.style.background = `#${theme === null || theme === void 0 ? void 0 : theme.primaryColor}`;\n  };\n  const handleMouseLeave = (e, key) => {\n    const {\n      domEvent\n    } = e;\n    const target = domEvent.currentTarget;\n    target.style.background = location.pathname === key ? `#${theme === null || theme === void 0 ? void 0 : theme.primaryColor}` : `#${theme === null || theme === void 0 ? void 0 : theme.secondaryColor}`;\n  };\n  const renderMenuItem = item => {\n    if (item.noRoute) return /*#__PURE__*/_jsxDEV(_Menu.Item, {\n      \"data-path\": item.path,\n      style: getMenuItemStyle(item.path),\n      onMouseEnter: e => handleMouseEnter(e),\n      onMouseLeave: e => handleMouseLeave(e, item.path),\n      className: styles.yjMenuItemDisabled,\n      icon: typeof item.icon === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 49\n      }, this) : /*#__PURE__*/_jsxDEV(item.icon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 81\n      }, this),\n      id: _makeRouteHash(item.path),\n      children: collapse ? 'This module is coming soon.' : item.title\n    }, _makeRouteHash(item.path), false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 9\n    }, this);\n    return item.privileged && /*#__PURE__*/_jsxDEV(_Menu.Item, {\n      \"data-path\": item.path,\n      style: getMenuItemStyle(item.path),\n      onMouseEnter: e => handleMouseEnter(e),\n      onMouseLeave: e => handleMouseLeave(e, item.path),\n      className: styles.yjMenuItem,\n      onClick: () => _redirect(item.path),\n      icon: item.hasCount && publishedFileCount && collapse ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(item.icon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.yj_cp_menu_item_count_collapsed,\n          children: publishedFileCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 15\n      }, this) : /*#__PURE__*/_jsxDEV(item.icon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 15\n      }, this),\n      children: item.hasCount && publishedFileCount && !collapse ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ant-menu-title-content\",\n        children: [item.title, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.yj_cp_menu_item_count,\n          children: publishedFileCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 13\n      }, this) : item.title\n    }, _makeRouteHash(item.path), false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this);\n  };\n  const _menuItems = data => {\n    return data.map(item => item.children && item.children.length > 0 ? renderSubMenu(item) : renderMenuItem(item));\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    trigger: null,\n    collapsible: true,\n    collapsed: collapse,\n    breakpoint: \"lg\",\n    collapsedWidth: \"70\",\n    className: 'yj_cp_sidenav',\n    width: \"300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.yj_cp_sidemenu_wrapper,\n      style: {\n        backgroundColor: `#${theme === null || theme === void 0 ? void 0 : theme.secondaryColor}`\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 50\n        },\n        children: /*#__PURE__*/_jsxDEV(_Button, {\n          onClick: _toggle,\n          style: {\n            position: 'absolute',\n            right: 0,\n            backgroundColor: `#${theme === null || theme === void 0 ? void 0 : theme.tertiaryColor}`\n          },\n          className: styles.yj_cp_sidemenu_hamburger_button,\n          children: collapse ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(_Menu, {\n        mode: \"inline\",\n        className: styles.yj_cp_sidemenu,\n        selectedKeys: selectedRoutes,\n        children: _menuItems(menu)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.yj_cp_portal_controls_sidebar_container,\n        children: /*#__PURE__*/_jsxDEV(KendoButton, {\n          onClick: () => _redirect('/portalControls'),\n          themeColor: \"primary\",\n          fillMode: \"solid\",\n          className: styles.yj_cp_portal_controls_sidebar,\n          children: [/*#__PURE__*/_jsxDEV(AiOutlineSetting, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), !collapse && 'Portal Controls']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(SideMenu, \"HfCgQpGuFipq0BpuMhJPdrjgCQA=\", false, function () {\n  return [useAppSelector, useLocation, useNavigate, useLazyGetFirmLogoAndNameQuery];\n});\n_c = SideMenu;\nconst getMatchedPaths = (routes, location) => {\n  // If we're on Portal Controls page, clear all menu selections\n  if (location === '/portalControls') {\n    return [];\n  }\n  const matchedPathsArray = [];\n  const generateFlatPathArray = (routeValues, locationValue) => {\n    if (routeValues) {\n      for (const route of routeValues) {\n        const matchedRoute = matchPath({\n          path: route.path,\n          end: route.exact\n        }, locationValue);\n        if (matchedRoute) {\n          matchedPathsArray.push(_makeRouteHash(route.path));\n        }\n        if (matchedRoute && route.children) {\n          generateFlatPathArray(route.children, locationValue);\n        }\n      }\n    }\n  };\n  generateFlatPathArray(routes, location);\n  return matchedPathsArray;\n};\nconst _makeRouteHash = key => {\n  return key.split('/').join('');\n};\nexport default SideMenu;\nvar _c;\n$RefreshReg$(_c, \"SideMenu\");", "map": {"version": 3, "names": ["MenuFoldOutlined", "MenuUnfoldOutlined", "useAppSelector", "UseLocalStorage", "selectAppState", "constants", "<PERSON><PERSON>", "KendoB<PERSON>on", "AiOutlineSetting", "SubMenu", "React", "useEffect", "useState", "matchPath", "useLocation", "useNavigate", "styles", "useLazyGetFirmLogoAndNameQuery", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_Layout", "SideMenu", "_ref", "_s", "menu", "theme", "setTheme", "publishedFileCount", "collapse", "setCollapse", "sideMenuCollapse", "hamburgerButtonClicked", "setHamburgerButtonClicked", "location", "navigate", "selectedRoutes", "setSelectedRoutes", "getMatchedPaths", "pathname", "triggerGetFirmLogoAndName", "then", "res", "data", "_toggle", "_redirect", "path", "renderSubMenu", "item", "children", "privileged", "className", "yjSubMenuItem", "disabled", "noRoute", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "_menuItems", "_makeRouteHash", "getMenuItemStyle", "key", "backgroundColor", "primaryColor", "transition", "handleMouseEnter", "e", "domEvent", "target", "currentTarget", "style", "background", "handleMouseLeave", "secondaryColor", "renderMenuItem", "_Menu", "<PERSON><PERSON>", "onMouseEnter", "onMouseLeave", "yjMenuItemDisabled", "id", "yjMenuItem", "onClick", "hasCount", "yj_cp_menu_item_count_collapsed", "yj_cp_menu_item_count", "map", "length", "trigger", "collapsible", "collapsed", "breakpoint", "collapsedWidth", "width", "yj_cp_sidemenu_wrapper", "height", "_<PERSON><PERSON>", "position", "right", "tertiaryColor", "yj_cp_sidemenu_hamburger_button", "mode", "yj_cp_sidemenu", "<PERSON><PERSON><PERSON><PERSON>", "yj_cp_portal_controls_sidebar_container", "themeColor", "fillMode", "yj_cp_portal_controls_sidebar", "_c", "routes", "matchedPathsA<PERSON>y", "generateFlatPathArray", "routeValues", "locationValue", "route", "matchedRoute", "end", "exact", "push", "split", "join", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/components/SideMenu/index.tsx"], "sourcesContent": ["import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport UseLocalStorage from '@app/hooks/useLocalStorage';\r\nimport { selectAppState } from '@app/appSlice';\r\nimport MenuConfigType from '@app/types/MenuConfigType';\r\nimport constants from '@app/utils/Constants';\r\nimport { Button, Layout, Menu } from 'antd';\r\nimport { Button as KendoButton } from '@progress/kendo-react-buttons';\r\nimport { AiOutlineSetting } from 'react-icons/ai';\r\nimport SubMenu from 'antd/lib/menu/SubMenu';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { matchPath, useLocation, useNavigate } from 'react-router-dom';\r\nimport styles from './index.module.less';\r\nimport { useLazyGetFirmLogoAndNameQuery } from \"@app/api/firmApiSlice\";\r\n\r\nconst { Sider } = Layout;\r\n\r\nconst SideMenu = ({ menu }: { menu: MenuConfigType[] }) => {\r\n\r\n  const [theme, setTheme] = useState<any>();\r\n\r\n  const { publishedFileCount } = useAppSelector(selectAppState);\r\n  const [collapse, setCollapse] = UseLocalStorage(constants.sideMenuCollapse, false);\r\n  const [hamburgerButtonClicked, setHamburgerButtonClicked] = useState(false);\r\n  const location = useLocation();\r\n  let navigate = useNavigate();\r\n\r\n  const [selectedRoutes, setSelectedRoutes] = useState(getMatchedPaths(menu, location.pathname));\r\n  const [triggerGetFirmLogoAndName] = useLazyGetFirmLogoAndNameQuery();\r\n\r\n  useEffect(()=>{\r\n    triggerGetFirmLogoAndName().then((res)=>{\r\n      res?.data && setTheme(res.data)\r\n    })\r\n  },[])\r\n\r\n  useEffect(() => {\r\n    // const listener = history.listen(() => {\r\n    //   logger.debug('YJ Client Portal', 'SideMenu', `route change identified : ${location.pathname}`);\r\n    setSelectedRoutes(getMatchedPaths(menu, location.pathname));\r\n    // });\r\n    return () => {\r\n      // listener();\r\n    };\r\n  }, [menu, location]);\r\n\r\n  const _toggle = () => {\r\n    collapse ? setCollapse(false) : setCollapse(true);\r\n    setHamburgerButtonClicked(!hamburgerButtonClicked);\r\n  };\r\n\r\n  const _redirect = (path: string) => {\r\n    navigate(path);\r\n  };\r\n\r\n  const renderSubMenu = (item: MenuConfigType) => {\r\n    return (\r\n      item.children &&\r\n      item.privileged && (\r\n        <SubMenu\r\n          className={styles.yjSubMenuItem}\r\n          disabled={item.noRoute}\r\n          icon={typeof item.icon === 'string' ? <div className={item.icon} /> : <item.icon />}\r\n          key={_makeRouteHash(item.path)}\r\n          title={item.title}\r\n        >\r\n          {_menuItems(item.children)}\r\n        </SubMenu>\r\n      )\r\n    );\r\n  };\r\n\r\n  const getMenuItemStyle = (key: string) => {\r\n    return location.pathname === key ? { backgroundColor: `#${theme?.primaryColor}`, transition: 'color 0.3s, background-color 0.3s'} : { transition: 'color 0.3s, background-color 0.3s'};\r\n  };\r\n\r\n  const handleMouseEnter = (e:any) => {\r\n    const { domEvent } = e;\r\n    const target = domEvent.currentTarget;\r\n    target.style.background = `#${theme?.primaryColor}`;\r\n  };\r\n\r\n  const handleMouseLeave = (e:any, key: string) => {\r\n    const { domEvent } = e;\r\n    const target = domEvent.currentTarget;\r\n    target.style.background  = location.pathname === key ? `#${theme?.primaryColor}` : `#${theme?.secondaryColor}`;\r\n  };\r\n\r\n  const renderMenuItem = (item: MenuConfigType) => {\r\n    if (item.noRoute)\r\n      return (\r\n        <Menu.Item\r\n            data-path={item.path}\r\n            style={getMenuItemStyle(item.path)}\r\n            onMouseEnter={(e) => handleMouseEnter(e)}\r\n            onMouseLeave={(e) => handleMouseLeave(e, item.path)}\r\n          className={styles.yjMenuItemDisabled}\r\n          icon={typeof item.icon === 'string' ? <div className={item.icon} /> : <item.icon />}\r\n          key={_makeRouteHash(item.path)}\r\n          id={_makeRouteHash(item.path)}\r\n        >\r\n          {collapse ? 'This module is coming soon.' : item.title}\r\n        </Menu.Item>\r\n      );\r\n    return (\r\n      item.privileged && (\r\n        <Menu.Item\r\n            data-path={item.path}\r\n            style={getMenuItemStyle(item.path)}\r\n            onMouseEnter={(e) => handleMouseEnter(e)}\r\n            onMouseLeave={(e) => handleMouseLeave(e, item.path)}\r\n          className={styles.yjMenuItem}\r\n          onClick={() => _redirect(item.path)}\r\n          icon={\r\n            item.hasCount && publishedFileCount && collapse ? (\r\n              <div>\r\n                <item.icon />\r\n                <div className={styles.yj_cp_menu_item_count_collapsed}>{publishedFileCount}</div>\r\n              </div>\r\n            ) : (\r\n              <item.icon />\r\n            )\r\n          }\r\n          key={_makeRouteHash(item.path)}\r\n        >\r\n          {item.hasCount && publishedFileCount && !collapse ? (\r\n            <div className=\"ant-menu-title-content\">\r\n              {item.title}\r\n              <div className={styles.yj_cp_menu_item_count}>{publishedFileCount}</div>\r\n            </div>\r\n          ) : (\r\n            item.title\r\n          )}\r\n        </Menu.Item>\r\n      )\r\n    );\r\n  };\r\n\r\n  const _menuItems = (data: MenuConfigType[]) => {\r\n    return data.map((item) => (item.children && item.children.length > 0 ? renderSubMenu(item) : renderMenuItem(item)));\r\n  };\r\n\r\n  return (\r\n    <Sider trigger={null} collapsible collapsed={collapse} breakpoint=\"lg\" collapsedWidth=\"70\" className={'yj_cp_sidenav'} width=\"300\">\r\n      <div className={styles.yj_cp_sidemenu_wrapper} style={{backgroundColor: `#${theme?.secondaryColor}`}}  >\r\n        <div style={{ height: 50 }}>\r\n          <Button onClick={_toggle} style={{ position: 'absolute', right: 0 , backgroundColor: `#${theme?.tertiaryColor}` }} className={styles.yj_cp_sidemenu_hamburger_button}>\r\n            {collapse ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\r\n          </Button>\r\n        </div>\r\n        <Menu mode=\"inline\" className={styles.yj_cp_sidemenu} selectedKeys={selectedRoutes}>\r\n          {_menuItems(menu)}\r\n        </Menu>\r\n        <div className={styles.yj_cp_portal_controls_sidebar_container}>\r\n          <KendoButton\r\n            onClick={() => _redirect('/portalControls')}\r\n            themeColor=\"primary\"\r\n            fillMode=\"solid\"\r\n            className={styles.yj_cp_portal_controls_sidebar}\r\n          >\r\n            <AiOutlineSetting />\r\n            {!collapse && 'Portal Controls'}\r\n          </KendoButton>\r\n        </div>\r\n      </div>\r\n    </Sider>\r\n  );\r\n};\r\n\r\nconst getMatchedPaths = (routes: MenuConfigType[], location: string) => {\r\n  // If we're on Portal Controls page, clear all menu selections\r\n  if (location === '/portalControls') {\r\n    return [];\r\n  }\r\n\r\n  const matchedPathsArray: string[] = [];\r\n  const generateFlatPathArray = (routeValues: MenuConfigType[], locationValue: string) => {\r\n    if (routeValues) {\r\n      for (const route of routeValues) {\r\n        const matchedRoute = matchPath(\r\n          {\r\n            path: route.path,\r\n            end: route.exact,\r\n          },\r\n          locationValue\r\n        );\r\n        if (matchedRoute) {\r\n          matchedPathsArray.push(_makeRouteHash(route.path));\r\n        }\r\n        if (matchedRoute && route.children) {\r\n          generateFlatPathArray(route.children, locationValue);\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  generateFlatPathArray(routes, location);\r\n  return matchedPathsArray;\r\n};\r\n\r\nconst _makeRouteHash = (key: string) => {\r\n  return key.split('/').join('');\r\n};\r\n\r\nexport default SideMenu;\r\n"], "mappings": ";;;;;;;;AAAA,SAASA,gBAAgB,EAAEC,kBAAkB,QAAQ,mBAAmB;AACxE,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,cAAc,QAAQ,eAAe;AAE9C,OAAOC,SAAS,MAAM,sBAAsB;AAE5C,SAASC,MAAM,IAAIC,WAAW,QAAQ,+BAA+B;AACrE,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,8BAA8B,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAM;EAAEC;AAAM,CAAC,GAAAC,OAAS;AAExB,MAAMC,QAAQ,GAAGC,IAAA,IAA0C;EAAAC,EAAA;EAAA,IAAzC;IAAEC;EAAiC,CAAC,GAAAF,IAAA;EAEpD,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAM,CAAC;EAEzC,MAAM;IAAEgB;EAAmB,CAAC,GAAG1B,cAAc,CAACE,cAAc,CAAC;EAC7D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,eAAe,CAACE,SAAS,CAAC0B,gBAAgB,EAAE,KAAK,CAAC;EAClF,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAMsB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,IAAIqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE5B,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC0B,eAAe,CAACb,IAAI,EAAES,QAAQ,CAACK,QAAQ,CAAC,CAAC;EAC9F,MAAM,CAACC,yBAAyB,CAAC,GAAGvB,8BAA8B,CAAC,CAAC;EAEpEN,SAAS,CAAC,MAAI;IACZ6B,yBAAyB,CAAC,CAAC,CAACC,IAAI,CAAEC,GAAG,IAAG;MACtC,CAAAA,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEC,IAAI,KAAIhB,QAAQ,CAACe,GAAG,CAACC,IAAI,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,EAAC,EAAE,CAAC;EAELhC,SAAS,CAAC,MAAM;IACd;IACA;IACA0B,iBAAiB,CAACC,eAAe,CAACb,IAAI,EAAES,QAAQ,CAACK,QAAQ,CAAC,CAAC;IAC3D;IACA,OAAO,MAAM;MACX;IAAA,CACD;EACH,CAAC,EAAE,CAACd,IAAI,EAAES,QAAQ,CAAC,CAAC;EAEpB,MAAMU,OAAO,GAAGA,CAAA,KAAM;IACpBf,QAAQ,GAAGC,WAAW,CAAC,KAAK,CAAC,GAAGA,WAAW,CAAC,IAAI,CAAC;IACjDG,yBAAyB,CAAC,CAACD,sBAAsB,CAAC;EACpD,CAAC;EAED,MAAMa,SAAS,GAAIC,IAAY,IAAK;IAClCX,QAAQ,CAACW,IAAI,CAAC;EAChB,CAAC;EAED,MAAMC,aAAa,GAAIC,IAAoB,IAAK;IAC9C,OACEA,IAAI,CAACC,QAAQ,IACbD,IAAI,CAACE,UAAU,iBACb/B,OAAA,CAACV,OAAO;MACN0C,SAAS,EAAEnC,MAAM,CAACoC,aAAc;MAChCC,QAAQ,EAAEL,IAAI,CAACM,OAAQ;MACvBC,IAAI,EAAE,OAAOP,IAAI,CAACO,IAAI,KAAK,QAAQ,gBAAGpC,OAAA;QAAKgC,SAAS,EAAEH,IAAI,CAACO;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGxC,OAAA,CAAC6B,IAAI,CAACO,IAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAEpFC,KAAK,EAAEZ,IAAI,CAACY,KAAM;MAAAX,QAAA,EAEjBY,UAAU,CAACb,IAAI,CAACC,QAAQ;IAAC,GAHrBa,cAAc,CAACd,IAAI,CAACF,IAAI,CAAC;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIvB,CACV;EAEL,CAAC;EAED,MAAMI,gBAAgB,GAAIC,GAAW,IAAK;IACxC,OAAO9B,QAAQ,CAACK,QAAQ,KAAKyB,GAAG,GAAG;MAAEC,eAAe,EAAE,IAAIvC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwC,YAAY,EAAE;MAAEC,UAAU,EAAE;IAAmC,CAAC,GAAG;MAAEA,UAAU,EAAE;IAAmC,CAAC;EACxL,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAK,IAAK;IAClC,MAAM;MAAEC;IAAS,CAAC,GAAGD,CAAC;IACtB,MAAME,MAAM,GAAGD,QAAQ,CAACE,aAAa;IACrCD,MAAM,CAACE,KAAK,CAACC,UAAU,GAAG,IAAIhD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwC,YAAY,EAAE;EACrD,CAAC;EAED,MAAMS,gBAAgB,GAAGA,CAACN,CAAK,EAAEL,GAAW,KAAK;IAC/C,MAAM;MAAEM;IAAS,CAAC,GAAGD,CAAC;IACtB,MAAME,MAAM,GAAGD,QAAQ,CAACE,aAAa;IACrCD,MAAM,CAACE,KAAK,CAACC,UAAU,GAAIxC,QAAQ,CAACK,QAAQ,KAAKyB,GAAG,GAAG,IAAItC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwC,YAAY,EAAE,GAAG,IAAIxC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkD,cAAc,EAAE;EAChH,CAAC;EAED,MAAMC,cAAc,GAAI7B,IAAoB,IAAK;IAC/C,IAAIA,IAAI,CAACM,OAAO,EACd,oBACEnC,OAAA,CAAA2D,KAAA,CAAMC,IAAI;MACN,aAAW/B,IAAI,CAACF,IAAK;MACrB2B,KAAK,EAAEV,gBAAgB,CAACf,IAAI,CAACF,IAAI,CAAE;MACnCkC,YAAY,EAAGX,CAAC,IAAKD,gBAAgB,CAACC,CAAC,CAAE;MACzCY,YAAY,EAAGZ,CAAC,IAAKM,gBAAgB,CAACN,CAAC,EAAErB,IAAI,CAACF,IAAI,CAAE;MACtDK,SAAS,EAAEnC,MAAM,CAACkE,kBAAmB;MACrC3B,IAAI,EAAE,OAAOP,IAAI,CAACO,IAAI,KAAK,QAAQ,gBAAGpC,OAAA;QAAKgC,SAAS,EAAEH,IAAI,CAACO;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGxC,OAAA,CAAC6B,IAAI,CAACO,IAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAEpFwB,EAAE,EAAErB,cAAc,CAACd,IAAI,CAACF,IAAI,CAAE;MAAAG,QAAA,EAE7BpB,QAAQ,GAAG,6BAA6B,GAAGmB,IAAI,CAACY;IAAK,GAHjDE,cAAc,CAACd,IAAI,CAACF,IAAI,CAAC;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIrB,CAAC;IAEhB,OACEX,IAAI,CAACE,UAAU,iBACb/B,OAAA,CAAA2D,KAAA,CAAMC,IAAI;MACN,aAAW/B,IAAI,CAACF,IAAK;MACrB2B,KAAK,EAAEV,gBAAgB,CAACf,IAAI,CAACF,IAAI,CAAE;MACnCkC,YAAY,EAAGX,CAAC,IAAKD,gBAAgB,CAACC,CAAC,CAAE;MACzCY,YAAY,EAAGZ,CAAC,IAAKM,gBAAgB,CAACN,CAAC,EAAErB,IAAI,CAACF,IAAI,CAAE;MACtDK,SAAS,EAAEnC,MAAM,CAACoE,UAAW;MAC7BC,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAACG,IAAI,CAACF,IAAI,CAAE;MACpCS,IAAI,EACFP,IAAI,CAACsC,QAAQ,IAAI1D,kBAAkB,IAAIC,QAAQ,gBAC7CV,OAAA;QAAA8B,QAAA,gBACE9B,OAAA,CAAC6B,IAAI,CAACO,IAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACbxC,OAAA;UAAKgC,SAAS,EAAEnC,MAAM,CAACuE,+BAAgC;UAAAtC,QAAA,EAAErB;QAAkB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CAAC,gBAENxC,OAAA,CAAC6B,IAAI,CAACO,IAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAEf;MAAAV,QAAA,EAGAD,IAAI,CAACsC,QAAQ,IAAI1D,kBAAkB,IAAI,CAACC,QAAQ,gBAC/CV,OAAA;QAAKgC,SAAS,EAAC,wBAAwB;QAAAF,QAAA,GACpCD,IAAI,CAACY,KAAK,eACXzC,OAAA;UAAKgC,SAAS,EAAEnC,MAAM,CAACwE,qBAAsB;UAAAvC,QAAA,EAAErB;QAAkB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,GAENX,IAAI,CAACY;IACN,GATIE,cAAc,CAACd,IAAI,CAACF,IAAI,CAAC;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUrB,CACZ;EAEL,CAAC;EAED,MAAME,UAAU,GAAIlB,IAAsB,IAAK;IAC7C,OAAOA,IAAI,CAAC8C,GAAG,CAAEzC,IAAI,IAAMA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACyC,MAAM,GAAG,CAAC,GAAG3C,aAAa,CAACC,IAAI,CAAC,GAAG6B,cAAc,CAAC7B,IAAI,CAAE,CAAC;EACrH,CAAC;EAED,oBACE7B,OAAA,CAACC,KAAK;IAACuE,OAAO,EAAE,IAAK;IAACC,WAAW;IAACC,SAAS,EAAEhE,QAAS;IAACiE,UAAU,EAAC,IAAI;IAACC,cAAc,EAAC,IAAI;IAAC5C,SAAS,EAAE,eAAgB;IAAC6C,KAAK,EAAC,KAAK;IAAA/C,QAAA,eAChI9B,OAAA;MAAKgC,SAAS,EAAEnC,MAAM,CAACiF,sBAAuB;MAACxB,KAAK,EAAE;QAACR,eAAe,EAAE,IAAIvC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkD,cAAc;MAAE,CAAE;MAAA3B,QAAA,gBACnG9B,OAAA;QAAKsD,KAAK,EAAE;UAAEyB,MAAM,EAAE;QAAG,CAAE;QAAAjD,QAAA,eACzB9B,OAAA,CAAAgF,OAAA;UAAQd,OAAO,EAAEzC,OAAQ;UAAC6B,KAAK,EAAE;YAAE2B,QAAQ,EAAE,UAAU;YAAEC,KAAK,EAAE,CAAC;YAAGpC,eAAe,EAAE,IAAIvC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4E,aAAa;UAAG,CAAE;UAACnD,SAAS,EAAEnC,MAAM,CAACuF,+BAAgC;UAAAtD,QAAA,EAClKpB,QAAQ,gBAAGV,OAAA,CAAClB,kBAAkB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxC,OAAA,CAACnB,gBAAgB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNxC,OAAA,CAAA2D,KAAA;QAAM0B,IAAI,EAAC,QAAQ;QAACrD,SAAS,EAAEnC,MAAM,CAACyF,cAAe;QAACC,YAAY,EAAEtE,cAAe;QAAAa,QAAA,EAChFY,UAAU,CAACpC,IAAI;MAAC;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACPxC,OAAA;QAAKgC,SAAS,EAAEnC,MAAM,CAAC2F,uCAAwC;QAAA1D,QAAA,eAC7D9B,OAAA,CAACZ,WAAW;UACV8E,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAAC,iBAAiB,CAAE;UAC5C+D,UAAU,EAAC,SAAS;UACpBC,QAAQ,EAAC,OAAO;UAChB1D,SAAS,EAAEnC,MAAM,CAAC8F,6BAA8B;UAAA7D,QAAA,gBAEhD9B,OAAA,CAACX,gBAAgB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC9B,QAAQ,IAAI,iBAAiB;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACnC,EAAA,CAtJIF,QAAQ;EAAA,QAImBpB,cAAc,EAG5BY,WAAW,EACbC,WAAW,EAGUE,8BAA8B;AAAA;AAAA8F,EAAA,GAX9DzF,QAAQ;AAwJd,MAAMgB,eAAe,GAAGA,CAAC0E,MAAwB,EAAE9E,QAAgB,KAAK;EACtE;EACA,IAAIA,QAAQ,KAAK,iBAAiB,EAAE;IAClC,OAAO,EAAE;EACX;EAEA,MAAM+E,iBAA2B,GAAG,EAAE;EACtC,MAAMC,qBAAqB,GAAGA,CAACC,WAA6B,EAAEC,aAAqB,KAAK;IACtF,IAAID,WAAW,EAAE;MACf,KAAK,MAAME,KAAK,IAAIF,WAAW,EAAE;QAC/B,MAAMG,YAAY,GAAGzG,SAAS,CAC5B;UACEiC,IAAI,EAAEuE,KAAK,CAACvE,IAAI;UAChByE,GAAG,EAAEF,KAAK,CAACG;QACb,CAAC,EACDJ,aACF,CAAC;QACD,IAAIE,YAAY,EAAE;UAChBL,iBAAiB,CAACQ,IAAI,CAAC3D,cAAc,CAACuD,KAAK,CAACvE,IAAI,CAAC,CAAC;QACpD;QACA,IAAIwE,YAAY,IAAID,KAAK,CAACpE,QAAQ,EAAE;UAClCiE,qBAAqB,CAACG,KAAK,CAACpE,QAAQ,EAAEmE,aAAa,CAAC;QACtD;MACF;IACF;EACF,CAAC;EAEDF,qBAAqB,CAACF,MAAM,EAAE9E,QAAQ,CAAC;EACvC,OAAO+E,iBAAiB;AAC1B,CAAC;AAED,MAAMnD,cAAc,GAAIE,GAAW,IAAK;EACtC,OAAOA,GAAG,CAAC0D,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAChC,CAAC;AAED,eAAerG,QAAQ;AAAC,IAAAyF,EAAA;AAAAa,YAAA,CAAAb,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}