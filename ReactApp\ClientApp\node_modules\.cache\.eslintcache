[{"D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\index.tsx": "1", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\serviceWorker.ts": "2", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\App.tsx": "3", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\store\\store.ts": "4", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\index.ts": "5", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\appSlice.ts": "6", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileApiSlice.ts": "7", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\sitesApiSlice.ts": "8", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\userApiSlice.ts": "9", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\changeUrl.ts": "10", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\firmApiSlice.ts": "11", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileManagementApiSlice.ts": "12", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\OktaAuthClient.ts": "13", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\AppInitialization.tsx": "14", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\RouteSwitch.tsx": "15", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\uploaderSlice.ts": "16", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\index.ts": "17", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\Constants\\index.ts": "18", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\endpoints.ts": "19", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\logger\\index.ts": "20", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\uploadService.ts": "21", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\interceptorsSlice.ts": "22", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatDownloadFileName.ts": "23", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\httpVerbs.ts": "24", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useAppSelector.ts": "25", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\PrivateRoute.tsx": "26", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\index.ts": "27", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\LoginCallbackPage.tsx": "28", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\antNotifications\\index.ts": "29", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\index.tsx": "30", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Logout\\index.tsx": "31", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TenentSelectionPage\\index.tsx": "32", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PageNotFound\\index.tsx": "33", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\MasterLayout\\index.tsx": "34", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\index.tsx": "35", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\index.tsx": "36", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\index.ts": "37", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\CommonLayout\\index.tsx": "38", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useWindowDimensions.ts": "39", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\OktaError\\index.tsx": "40", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\types.ts": "41", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\SideMenuContainer.tsx": "42", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileCard\\FileCard.tsx": "43", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\utils\\confirmDiscard.tsx": "44", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\index.tsx": "45", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Header\\index.tsx": "46", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageTitle\\index.tsx": "47", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Login\\index.tsx": "48", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageContent\\index.tsx": "49", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\index.tsx": "50", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Logout\\index.tsx": "51", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TenatSelection\\index.tsx": "52", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Breadcrumbs\\index.tsx": "53", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\CustomModal\\index.tsx": "54", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinityList\\index.tsx": "55", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\validators\\index.ts": "56", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SiteSelection\\index.tsx": "57", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\FilterArea\\index.tsx": "58", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\FileDetailsModal\\index.tsx": "59", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\FilterArea\\index.tsx": "60", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useLocalStorage.ts": "61", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\menus\\menuConfig.ts": "62", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\statusCodes.ts": "63", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\utils\\mapMenuConfig.ts": "64", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\constants\\errors.ts": "65", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useUploader.ts": "66", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\index.tsx": "67", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\index.tsx": "68", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\DragAndDrop\\index.tsx": "69", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useFileList.ts": "70", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\UploaderSubmitContainer.tsx": "71", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\array\\index.ts": "72", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\regex\\index.ts": "73", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinitySelect\\index.tsx": "74", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatSize.ts": "75", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\ProgressingFile.tsx": "76", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\index.tsx": "77", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\FileList.tsx": "78", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Search\\index.tsx": "79", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PortalControls\\index.tsx": "80"}, {"size": 938, "mtime": 1748258178144, "results": "81", "hashOfConfig": "82"}, {"size": 5315, "mtime": 1748258178144, "results": "83", "hashOfConfig": "82"}, {"size": 1649, "mtime": 1748349186770, "results": "84", "hashOfConfig": "82"}, {"size": 2744, "mtime": 1748258178130, "results": "85", "hashOfConfig": "82"}, {"size": 63, "mtime": 1748258178144, "results": "86", "hashOfConfig": "82"}, {"size": 4318, "mtime": 1748258178038, "results": "87", "hashOfConfig": "82"}, {"size": 8229, "mtime": 1748258178038, "results": "88", "hashOfConfig": "82"}, {"size": 981, "mtime": 1748258178038, "results": "89", "hashOfConfig": "82"}, {"size": 1033, "mtime": 1748258178038, "results": "90", "hashOfConfig": "82"}, {"size": 694, "mtime": 1748258178038, "results": "91", "hashOfConfig": "82"}, {"size": 761, "mtime": 1748258178038, "results": "92", "hashOfConfig": "82"}, {"size": 2369, "mtime": 1748258178038, "results": "93", "hashOfConfig": "82"}, {"size": 671, "mtime": 1748258178144, "results": "94", "hashOfConfig": "82"}, {"size": 4701, "mtime": 1748258178049, "results": "95", "hashOfConfig": "82"}, {"size": 1411, "mtime": 1748258178130, "results": "96", "hashOfConfig": "82"}, {"size": 943, "mtime": 1748258178069, "results": "97", "hashOfConfig": "82"}, {"size": 1083, "mtime": 1748354902431, "results": "98", "hashOfConfig": "82"}, {"size": 479, "mtime": 1748258178130, "results": "99", "hashOfConfig": "82"}, {"size": 2123, "mtime": 1748258178144, "results": "100", "hashOfConfig": "82"}, {"size": 2643, "mtime": 1748258178144, "results": "101", "hashOfConfig": "82"}, {"size": 1242, "mtime": 1748258178038, "results": "102", "hashOfConfig": "82"}, {"size": 803, "mtime": 1748258178038, "results": "103", "hashOfConfig": "82"}, {"size": 265, "mtime": 1748258178144, "results": "104", "hashOfConfig": "82"}, {"size": 158, "mtime": 1748258178144, "results": "105", "hashOfConfig": "82"}, {"size": 286, "mtime": 1748258178112, "results": "106", "hashOfConfig": "82"}, {"size": 1018, "mtime": 1748258178144, "results": "107", "hashOfConfig": "82"}, {"size": 2349, "mtime": 1748258178144, "results": "108", "hashOfConfig": "82"}, {"size": 639, "mtime": 1748258178112, "results": "109", "hashOfConfig": "82"}, {"size": 1647, "mtime": 1748258178130, "results": "110", "hashOfConfig": "82"}, {"size": 177, "mtime": 1748258178112, "results": "111", "hashOfConfig": "82"}, {"size": 270, "mtime": 1748258178112, "results": "112", "hashOfConfig": "82"}, {"size": 314, "mtime": 1748258178130, "results": "113", "hashOfConfig": "82"}, {"size": 936, "mtime": 1748258178112, "results": "114", "hashOfConfig": "82"}, {"size": 2781, "mtime": 1748258178112, "results": "115", "hashOfConfig": "82"}, {"size": 21733, "mtime": 1748258178130, "results": "116", "hashOfConfig": "82"}, {"size": 12623, "mtime": 1748353101192, "results": "117", "hashOfConfig": "82"}, {"size": 2697, "mtime": 1748258178144, "results": "118", "hashOfConfig": "82"}, {"size": 420, "mtime": 1748258178112, "results": "119", "hashOfConfig": "82"}, {"size": 577, "mtime": 1748258178112, "results": "120", "hashOfConfig": "82"}, {"size": 1787, "mtime": 1748258178081, "results": "121", "hashOfConfig": "82"}, {"size": 375, "mtime": 1748258178049, "results": "122", "hashOfConfig": "82"}, {"size": 765, "mtime": 1748258178096, "results": "123", "hashOfConfig": "82"}, {"size": 2189, "mtime": 1748258178049, "results": "124", "hashOfConfig": "82"}, {"size": 592, "mtime": 1748258178071, "results": "125", "hashOfConfig": "82"}, {"size": 7544, "mtime": 1748258178067, "results": "126", "hashOfConfig": "82"}, {"size": 3956, "mtime": 1748353080980, "results": "127", "hashOfConfig": "82"}, {"size": 508, "mtime": 1748258178096, "results": "128", "hashOfConfig": "82"}, {"size": 541, "mtime": 1748258178081, "results": "129", "hashOfConfig": "82"}, {"size": 465, "mtime": 1748258178081, "results": "130", "hashOfConfig": "82"}, {"size": 3806, "mtime": 1748258178049, "results": "131", "hashOfConfig": "82"}, {"size": 1009, "mtime": 1748258178081, "results": "132", "hashOfConfig": "82"}, {"size": 2673, "mtime": 1748258178096, "results": "133", "hashOfConfig": "82"}, {"size": 2771, "mtime": 1748258178049, "results": "134", "hashOfConfig": "82"}, {"size": 888, "mtime": 1748258178049, "results": "135", "hashOfConfig": "82"}, {"size": 16198, "mtime": 1748258178071, "results": "136", "hashOfConfig": "82"}, {"size": 785, "mtime": 1748258178071, "results": "137", "hashOfConfig": "82"}, {"size": 3061, "mtime": 1748258178096, "results": "138", "hashOfConfig": "82"}, {"size": 4066, "mtime": 1748258178130, "results": "139", "hashOfConfig": "82"}, {"size": 2611, "mtime": 1748258178049, "results": "140", "hashOfConfig": "82"}, {"size": 3498, "mtime": 1748330843125, "results": "141", "hashOfConfig": "82"}, {"size": 870, "mtime": 1748258178112, "results": "142", "hashOfConfig": "82"}, {"size": 1216, "mtime": 1748258618221, "results": "143", "hashOfConfig": "82"}, {"size": 36, "mtime": 1748258178144, "results": "144", "hashOfConfig": "82"}, {"size": 865, "mtime": 1748258178096, "results": "145", "hashOfConfig": "82"}, {"size": 742, "mtime": 1748258178049, "results": "146", "hashOfConfig": "82"}, {"size": 5254, "mtime": 1748258178065, "results": "147", "hashOfConfig": "82"}, {"size": 7391, "mtime": 1748365540782, "results": "148", "hashOfConfig": "82"}, {"size": 2958, "mtime": 1748258178049, "results": "149", "hashOfConfig": "82"}, {"size": 2859, "mtime": 1748258178049, "results": "150", "hashOfConfig": "82"}, {"size": 2113, "mtime": 1748258178065, "results": "151", "hashOfConfig": "82"}, {"size": 2037, "mtime": 1748258178096, "results": "152", "hashOfConfig": "82"}, {"size": 657, "mtime": 1748258178144, "results": "153", "hashOfConfig": "82"}, {"size": 764, "mtime": 1748258178144, "results": "154", "hashOfConfig": "82"}, {"size": 7602, "mtime": 1748258178081, "results": "155", "hashOfConfig": "82"}, {"size": 590, "mtime": 1748258178144, "results": "156", "hashOfConfig": "82"}, {"size": 1481, "mtime": 1748258178049, "results": "157", "hashOfConfig": "82"}, {"size": 2970, "mtime": 1748258178112, "results": "158", "hashOfConfig": "82"}, {"size": 3476, "mtime": 1748258178096, "results": "159", "hashOfConfig": "82"}, {"size": 654, "mtime": 1748258178096, "results": "160", "hashOfConfig": "82"}, {"size": 2168, "mtime": 1748357786864, "results": "161", "hashOfConfig": "82"}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, "mels26", {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "169", "usedDeprecatedRules": "165"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "185", "usedDeprecatedRules": "165"}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "195", "usedDeprecatedRules": "165"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "202", "usedDeprecatedRules": "165"}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "209", "usedDeprecatedRules": "165"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "213", "usedDeprecatedRules": "165"}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "247", "usedDeprecatedRules": "165"}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "272", "usedDeprecatedRules": "165"}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "276", "usedDeprecatedRules": "165"}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "280", "usedDeprecatedRules": "165"}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "284", "usedDeprecatedRules": "165"}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "291", "usedDeprecatedRules": "165"}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "295", "usedDeprecatedRules": "165"}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "311", "usedDeprecatedRules": "165"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "315", "usedDeprecatedRules": "165"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "322", "usedDeprecatedRules": "165"}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "329", "usedDeprecatedRules": "165"}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "333", "usedDeprecatedRules": "165"}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "337", "usedDeprecatedRules": "165"}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "347", "usedDeprecatedRules": "165"}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "354", "usedDeprecatedRules": "165"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "358", "usedDeprecatedRules": "165"}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "362", "usedDeprecatedRules": "165"}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "366", "usedDeprecatedRules": "165"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "373", "usedDeprecatedRules": "165"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "392", "usedDeprecatedRules": "165"}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "408", "usedDeprecatedRules": "165"}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "412", "usedDeprecatedRules": "165"}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "416", "usedDeprecatedRules": "165"}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "165"}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "423", "usedDeprecatedRules": "165"}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "427", "usedDeprecatedRules": "165"}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\index.tsx", [], [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\serviceWorker.ts", ["434", "435"], [], "// This optional code is used to register a service worker.\r\n// register() is not called by default.\r\n\r\n// This lets the app load faster on subsequent visits in production, and gives\r\n// it offline capabilities. However, it also means that developers (and users)\r\n// will only see deployed updates on subsequent visits to a page, after all the\r\n// existing tabs open on the page have been closed, since previously cached\r\n// resources are updated in the background.\r\n\r\n// To learn more about the benefits of this model and instructions on how to\r\n// opt-in, read https://bit.ly/CRA-PWA\r\n\r\nconst NOT_FOUND = 404;\r\n\r\nconst isLocalhost = Boolean(\r\n  window.location.hostname === 'localhost' ||\r\n    // [::1] is the IPv6 localhost address.\r\n    window.location.hostname === '[::1]' ||\r\n    // *********/8 are considered localhost for IPv4.\r\n    window.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)\r\n);\r\n\r\ntype Config = {\r\n  onSuccess?: (registration: ServiceWorkerRegistration) => void;\r\n  onUpdate?: (registration: ServiceWorkerRegistration) => void;\r\n};\r\n\r\nexport function register(config?: Config) {\r\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\r\n    // The URL constructor is available in all browsers that support SW.\r\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\r\n    if (publicUrl.origin !== window.location.origin) {\r\n      // Our service worker won't work if PUBLIC_URL is on a different origin\r\n      // from what our page is served on. This might happen if a CDN is used to\r\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\r\n      return;\r\n    }\r\n\r\n    window.addEventListener('load', () => {\r\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\r\n\r\n      if (isLocalhost) {\r\n        // This is running on localhost. Let's check if a service worker still exists or not.\r\n        checkValidServiceWorker(swUrl, config);\r\n\r\n        // Add some additional logging to localhost, pointing developers to the\r\n        // service worker/PWA documentation.\r\n        navigator.serviceWorker.ready.then(() => {\r\n          console.log('This web app is being served cache-first by a service ' + 'worker. To learn more, visit https://bit.ly/CRA-PWA');\r\n        });\r\n      } else {\r\n        // Is not localhost. Just register service worker\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nfunction registerValidSW(swUrl: string, config?: Config) {\r\n  navigator.serviceWorker\r\n    .register(swUrl)\r\n    .then((registration) => {\r\n      registration.onupdatefound = () => {\r\n        const installingWorker = registration.installing;\r\n        if (installingWorker == null) {\r\n          return;\r\n        }\r\n        installingWorker.onstatechange = () => {\r\n          if (installingWorker.state === 'installed') {\r\n            if (navigator.serviceWorker.controller) {\r\n              // At this point, the updated precached content has been fetched,\r\n              // but the previous service worker will still serve the older\r\n              // content until all client tabs are closed.\r\n              console.log('New content is available and will be used when all ' + 'tabs for this page are closed. See https://bit.ly/CRA-PWA.');\r\n\r\n              // Execute callback\r\n              if (config && config.onUpdate) {\r\n                config.onUpdate(registration);\r\n              }\r\n            } else {\r\n              // At this point, everything has been precached.\r\n              // It's the perfect time to display a\r\n              // \"Content is cached for offline use.\" message.\r\n              console.log('Content is cached for offline use.');\r\n\r\n              // Execute callback\r\n              if (config && config.onSuccess) {\r\n                config.onSuccess(registration);\r\n              }\r\n            }\r\n          }\r\n        };\r\n      };\r\n    })\r\n    .catch((error) => {\r\n      console.error('Error during service worker registration:', error);\r\n    });\r\n}\r\n\r\nfunction checkValidServiceWorker(swUrl: string, config?: Config) {\r\n  // Check if the service worker can be found. If it can't reload the page.\r\n  fetch(swUrl, {\r\n    headers: { 'Service-Worker': 'script' },\r\n  })\r\n    .then((response) => {\r\n      // Ensure service worker exists, and that we really are getting a JS file.\r\n      const contentType = response.headers.get('content-type');\r\n      if (response.status === NOT_FOUND || (contentType != null && contentType.indexOf('javascript') === -1)) {\r\n        // No service worker found. Probably a different app. Reload the page.\r\n        navigator.serviceWorker.ready.then((registration) => {\r\n          registration.unregister().then(() => {\r\n            window.location.reload();\r\n          });\r\n        });\r\n      } else {\r\n        // Service worker found. Proceed as normal.\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    })\r\n    .catch(() => {\r\n      console.log('No internet connection found. App is running in offline mode.');\r\n    });\r\n}\r\n\r\nexport function unregister() {\r\n  if ('serviceWorker' in navigator) {\r\n    navigator.serviceWorker.ready\r\n      .then((registration) => {\r\n        registration.unregister();\r\n      })\r\n      .catch((error) => {\r\n        console.error(error.message);\r\n      });\r\n  }\r\n}\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\App.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\store\\store.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\appSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileApiSlice.ts", ["436", "437"], [], "import changeUrl from '@app/api/changeUrl';\r\nimport { upload } from '@app/api/uploadService';\r\nimport {\r\n  DownloadFileRequest,\r\n  DownloadFileResponse,\r\n  DownloadPortalFileURIRequest,\r\n  DownloadPortalFileURIResponse,\r\n  DownloadPortalZipFileURIRequest,\r\n  DownloadPortalZipFileURIResponse,\r\n  DownloadZipFileRequest,\r\n  DownloadZipFileResponse,\r\n  GetSubmittedFilesRequest,\r\n  GetSubmittedFilesResponse,\r\n  RemoveFilesRequest,\r\n  RemoveFilesResponse,\r\n  SaveFileDataRequest,\r\n  SaveFileDataResponse,\r\n  UploadFilesRequest,\r\n  UploadFilesResponse,\r\n  SoftDeleteFilesRequest,\r\n  SoftDeleteFilesResponse\r\n} from '@app/types/FileApiSliceTypes';\r\nimport { getParameterizedUrl } from '@app/utils';\r\nimport config from '@app/utils/config';\r\nimport formatDownloadFileName from '@app/utils/files/formatDownloadFileName';\r\nimport httpVerbs from '@app/utils/http/httpVerbs';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\nimport { createApi } from '@reduxjs/toolkit/query/react';\r\nimport download from 'downloadjs';\r\nimport { baseQueryWithReAuth } from './interceptorsSlice';\r\n\r\nexport const apiSlice = createApi({\r\n  reducerPath: '/file',\r\n  baseQuery: baseQueryWithReAuth,\r\n  tagTypes: ['publishedFiles', 'submittedFiles'],\r\n  endpoints: (builder) => ({\r\n    getSubmittedFiles: builder.query<GetSubmittedFilesResponse, GetSubmittedFilesRequest>({\r\n      query: ({ siteId, options }) => ({\r\n        url: `${config.api[OperationalServiceTypes.PortalService].getSubmittedFiles}`,\r\n        params: { siteId, ...options },\r\n      }),\r\n      providesTags: ['submittedFiles'],\r\n    }),\r\n    saveFileData: builder.mutation<SaveFileDataResponse, SaveFileDataRequest>({\r\n      query: (data) => ({\r\n        url: `${config.api[OperationalServiceTypes.PortalService].portalFilesUpload}`,\r\n        method: httpVerbs.POST,\r\n        body: data,\r\n      }),\r\n      invalidatesTags: ['submittedFiles'],\r\n    }),\r\n    downloadPortalFileURI: builder.query<DownloadPortalFileURIResponse, DownloadPortalFileURIRequest>({\r\n      query: ({ fileId }) => ({\r\n        url: getParameterizedUrl(`${config.api[OperationalServiceTypes.PortalService].downloadPortalFile}`, fileId),\r\n      }),\r\n    }),\r\n    downloadPortalZipFileURI: builder.mutation<DownloadPortalZipFileURIResponse, DownloadPortalZipFileURIRequest>({\r\n      query: ({ fileIds }) => ({\r\n        url: `${config.api[OperationalServiceTypes.PortalService].downloadPortalZipFile}`,\r\n        method: httpVerbs.POST,\r\n        body: { fileIds },\r\n      }),\r\n    }),\r\n    uploadFiles: builder.mutation<UploadFilesResponse, UploadFilesRequest>({\r\n      queryFn: async ({ formData, onUploadProgress, cancelToken }, _api, _extraOptions, baseQuery) => {\r\n        const [baseUrl, path] = changeUrl(config.api[OperationalServiceTypes.FileStorageService].uploadFiles);\r\n\r\n        // return new Promise((resolve, reject) => {\r\n        //       const xhr = new XMLHttpRequest();\r\n        //       xhr.upload.addEventListener('progress', e => onUploadProgress(e));\r\n        //       xhr.addEventListener('load', () => resolve({ data: {status: xhr.status, body: xhr.responseText }}));\r\n        //       xhr.addEventListener('error', () => reject(new Error('File upload failed')));\r\n        //       xhr.addEventListener('abort', () => reject(new Error('File upload aborted')));\r\n        //       xhr.open('POST', `${baseUrl}${path}`, true);\r\n        //       xhr.setRequestHeader(\"content-type\", \"multipart/form-data; boundary=-\")\r\n        //       xhr.setRequestHeader(\"Authorization\",`Bearer ${OktaAuthClient()?.getAccessToken()}`)\r\n        //       // Array.from(files).forEach((file, index) => formData.append(index.toString(), file));\r\n        //       xhr.send(formData);\r\n        //     });\r\n        // return  new Promise((resolve, reject)=> {\r\n        //   let ajax = new XMLHttpRequest();\r\n        //\r\n        //   // add progress event to find the progress of file upload\r\n        //   ajax.upload.addEventListener(\"progress\", (ev)=>{\r\n        //     let totalSize = ev.total; // total size of the file in bytes\r\n        //     let loadedSize = ev.loaded; // loaded size of the file in bytes\r\n        //     console.log(\"Uploaded \" + loadedSize + \" bytes of \" + totalSize + \" bytes.\");\r\n        //     // calculate percentage\r\n        //     let percent = (ev.loaded / ev.total) * 100;\r\n        //   });\r\n        //   // const [baseUrl, path] = changeUrl(config.api[OperationalServiceTypes.FileStorageService].uploadFiles);\r\n        //\r\n        //   ajax.open(\"POST\", 'https://filestorage.dev.conarc.net/C8UT/api/files'); // replace with your file URL\r\n        //   ajax.setRequestHeader('Authorization', 'Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' );\r\n        //   ajax.onload = (e) =>{\r\n        //     if (ajax.status == 200) {\r\n        //       console.log('response', ajax.response, JSON.parse( ajax.response)); // JSON response\r\n        //       resolve(JSON.parse(ajax.response));\r\n        //     }\r\n        //\r\n        //   };\r\n        //   // send request to the server\r\n        //   ajax.send(formData);\r\n        // })\r\n\r\n        return upload({ formData, onUploadProgress, cancelToken });\r\n      },\r\n    }),\r\n    removeFiles: builder.mutation<RemoveFilesResponse, RemoveFilesRequest>({\r\n      query: (file) => ({\r\n        url: `${config.api[OperationalServiceTypes.FileStorageService].uploadFiles}`,\r\n        method: httpVerbs.DELETE,\r\n        body: {\r\n          siteId: file.siteId,\r\n          referenceNumbers: file.referenceNumber,\r\n        },\r\n      }),\r\n    }),\r\n\r\n    softDeleteFiles: builder.mutation<SoftDeleteFilesResponse, SoftDeleteFilesRequest>({\r\n      query: (file) => ({\r\n        url: `${config.api[OperationalServiceTypes.PortalService].softDeleteFile}`,\r\n        method: httpVerbs.DELETE,\r\n        body: {\r\n          siteId: file.siteId,\r\n          fileIds: file.fileId,\r\n        },\r\n      }),\r\n    }),\r\n\r\n    downloadFile: builder.mutation<DownloadFileResponse, DownloadFileRequest>({\r\n      queryFn: async ({ uri }, _api, _extraOptions, baseQuery) => {\r\n        const response = await baseQuery({\r\n          url: OperationalServiceTypes.FileStorageService.toString() + uri,\r\n          responseHandler: (response) => response.blob(),\r\n        });\r\n\r\n        const blob = new Blob([response.data as Blob]);\r\n        download(blob, formatDownloadFileName((response.meta as any).response));\r\n        return { data: null };\r\n      },\r\n    }),\r\n    downloadZipFile: builder.mutation<DownloadZipFileResponse, DownloadZipFileRequest>({\r\n      queryFn: async ({ uri, files }, _api, _extraOptions, baseQuery) => {\r\n        const response = await baseQuery({\r\n          url: OperationalServiceTypes.FileStorageService.toString() + uri,\r\n          responseHandler: (response) => response.blob(),\r\n          method: httpVerbs.POST,\r\n          body: { files },\r\n        });\r\n\r\n        const blob = new Blob([response.data as Blob]);\r\n        download(blob, formatDownloadFileName((response.meta as any).response));\r\n        return { data: null };\r\n      },\r\n    }),\r\n  }),\r\n});\r\n\r\nexport const {\r\n  useGetSubmittedFilesQuery,\r\n  useSaveFileDataMutation,\r\n  useUploadFilesMutation,\r\n  useRemoveFilesMutation,\r\n  useDownloadPortalZipFileURIMutation,\r\n  useLazyDownloadPortalFileURIQuery,\r\n  useDownloadFileMutation,\r\n  useDownloadZipFileMutation,\r\n  useSoftDeleteFilesMutation,\r\n} = apiSlice;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\sitesApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\userApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\changeUrl.ts", ["438"], [], "import constants from '@app/utils/Constants';\r\nlet store: any;\r\n\r\nexport const injectStore = (_store: any) => {\r\n  store = _store;\r\n};\r\n\r\nconst ChangeUrl = (path: any) => {\r\n  const state = store.getState();\r\n  const { endPoints, tenant } = state.app;\r\n  path = path.url || path;\r\n  let key = path.slice(0, path.indexOf('/api'));\r\n\r\n  if (key) {\r\n    let baseUrl = endPoints?.find((e: any) => e.serviceType.toString() == key);\r\n    if (baseUrl) {\r\n      let newBaseURL = baseUrl.entrypointAddress + '/' + tenant;\r\n      let newUrl = path.slice(path.indexOf('/'));\r\n      return [newBaseURL, newUrl];\r\n    }\r\n  }\r\n  return [constants.baseUrlPlaceholder, path];\r\n};\r\n\r\nexport default ChangeUrl;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\firmApiSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\fileManagementApiSlice.ts", ["439", "440", "441"], [], "import {\r\n  DownloadFileURIRequest,\r\n  DownloadFileURIResponse,\r\n  DownloadZipFileURIRequest,\r\n  DownloadZipFileURIResponse,\r\n  GetPublishedFileCountRequest,\r\n  GetPublishedFileCountResponse,\r\n  GetPublishedFileRequest,\r\n  GetPublishedFileResponse,\r\n} from '@app/types/FileApiSliceTypes';\r\nimport { getParameterizedUrl } from '@app/utils';\r\nimport config from '@app/utils/config';\r\nimport formatDownloadFileName from '@app/utils/files/formatDownloadFileName';\r\nimport httpVerbs from '@app/utils/http/httpVerbs';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\nimport { createApi } from '@reduxjs/toolkit/query/react';\r\nimport download from 'downloadjs';\r\nimport { baseQueryWithReAuth } from './interceptorsSlice';\r\nimport { upload } from '@app/api/uploadService';\r\n\r\nexport const apiSlice = createApi({\r\n  reducerPath: '/fileManagement',\r\n  baseQuery: baseQueryWithReAuth,\r\n  tagTypes: ['publishedFiles', 'submittedFiles'],\r\n  endpoints: (builder) => ({\r\n    getPublishedFile: builder.query<GetPublishedFileResponse, GetPublishedFileRequest>({\r\n      query: ({ siteId, options }) => ({\r\n        url: `${config.api[OperationalServiceTypes.FileManagementService].getPublishedFiles}`,\r\n        params: { siteId, ...options },\r\n      }),\r\n      providesTags: ['publishedFiles'],\r\n    }),\r\n    getPublishedFileCount: builder.query<GetPublishedFileCountResponse, GetPublishedFileCountRequest>({\r\n      query: ({ siteId }) => ({\r\n        url: `${config.api[OperationalServiceTypes.FileManagementService].getPublishedFilesCount}`,\r\n        params: { siteId },\r\n      }),\r\n      providesTags: ['publishedFiles'],\r\n    }),\r\n    downloadFileURI: builder.query<DownloadFileURIResponse, DownloadFileURIRequest>({\r\n      query: ({ fileId }) => ({\r\n        url: getParameterizedUrl(`${config.api[OperationalServiceTypes.FileManagementService].downloadFile}`, fileId),\r\n      }),\r\n    }),\r\n    downloadZipFileURI: builder.mutation<DownloadZipFileURIResponse, DownloadZipFileURIRequest>({\r\n      query: ({ fileIds }) => ({\r\n        url: `${config.api[OperationalServiceTypes.FileManagementService].downloadZipFile}`,\r\n        method: httpVerbs.POST,\r\n        body: { fileIds },\r\n      }),\r\n    }),\r\n  }),\r\n});\r\n\r\nexport const { useGetPublishedFileQuery, useLazyGetPublishedFileCountQuery, useLazyDownloadFileURIQuery, useDownloadZipFileURIMutation } = apiSlice;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\OktaAuthClient.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\AppInitialization.tsx", ["442", "443", "444", "445", "446", "447", "448"], [], "import { useLazyGetSitesInfiniteRecordsQuery } from '@app/api/sitesApiSlice';\r\nimport { useLazyGetPublishedFileCountQuery } from '@app/api/fileManagementApiSlice';\r\nimport { useLazyGetNavigationMenuQuery, useLazyGetUserPermissionsQuery, useLazyGetUserPreferenceQuery } from '@app/api/userApiSlice';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport {\r\n  selectAppInitializing,\r\n  setApiError,\r\n  setEndpoints,\r\n  setMenu,\r\n  setMenuApiError,\r\n  setPublishedFileCount,\r\n  setSelectedSite,\r\n  setTenant,\r\n  setTenantContext,\r\n  setUser\r\n} from '@app/appSlice';\r\nimport { errorNotification } from '@app/utils/antNotifications';\r\nimport constants from '@app/utils/Constants';\r\nimport { OperationalRoleTypes } from '@iris/discovery.fe.client';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport React, { useEffect } from 'react';\r\nimport { Skeleton } from 'antd';\r\nimport { DiscoveryClient } from '../../index';\r\nimport TenantSelectionPage from '@pages/TenentSelectionPage';\r\n\r\nconst AppInitialization = () => {\r\n  const {tenant, endPoints, menuItems, isInitializing, selectedSite} = useAppSelector(selectAppInitializing);\r\n\r\n  const [triggerGetSitesInfinityRecords] = useLazyGetSitesInfiniteRecordsQuery();\r\n  const [triggerGetPublishedFileCount] = useLazyGetPublishedFileCountQuery();\r\n\r\n  const dispatch = useAppDispatch();\r\n  const {authState, oktaAuth} = useOktaAuth();\r\n  const [triggerGetNavigationMenu, {isLoading, error}] = useLazyGetNavigationMenuQuery();\r\n  const [triggerGetUserPermissions, ] = useLazyGetUserPermissionsQuery();\r\n  const [triggerGetUserPreference, ] = useLazyGetUserPreferenceQuery();\r\n  let interval: NodeJS.Timeout;\r\n\r\n  useEffect(() => {\r\n    if (selectedSite) {\r\n      triggerGetPublishedFileCount({siteId: selectedSite}).then(({data}) => {\r\n        dispatch(setPublishedFileCount(Number(data?.totalRecordCount)));\r\n      });\r\n      interval = setInterval(\r\n        () =>\r\n          triggerGetPublishedFileCount({siteId: selectedSite}).then(({data}) => {\r\n            dispatch(setPublishedFileCount(Number(data?.totalRecordCount)));\r\n          }),\r\n        180000\r\n      );\r\n    }\r\n    return () => interval && clearInterval(interval);\r\n  }, [selectedSite]);\r\n\r\n  const fetchEndpointsAndUser = async (tenant: string | undefined) => {\r\n    const data = await DiscoveryClient.getBaseURL(constants.tokenKey, {\r\n      TenantCode: tenant,\r\n      RoleType: [OperationalRoleTypes.DocumentManagementRole],\r\n    });\r\n\r\n    const user = await oktaAuth.getUser();\r\n    if (data && user) {\r\n      dispatch(setEndpoints(data));\r\n      dispatch(setUser(user));\r\n    }\r\n  };\r\n\r\n  const fetchTenantContext = async (tenant: string) => {\r\n    const data = await DiscoveryClient.getTenantContexts(constants.tokenKey, tenant);\r\n    if (data.length > 0) {\r\n      dispatch(setTenantContext(data[0]));\r\n    }\r\n  };\r\n\r\n  const fetchMenusAndSites = async () => {\r\n    const {data: menus} = await triggerGetNavigationMenu();\r\n    const {data: userPermissions} = await triggerGetUserPermissions();\r\n    const {data: userPreference} = await triggerGetUserPreference();\r\n    const {data: sites} = await triggerGetSitesInfinityRecords({limit: 10, offset: 0});\r\n    if (menus && sites) {\r\n      dispatch(setMenu(menus));\r\n      if(sites.records.length == 0){\r\n        dispatch(setSelectedSite(''));\r\n      }else{\r\n        dispatch(setSelectedSite(sites.records[0].id));\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!isLoading && error) {\r\n      const errMessage = (error as any).data.message || \"An unknown error occurred\";\r\n      dispatch(setMenuApiError(errMessage));\r\n    }\r\n  }, [error]);\r\n\r\n  useEffect(() => {\r\n    let tenantIds = (authState?.accessToken?.claims.tid as Array<string>) ?? [];\r\n\r\n    if (tenantIds.length === 1 && !tenant) dispatch(setTenant(tenantIds[0]));\r\n    if (authState?.isAuthenticated && tenant) {\r\n      if (!tenantIds.find((e) => e == tenant)) dispatch(setTenant(null));\r\n\r\n      if (endPoints === null) {\r\n        fetchEndpointsAndUser(tenant).catch((e) => {\r\n          dispatch(setApiError(e.code));\r\n          errorNotification([''], 'Discovery Service Error');\r\n        });\r\n      } else if (menuItems.length === 0) {\r\n        fetchMenusAndSites().catch((e) => {\r\n          dispatch(setApiError(e.code));\r\n        });\r\n      }\r\n    }\r\n    if (endPoints && tenant) {\r\n      fetchTenantContext(tenant).catch((e) => {\r\n        dispatch(setApiError(e.code));\r\n      });\r\n    }\r\n  }, [authState?.isAuthenticated, tenant, endPoints]);\r\n\r\n  return (\r\n    <>\r\n      {!tenant && authState?.isAuthenticated && <TenantSelectionPage/>}\r\n      {isInitializing ?? <Skeleton/>}\r\n    </>\r\n  );\r\n};\r\nexport default AppInitialization;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\RouteSwitch.tsx", ["449"], [], "import React, { Suspense } from 'react';\r\nimport { Navigate, Route, Routes } from 'react-router-dom';\r\nimport RouteConfigType from '@app/types/RouteConfigType';\r\nimport { PrivateRoute } from '@app/utils/oktaAuthClient/PrivateRoute';\r\nimport LoginCallbackPage from '@pages/Login/LoginCallbackPage';\r\nimport LogoutPage from '@pages/Logout';\r\nimport PageNotFound from '@pages/PageNotFound';\r\nimport { Skeleton } from 'antd';\r\nimport MasterLayout from '@app/layouts/MasterLayout';\r\n\r\nconst RouteSwitch = (props: any) => {\r\n  if (!props.routes) {\r\n    return null;\r\n  }\r\n  return (\r\n    <Routes>\r\n      {props.routes.map((route: any) => (\r\n        <Route key={route.path} path=\"/\" element={<PrivateRoute />}>\r\n          <Route key={route.path} path=\"/\" element={<MasterLayout />}>\r\n            <Route\r\n              key={route.path}\r\n              path={route.path}\r\n              element={\r\n                <Suspense fallback={<Skeleton />}>\r\n                  <route.component />\r\n                </Suspense>\r\n              }\r\n            />\r\n          </Route>\r\n        </Route>\r\n      ))}\r\n      <Route path=\"/login/callback\" element={<LoginCallbackPage />} />\r\n      <Route path=\"/logout\" element={<LogoutPage />} />\r\n      <Route path=\"/page-not-found\" element={<PageNotFound />} />\r\n      <Route path=\"*\" element={<Navigate to=\"/page-not-found\" />} />\r\n    </Routes>\r\n  );\r\n};\r\n\r\nexport default RouteSwitch;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\uploaderSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\routes\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\Constants\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\config\\endpoints.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\logger\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\uploadService.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\api\\interceptorsSlice.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatDownloadFileName.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\httpVerbs.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useAppSelector.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\oktaAuthClient\\PrivateRoute.tsx", ["450", "451"], [], "import React, { useEffect } from 'react';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport { toRelativeUrl } from '@okta/okta-auth-js';\r\nimport { Skeleton } from 'antd';\r\nimport { Outlet } from 'react-router-dom';\r\nimport { selectAppInitializing } from '@app/appSlice';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\n\r\nexport function PrivateRoute() {\r\n  const { tenant, endPoints } = useAppSelector(selectAppInitializing);\r\n  const { oktaAuth, authState } = useOktaAuth();\r\n\r\n  useEffect(() => {\r\n    if (!authState) {\r\n      return;\r\n    }\r\n\r\n    if (!authState?.isAuthenticated) {\r\n      const originalUri = toRelativeUrl(window.location.href, window.location.origin);\r\n      oktaAuth.setOriginalUri(originalUri);\r\n      oktaAuth.signInWithRedirect();\r\n    }\r\n  }, [oktaAuth, !!authState, authState?.isAuthenticated]);\r\n\r\n  if (!authState || !authState?.isAuthenticated || !tenant) {\r\n    return <></>;\r\n  }\r\n\r\n  if (!endPoints) {\r\n    return <Skeleton />;\r\n  }\r\n  \r\n  return <Outlet />;\r\n}\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\LoginCallbackPage.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\antNotifications\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Login\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\Logout\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\TenentSelectionPage\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PageNotFound\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\MasterLayout\\index.tsx", ["452", "453", "454", "455", "456"], [], "import React from 'react';\r\nimport { <PERSON><PERSON>, But<PERSON>, Layout, Skeleton } from 'antd';\r\nimport Header from '../../components/Header';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport PageContent from '../../components/PageContent';\r\nimport SideMenuContainer from '../../components/SideMenu/SideMenuContainer';\r\nimport styles from './index.module.less';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { selectAppInitializing, selectAppState } from '@app/appSlice';\r\nimport { errorNotification } from '@app/utils/antNotifications';\r\nimport { Outlet } from 'react-router-dom';\r\nimport Breadcrumbs from '@app/components/Breadcrumbs';\r\nimport { LogoutOutlined } from '@ant-design/icons';\r\nimport { useOktaAuth } from \"@okta/okta-react\";\r\n\r\nconst MasterLayout = (props: any) => {\r\n  const {apiError, menuApiError, isInitializing, selectedSite} = useAppSelector(selectAppInitializing);\r\n  const {tenant, user, tenantContext} = useAppSelector(selectAppState);\r\n  const {oktaAuth, authState} = useOktaAuth();\r\n\r\n  if (apiError) {\r\n    errorNotification([apiError], 'App Initialization Error');\r\n  }\r\n  const logout = () => {\r\n    oktaAuth.signOut();\r\n    localStorage.clear();\r\n  };\r\n  return (\r\n    <Layout className={'yj_cp_main_layout'} style={{height: '100vh'}}>\r\n      <Header {...props} />\r\n      <Layout>\r\n        <SideMenuContainer/>\r\n        <Layout className={styles.yj_cp_masterlayout_wrapper}>\r\n          <Breadcrumbs/>\r\n          {isInitializing ? <Skeleton/> : <Outlet/>}\r\n          {(isInitializing && menuApiError && user) && <div style={{padding: '25px'}}>\r\n              <Alert\r\n                  message={`We couldn't find an account for the username ${user.preferred_username}. Contact your organization\\'s administrator for assistance `}\r\n                  type=\"warning\" action={\r\n                <Button type=\"link\" onClick={logout} icon={<LogoutOutlined style={{color: '#333333'}}/>}\r\n                        style={{margin: '5px 15px'}}>\r\n                  Logout\r\n                </Button>\r\n              }/>\r\n          </div>}\r\n          {(isInitializing && selectedSite===\"\" && user) && <div style={{padding: '25px'}}>\r\n              <Alert\r\n                  message={`We couldn't find any site  for the username  ${user.preferred_username}. Contact your organization\\'s administrator for assistance `}\r\n                  type=\"warning\" action={\r\n                <Button type=\"link\" onClick={logout} icon={<LogoutOutlined style={{color: '#333333'}}/>}\r\n                        style={{margin: '5px 15px'}}>\r\n                  Logout\r\n                </Button>\r\n              }/>\r\n          </div>}\r\n\r\n        </Layout>\r\n      </Layout>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport { PageTitle, PageContent };\r\nexport default MasterLayout;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\index.tsx", ["457", "458", "459", "460", "461"], [], "import { ControlOutlined, DeleteOutlined, DownloadOutlined, DownOutlined, SyncOutlined } from '@ant-design/icons';\r\nimport {\r\n  useDownloadFileMutation,\r\n  useDownloadPortalZipFileURIMutation,\r\n  useDownloadZipFileMutation,\r\n  useGetSubmittedFilesQuery,\r\n  useLazyDownloadPortalFileURIQuery,\r\n  useRemoveFilesMutation,\r\n  useSaveFileDataMutation,\r\n  useUploadFilesMutation,\r\n  useSoftDeleteFilesMutation\r\n} from '@app/api/fileApiSlice';\r\nimport CustomModal from '@app/components/CustomModal';\r\nimport DownloadModal from '@app/components/DownloadModal';\r\nimport { DownloadModalDownloadTypes } from '@app/components/DownloadModal/types';\r\nimport FileCard from '@app/components/FileCard/FileCard';\r\nimport FileUploader from '@app/components/FileUploader';\r\nimport { setPendingSave, setSucceededFiles } from '@app/components/FileUploader/uploaderSlice';\r\nimport InfinityList from '@app/components/InfinityList';\r\nimport SiteSelection from '@app/components/SiteSelection';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport useWindowDimensions from '@app/hooks/useWindowDimensions';\r\nimport { PageContent, PageTitle } from '@app/layouts/MasterLayout';\r\nimport { selectAppState } from '@app/appSlice';\r\nimport { FileType, SubmittedFilesType } from '@app/types/fileTypes';\r\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\r\nimport { errorNotification, infoNotification, successNotification } from '@app/utils/antNotifications';\r\nimport logger from '@app/utils/logger';\r\nimport { Button, Checkbox, Col, Divider, Dropdown, Menu, Row, Tooltip } from 'antd';\r\nimport moment from 'moment';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport FilterArea from './FilterArea';\r\nimport styles from './index.module.less';\r\nimport FileDetailsModal from '@app/components/FileUploader/FileDetailsModal';\r\nimport { FileRecord } from '@app/components/forms/UploaderSubmit/types';\r\nimport { validateTitle } from '@app/components/FileUploader/validators';\r\nimport { FileList } from '@app/components/FileUploader/types';\r\nimport confirmDiscard from '@app/components/FileUploader/utils/confirmDiscard';\r\n\r\nconst MIN_SCREEN_RESOLUTION = 1366;\r\nconst MIN_INFINITY_HEIGHT = 315;\r\nconst MAX_INFINITY_HEIGHT = 700;\r\n\r\nconst SubmittedFiles = (props: any) => {\r\n  const [sortDir, setSortDir] = React.useState('desc');\r\n  const [items, setItems] = React.useState<SubmittedFilesType[]>([]);\r\n  const [selectedFiles, setSelectedFiles] = React.useState<Array<string>>([]);\r\n  const [indeterminate, setIndeterminate] = React.useState(false);\r\n  const [selectAll, setSelectAll] = React.useState(false);\r\n  const [selectTotalFiles, setSelectTotalFiles] = React.useState(false);\r\n  const [totalFileCount, setTotalFileCount] = React.useState(0);\r\n  const { selectedSite: siteId } = useAppSelector(selectAppState);\r\n  const [showDownloadModal, setShowDownloadModal] = React.useState(false);\r\n  const [downloadType, setDownloadType] = React.useState<DownloadModalDownloadTypes | undefined>(DownloadModalDownloadTypes.individual);\r\n  const { width } = useWindowDimensions();\r\n  const [triggerDownloadPortalFileURI] = useLazyDownloadPortalFileURIQuery();\r\n  const [triggerDownloadPortalZipFileURI] = useDownloadPortalZipFileURIMutation();\r\n  const [triggerDownloadFile] = useDownloadFileMutation();\r\n  const [triggerDownloadZipFile] = useDownloadZipFileMutation();\r\n  const [triggerUploadFiles] = useUploadFilesMutation();\r\n  const [triggerRemoveFiles] = useRemoveFilesMutation();\r\n  const [triggerSoftDeleteFiles] = useSoftDeleteFilesMutation(); \r\n  const [triggerSaveFileData] = useSaveFileDataMutation();\r\n  const [uploadedFiles, setUploadedFiles] = useState<FileList>({});\r\n  const [pageOptions, setPageOptions] = useState<any>({\r\n    limit: 10,\r\n    offset: 0,\r\n  });\r\n  const { data: submittedFilesRecords, isFetching } = useGetSubmittedFilesQuery({\r\n    siteId,\r\n    options: pageOptions,\r\n  });\r\n  let navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n\r\n  useEffect(() => {\r\n    setSelectedFiles([]);\r\n  }, []);\r\n\r\n  \r\n \r\n  useEffect(() => {\r\n    if (items.length != 0) {\r\n      setIndeterminate(selectedFiles.length > 0 && selectedFiles.length < items.length);\r\n      setSelectAll(selectedFiles.length === items.length);\r\n      if (selectedFiles.length !== items.length) setSelectTotalFiles(false);\r\n    }\r\n  }, [selectedFiles, totalFileCount, items]);\r\n\r\n  const toggleSortDir = () => {\r\n    setSortDir(sortDir === 'desc' ? 'asc' : 'desc');\r\n  };\r\n\r\n  const onSelectAll = (checked: boolean) => {\r\n    setSelectAll(checked);\r\n    setSelectedFiles(checked ? items.map((item: SubmittedFilesType) => item.id) : []);\r\n  };\r\n\r\n  const handleOnDownloadModalCancel = () => {\r\n    setShowDownloadModal(false);\r\n  };\r\n\r\n  const displaydownloadModal = (downloadTypeInput: DownloadModalDownloadTypes, display: boolean) => {\r\n    setDownloadType(downloadTypeInput);\r\n    setShowDownloadModal(display);\r\n  };\r\n\r\n  const handleMenuClick = (e: any) => {\r\n    if (e.key === '1') {\r\n      if (selectedFiles.length === 1) {\r\n        downloadAndSaveFile(selectedFiles[0]);\r\n      } else {\r\n        displaydownloadModal(DownloadModalDownloadTypes.individual, true);\r\n      }\r\n    } else {\r\n      displaydownloadModal(DownloadModalDownloadTypes.zip, true);\r\n    }\r\n  };\r\n\r\n  const menu: any = [\r\n    {\r\n      label: 'Download files',\r\n      key: '1',\r\n    },\r\n  ];\r\n\r\n  if (selectedFiles.length > 1) {\r\n    menu.push({\r\n      label: 'Download as a zip files',\r\n      key: '2',\r\n    });\r\n  }\r\n\r\n  const downloadMenu = <Menu onClick={handleMenuClick} items={menu} />;\r\n\r\n  const downloadAndSaveFile = async (fileId: string, isMultiple?: boolean, isZip?: boolean, fileList?: FileType[]) => {\r\n    if (isZip) {\r\n      infoNotification([''], 'Zip is being downloaded.');\r\n      const fileIdList = [] as any;\r\n      fileList?.forEach((file) => {\r\n        fileIdList.push(file.fileId);\r\n      });\r\n\r\n      if (fileIdList.length > 0) {\r\n        try {\r\n          let { payload, uri } = await triggerDownloadPortalZipFileURI({\r\n            fileIds: fileIdList,\r\n          }).unwrap();\r\n          triggerDownloadZipFile({ files: payload, uri });\r\n        } catch (error: any) {\r\n          const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));\r\n\r\n          if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {\r\n            navigate('/forbidden');\r\n          } else {\r\n            errorNotification([''], 'Download Failed');\r\n          }\r\n\r\n          logger.error('File Management Module', 'Download Component', errorObject);\r\n        }\r\n      }\r\n      return;\r\n    }\r\n    if (!isMultiple) infoNotification([''], 'File is being downloaded.');\r\n    try {\r\n      let { data: URIData } = await triggerDownloadPortalFileURI({ fileId });\r\n      if (URIData) triggerDownloadFile(URIData);\r\n    } catch (error: any) {\r\n      const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));\r\n\r\n      if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {\r\n        navigate('/forbidden');\r\n      } else {\r\n        errorNotification([''], 'Download Failed');\r\n      }\r\n\r\n      logger.error('File Management Module', 'Download Component', errorObject);\r\n    }\r\n  };\r\n\r\n  const cardContent = (file: SubmittedFilesType) => {\r\n    return (\r\n      <div>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Size:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.size}</div>\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Uploaded at:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.uploadedAt).format('YYYY-MM-DD hh:mmA')}</div>\r\n          </Col>\r\n        </Row>\r\n        {file.status.value === 2 && (\r\n          <>\r\n            <Row>\r\n              <Col span={4}>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Accepted at:</div>\r\n              </Col>\r\n              <Col>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.acceptedAt).format('YYYY-MM-DD hh:mmA')}</div>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span={4}>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Accepted by:</div>\r\n              </Col>\r\n              <Col>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.acceptedBy?.name}</div>\r\n              </Col>\r\n            </Row>\r\n          </>\r\n        )}\r\n        {file.status.value === 3 && (\r\n          <>\r\n            <Row>\r\n              <Col span={4}>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deleted at:</div>\r\n              </Col>\r\n              <Col>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.deletedAt).format('YYYY-MM-DD hh:mmA')}</div>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span={4}>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deleted by:</div>\r\n              </Col>\r\n              <Col>\r\n                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.deletedBy?.name}</div>\r\n              </Col>\r\n            </Row>\r\n            {file.deletionNote && (\r\n              <Row>\r\n                <Col span={4}>\r\n                  <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deletion Note:</div>\r\n                </Col>\r\n                <Col>\r\n                  <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.deletionNote}</div>\r\n                </Col>\r\n              </Row>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const generateCards = (file: SubmittedFilesType) => {\r\n    return (\r\n      <FileCard\r\n        fileId={file.id}\r\n        fileType={file.type}\r\n        title={file.title}\r\n        isSelected={selectedFiles.includes(file.id)}\r\n        className={'yj_cp_submitted_fileCard_' + file.status.value}\r\n        content={cardContent(file)}\r\n        note={file.status.value === 3 ? file.deletionNote && 'Deletion Note: ' + file.deletionNote : undefined}\r\n        trailingIcons={\r\n          <Row gutter={10} justify=\"space-between\">\r\n            <Col span={12}>\r\n              <div className={styles['yj_cp_submitted_fileCard_fileStatus_' + file.status.value]}>\r\n                <p className={styles['yj_cp_submitted_fileCard_fileStatus_dot_' + file.status.value]}></p>\r\n                {file.status.name}\r\n              </div>\r\n            </Col>\r\n            <Col span={12}>\r\n              {file.status.value === 1 && (\r\n                <Row gutter={10} justify=\"end\">\r\n                  <Col>\r\n                    <DownloadOutlined\r\n                      className={styles.YJ_CP_FileCard_DownloadIcon}\r\n                      onClick={(event) => {\r\n                        downloadAndSaveFile(file.id);\r\n                        event.stopPropagation();\r\n                      }}\r\n                    />\r\n                  </Col>\r\n                  {/* <Col>\r\n                    <SyncOutlined\r\n                      className={styles.YJ_CP_FileCard_RefreshIcon}\r\n                      onClick={(event) => {\r\n                        event.stopPropagation();\r\n                      }}\r\n                    />\r\n                  </Col> */}\r\n                  <Col>\r\n                    <DeleteOutlined\r\n                      className={styles.YJ_CP_FileCard_DeleteIcon}\r\n                      onClick={(event) => {\r\n                        softDeleteFile(file.id)\r\n                        event.stopPropagation();\r\n                      }}\r\n                    />\r\n                  </Col>\r\n                </Row>\r\n              )}\r\n            </Col>\r\n          </Row>\r\n        }\r\n        onSelectChange={(isSelected: boolean) => {\r\n          \r\n          if (isSelected) {\r\n            setSelectedFiles([...selectedFiles, file.id]);\r\n          } else {\r\n            setSelectedFiles(selectedFiles.filter((id) => id !== file.id));\r\n          }\r\n          \r\n          \r\n        }}\r\n      />\r\n    );\r\n  };\r\n\r\n  const mapToFileList = (fileList: FileList): FileRecord[] => {\r\n    return Object.entries(fileList).map(([_uid, info]) => {\r\n      return {\r\n        referenceNumber: info.referenceNumber || '',\r\n        title: info.name.slice(0, info.name.lastIndexOf('.')),\r\n        checked: true,\r\n        error: validateTitle(info.name),\r\n      };\r\n    });\r\n  };\r\n\r\n  const removeFiles = () => {\r\n    \r\n    const chunckedReferencesreferences = Object.entries(uploadedFiles).map(([_, info]) => info.referenceNumber);\r\n    triggerRemoveFiles({ siteId, referenceNumber: chunckedReferencesreferences, removeChunks: true });\r\n    setUploadedFiles({});\r\n  };\r\n\r\n  const handleSoftDeleteFiles = () => {\r\n    bulkSoftDeleteFiles(selectedFiles)\r\n  }\r\n\r\n  const bulkSoftDeleteFiles = async (fileIds: any[]) => {\r\n    \r\n    triggerSoftDeleteFiles({ siteId, fileId: fileIds });\r\n  };\r\n\r\n  const softDeleteFile = async (fileId?: string) => {\r\n   \r\n    const fileIdList = [] as any;\r\n    fileIdList.push(fileId);\r\n    triggerSoftDeleteFiles({ siteId, fileId: fileIdList });\r\n  };\r\n\r\n  const removeFile = (uid: string) => {\r\n    \r\n    triggerRemoveFiles({\r\n      siteId,\r\n      referenceNumber: [uid],\r\n      removeChunks: true,\r\n    });\r\n    setUploadedFiles((files) => {\r\n      const id = Object.entries(files).filter(([_, info]) => info.referenceNumber === uid)[0];\r\n      delete files[id[0]];\r\n      return files;\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <PageTitle title={props.title}>\r\n        <SiteSelection />\r\n      </PageTitle>\r\n      <PageContent>\r\n        <FileUploader\r\n          siteId={siteId}\r\n          onUpload={(fileData) => {\r\n            const formData = new FormData();\r\n            formData.append('file', fileData.file, fileData.fileName);\r\n            formData.append('siteid', fileData.siteId);\r\n            fileData.referenceNo && formData.append('ReferenceNumber', fileData.referenceNo);\r\n            formData.append('chunkId', fileData.chunkId.toString());\r\n            formData.append('totalChunkCount', fileData.totalChunkCount.toString());\r\n            return triggerUploadFiles({ formData, onUploadProgress: fileData.onUploadProgress, cancelToken: fileData.cancelToken });\r\n          }}\r\n          onRemove={(fileData) => triggerRemoveFiles(fileData)}\r\n          onUploadComplete={(files) => {\r\n            setUploadedFiles(files);\r\n          }}\r\n        />\r\n        {Object.keys(uploadedFiles).length > 0 && (\r\n          <FileDetailsModal\r\n            siteId={siteId}\r\n            files={mapToFileList(uploadedFiles)}\r\n            onClose={() => confirmDiscard(() => removeFiles())}\r\n            onRemove={removeFile}\r\n            onSaveComplete={(successedFiles) => {\r\n              successedFiles.forEach((file) => {\r\n                const [uid] = Object.entries(uploadedFiles).find(([_fileUid, info]) => {\r\n                  return info.referenceNumber === file.referenceNumber;\r\n                })!;\r\n                setUploadedFiles(({ [uid]: _value, ...rest }: FileList) => {\r\n                  return { ...rest };\r\n                });\r\n              });\r\n            }}\r\n            onFinished={(data: any) => {\r\n              try {\r\n                dispatch(setPendingSave(true));\r\n                setPageOptions({ limit: 10, offset: 0 });\r\n                triggerSaveFileData(data).then(() => {\r\n                  successNotification([''], 'File(s) uploaded successfully.');\r\n                  setTimeout(() => {\r\n                    dispatch(setSucceededFiles(data.files || []));\r\n                    dispatch(setPendingSave(false));\r\n                  }, 500);\r\n                });\r\n              } catch (error: any) {\r\n                if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n                  errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\r\n                } else {\r\n                  errorNotification([''], 'Upload Failed.');\r\n                }\r\n                dispatch(setPendingSave(false));\r\n              }\r\n            }}\r\n          />\r\n        )}\r\n        <FilterArea\r\n          sortBy={sortDir}\r\n          onSeachByChange={(value) => console.log(value)}\r\n          onSortChange={toggleSortDir}\r\n          onSearch={(value) => console.log(value)}\r\n          onSortByChange={(value) => console.log(value)}\r\n          onStatusChange={(value) => console.log(value)}\r\n          onCategoryChange={(value) => console.log(value)}\r\n        />\r\n        <Divider />\r\n        <Row style={{ marginTop: '-15px', padding: '0 0 10px 18px' }} justify=\"space-between\">\r\n          <Col>\r\n            <Checkbox checked={selectAll} indeterminate={indeterminate} onChange={(e) => onSelectAll(e.target.checked)} />\r\n            {(() => {\r\n              if (selectedFiles.length > 0)\r\n                return (\r\n                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_SELECTED_COUNT}>\r\n                    {(() => (selectTotalFiles ? totalFileCount : selectedFiles.length))()}\r\n                    {' out of ' + totalFileCount + ' files selected'}\r\n                  </div>\r\n                );\r\n              else\r\n                return (\r\n                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_NONE_SELECTED}>\r\n                    None selected\r\n                  </div>\r\n                );\r\n            })()}{' '}\r\n            {selectedFiles.length > 0 && items.length < totalFileCount && (\r\n              <a\r\n                className={styles.YJ_CP_SELECT_TOTAL_FILES}\r\n                type=\"link\"\r\n                onClick={() => {\r\n                  onSelectAll(true);\r\n                  setSelectTotalFiles(true);\r\n                }}\r\n              >\r\n                {'Select all ' + totalFileCount + ' files'}\r\n              </a>\r\n            )}\r\n          </Col>\r\n          <Col>\r\n            <Row gutter={5}>\r\n              <Col>\r\n                <Dropdown\r\n                  trigger={['click']}\r\n                  overlay={downloadMenu}\r\n                  disabled={(!selectAll && !selectTotalFiles && selectedFiles.length === 0) || !selectedFiles.every((e) => items.find((item) => item.id === e)?.status.value === 1)}\r\n                  className={styles.yj_cp_download_btn}\r\n                >\r\n                  <Button icon={<DownloadOutlined />} >\r\n                    DOWNLOAD <DownOutlined />\r\n                  </Button>\r\n                </Dropdown>\r\n              </Col>\r\n              <Col>\r\n                <Button type=\"default\" onClick={handleSoftDeleteFiles}\r\n                disabled={(!selectAll && !selectTotalFiles && selectedFiles.length === 0) || !selectedFiles.every((e) => items.find((item) => item.id === e)?.status.value === 1)}\r\n                className={styles.yj_cp_download_btn} icon={<DeleteOutlined />}>\r\n                  Delete\r\n                </Button>\r\n              </Col>\r\n              {/* <Col>\r\n                <Button type=\"default\" disabled={true} icon={<ControlOutlined />}>\r\n                  Manage Categories\r\n                </Button>\r\n              </Col> */}\r\n            </Row>\r\n          </Col>\r\n        </Row>\r\n        <div style={{ width: '100%' }}>\r\n          {siteId && (\r\n            <InfinityList\r\n              setPaginations={(page: number, searchValue?: string | undefined) => {\r\n                setPageOptions({\r\n                  limit: 10,\r\n                  offset: page - 1,\r\n                  search: searchValue,\r\n                });\r\n              }}\r\n              heightInPx={(() => {\r\n                if (width <= MIN_SCREEN_RESOLUTION) return MIN_INFINITY_HEIGHT;\r\n                else return MAX_INFINITY_HEIGHT;\r\n              })()}\r\n              onTotalCount={(count) => setTotalFileCount(count)}\r\n              key=\"YJ-CP-SubmittedFiles\"\r\n              paginatedLimit={20}\r\n              idKeyValue=\"fileId\"\r\n              formatValue={(value: any) => generateCards(value)}\r\n              notFoundContent={'No records available.'}\r\n              grid={{ column: 1, gutter: 0 }}\r\n              onRecordsChange={(records) => setItems(records)}\r\n              data={submittedFilesRecords}\r\n              isLoading={isFetching}\r\n            />\r\n          )}\r\n        </div>\r\n      </PageContent>\r\n      {/*  Download Option Menu Modal */}\r\n      <CustomModal\r\n        visible={showDownloadModal}\r\n        title={'Download File(s)'}\r\n        size={'small'}\r\n        onCancel={handleOnDownloadModalCancel}\r\n        footer={[\r\n          <Button onClick={handleOnDownloadModalCancel} key=\"submit\" type=\"primary\">\r\n            Done\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <DownloadModal\r\n            downloadAndSaveFile={downloadAndSaveFile}\r\n            hasDownloaded={(hasDownloaded: boolean) => {\r\n              if (hasDownloaded) {\r\n                setShowDownloadModal(false);\r\n              }\r\n            }}\r\n            selectedFiles={items\r\n              .filter((item) => selectedFiles.includes(item.id))\r\n              .map(({ id: fileId, title, type }) => ({\r\n                fileId,\r\n                title,\r\n                type,\r\n              }))}\r\n            downloadType={downloadType}\r\n          />\r\n        </div>\r\n      </CustomModal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default SubmittedFiles;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\index.tsx", ["462", "463"], [], "import { DownloadOutlined, DownOutlined } from '@ant-design/icons';\r\nimport { useDownloadFileMutation, useDownloadZipFileMutation } from '@app/api/fileApiSlice';\r\nimport { useDownloadZipFileURIMutation, useLazyDownloadFileURIQuery, useGetPublishedFileQuery } from '@app/api/fileManagementApiSlice';\r\nimport CustomModal from '@app/components/CustomModal';\r\nimport DownloadModal from '@app/components/DownloadModal';\r\nimport { DownloadModalDownloadTypes } from '@app/components/DownloadModal/types';\r\nimport FileCard from '@app/components/FileCard/FileCard';\r\nimport InfinityList from '@app/components/InfinityList';\r\nimport SiteSelection from '@app/components/SiteSelection';\r\nimport { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport useWindowDimensions from '@app/hooks/useWindowDimensions';\r\nimport { PageContent, PageTitle } from '@app/layouts/MasterLayout';\r\nimport { selectAppState } from '@app/appSlice';\r\nimport { FileType, PublishedFileType } from '@app/types/fileTypes';\r\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\r\nimport { errorNotification, infoNotification } from '@app/utils/antNotifications';\r\nimport logger from '@app/utils/logger';\r\nimport { Button, Checkbox, Col, Divider, Dropdown, Menu, Row } from 'antd';\r\nimport moment from 'moment';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport FilterArea from './FilterArea';\r\nimport styles from './index.module.less';\r\n\r\nconst MIN_SCRREN_RESOLUTION = 1366;\r\nconst MIN_INFINITY_HEIGHT = 315;\r\nconst MAX_INFINITY_HEIGHT = 600;\r\nconst LIMIT = 10;\r\n\r\nconst PublishedFiles = (props: any) => {\r\n  const [sortDir, setSortDir] = useState('desc');\r\n  const [items, setItems] = useState<Array<PublishedFileType>>([]);\r\n  const [selectedFiles, setSelectedFiles] = useState<Array<string>>([]);\r\n  const [indeterminate, setIndeterminate] = useState(false);\r\n  const [selectAll, setSelectAll] = useState(false);\r\n  const [selectTotalFiles, setSelectTotalFiles] = useState(false);\r\n  const [totalFileCount, setTotalFileCount] = useState(0);\r\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\r\n  const [downloadType, setDownloadType] = useState<DownloadModalDownloadTypes | undefined>(DownloadModalDownloadTypes.individual);\r\n  const { selectedSite } = useAppSelector(selectAppState);\r\n  const { width } = useWindowDimensions();\r\n  const [triggerDownloadFile] = useDownloadFileMutation();\r\n  const [triggerDownloadZipFile] = useDownloadZipFileMutation();\r\n  const [pageOptions, setPageOptions] = useState<any>({\r\n    limit: LIMIT,\r\n    offset: 0,\r\n  });\r\n  const { data: publishedFilesRecords, isFetching } = useGetPublishedFileQuery({\r\n    siteId: selectedSite,\r\n    options: pageOptions,\r\n  });\r\n  const [triggerDownloadFileURI] = useLazyDownloadFileURIQuery();\r\n  const [triggerDownloadZipFileURI] = useDownloadZipFileURIMutation();\r\n  let navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    setSelectedFiles([]);\r\n  }, [selectedSite]);\r\n\r\n  useEffect(() => {\r\n    if (items.length != 0) {\r\n      setIndeterminate(selectedFiles.length > 0 && selectedFiles.length < items.length);\r\n      setSelectAll(selectedFiles.length === items.length);\r\n      if (selectedFiles.length !== items.length) setSelectTotalFiles(false);\r\n    }\r\n  }, [selectedFiles, totalFileCount, items]);\r\n\r\n  const toggleSortDir = () => {\r\n    setSortDir(sortDir === 'desc' ? 'asc' : 'desc');\r\n  };\r\n\r\n  const onSelectAll = (checked: boolean) => {\r\n    setSelectAll(checked);\r\n    setSelectedFiles(checked ? items.map((item: PublishedFileType) => item.fileId) : []);\r\n  };\r\n\r\n  const downloadAndSaveFile = async (fileId: string, isMultiple?: boolean, isZip?: boolean, fileList?: FileType[]) => {\r\n    if (isZip) {\r\n      infoNotification([''], 'Zip is being downloaded.');\r\n      const fileIdList = [] as any;\r\n      fileList?.forEach((file) => {\r\n        fileIdList.push(file.fileId);\r\n      });\r\n\r\n      if (fileIdList.length > 0) {\r\n        try {\r\n          let { payload, uri } = await triggerDownloadZipFileURI({\r\n            fileIds: fileIdList,\r\n          }).unwrap();\r\n          triggerDownloadZipFile({ files: payload, uri });\r\n        } catch (error: any) {\r\n          const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));\r\n\r\n          if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {\r\n            navigate('/forbidden');\r\n          } else {\r\n            errorNotification([''], 'Download Failed');\r\n          }\r\n\r\n          logger.error('File Management Module', 'Download Component', errorObject);\r\n        }\r\n      }\r\n      return;\r\n    }\r\n    if (!isMultiple) infoNotification([''], 'File is being downloaded.');\r\n    try {\r\n      let { data: URIData } = await triggerDownloadFileURI({ fileId });\r\n      if (URIData) triggerDownloadFile(URIData).unwrap();\r\n    } catch (error: any) {\r\n      const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));\r\n\r\n      if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {\r\n        navigate('/forbidden');\r\n      } else {\r\n        errorNotification([''], 'Download Failed');\r\n      }\r\n\r\n      logger.error('File Management Module', 'Download Component', errorObject);\r\n    }\r\n  };\r\n\r\n  const cardContent = (file: PublishedFileType) => {\r\n    return (\r\n      <div>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Size:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.size}</div>\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Year:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.year}</div>\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Published at:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.publishedAt).format('YYYY-MM-DD hh:mmA')}</div>\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col span={4}>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Published by:</div>\r\n          </Col>\r\n          <Col>\r\n            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.publishedBy.name}</div>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const generateCards = (file: PublishedFileType) => {\r\n    return (\r\n      <FileCard\r\n        fileId={file.fileId}\r\n        fileType={file.type}\r\n        title={file.title}\r\n        isSelected={selectedFiles.includes(file.fileId)}\r\n        content={cardContent(file)}\r\n        trailingIcons={\r\n          <DownloadOutlined\r\n            className={styles.YJ_CP_FileCard_DownloadIcon}\r\n            onClick={(event) => {\r\n              downloadAndSaveFile(file.fileId);\r\n              event.stopPropagation();\r\n            }}\r\n          />\r\n        }\r\n        onSelectChange={(isSelected) => {\r\n          if (isSelected) {\r\n            setSelectedFiles([...selectedFiles, file.fileId]);\r\n          } else {\r\n            setSelectedFiles(selectedFiles.filter((id) => id !== file.fileId));\r\n          }\r\n        }}\r\n      />\r\n    );\r\n  };\r\n\r\n  const handleMenuClick = (e: any) => {\r\n    if (e.key === '1') {\r\n      if (selectedFiles.length === 1) {\r\n        downloadAndSaveFile(selectedFiles[0]);\r\n      } else {\r\n        displaydownloadModal(DownloadModalDownloadTypes.individual, true);\r\n      }\r\n    } else {\r\n      displaydownloadModal(DownloadModalDownloadTypes.zip, true);\r\n    }\r\n  };\r\n\r\n  const menu: any = [\r\n    {\r\n      label: 'Download files',\r\n      key: '1',\r\n    },\r\n  ];\r\n\r\n  if (selectedFiles.length > 1) {\r\n    menu.push({\r\n      label: 'Download as a zip files',\r\n      key: '2',\r\n    });\r\n  }\r\n\r\n  const downloadMenu = <Menu onClick={handleMenuClick} items={menu} />;\r\n\r\n  const handleOnDownloadModalCancel = () => {\r\n    setShowDownloadModal(false);\r\n  };\r\n\r\n  const displaydownloadModal = (downloadTypeInput: DownloadModalDownloadTypes, display: boolean) => {\r\n    setDownloadType(downloadTypeInput);\r\n    setShowDownloadModal(display);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <PageTitle title={props.title}>\r\n        <SiteSelection />\r\n      </PageTitle>\r\n      <PageContent>\r\n        <FilterArea\r\n          sortBy={sortDir}\r\n          onSeachByChange={(value) => console.log(value)}\r\n          onSortChange={toggleSortDir}\r\n          onSearch={(value) => console.log(value)}\r\n          onSortByChange={(value) => console.log(value)}\r\n          onStatusChange={(value) => console.log(value)}\r\n        />\r\n        <Divider />\r\n        <Row style={{ marginTop: '-15px', padding: '0 0 10px 18px' }} justify=\"space-between\">\r\n          <Col>\r\n            <Checkbox checked={selectAll} indeterminate={indeterminate} onChange={(e) => onSelectAll(e.target.checked)} />\r\n            {(() => {\r\n              if (selectedFiles.length > 0)\r\n                return (\r\n                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_SELECTED_COUNT}>\r\n                    {(() => (selectTotalFiles ? totalFileCount : selectedFiles.length))()}\r\n                    {' out of ' + totalFileCount + ' files selected'}\r\n                  </div>\r\n                );\r\n              else\r\n                return (\r\n                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_NONE_SELECTED}>\r\n                    None selected\r\n                  </div>\r\n                );\r\n            })()}\r\n            {selectedFiles.length > 0 && items.length < totalFileCount && (\r\n              <a\r\n                className={styles.YJ_CP_SELECT_TOTAL_FILES}\r\n                type=\"link\"\r\n                onClick={() => {\r\n                  onSelectAll(true);\r\n                  setSelectTotalFiles(true);\r\n                }}\r\n              >\r\n                {'Select all ' + totalFileCount + ' files'}\r\n              </a>\r\n            )}\r\n          </Col>\r\n          <Col>\r\n            <Dropdown trigger={['click']} overlay={downloadMenu} disabled={!selectAll && !selectTotalFiles && selectedFiles.length === 0} className={styles.yj_cp_download_btn}>\r\n              <Button icon={<DownloadOutlined />}>\r\n                DOWNLOAD <DownOutlined />\r\n              </Button>\r\n            </Dropdown>\r\n          </Col>\r\n        </Row>\r\n        <div style={{ width: '100%' }}>\r\n          {selectedSite && (\r\n            <InfinityList\r\n              setPaginations={(page: number, searchValue?: string | undefined) => {\r\n                setPageOptions({\r\n                  limit: LIMIT,\r\n                  offset: page - 1,\r\n                  search: searchValue,\r\n                });\r\n              }}\r\n              heightInPx={(() => {\r\n                if (width <= MIN_SCRREN_RESOLUTION) return MIN_INFINITY_HEIGHT;\r\n                else return MAX_INFINITY_HEIGHT;\r\n              })()}\r\n              onTotalCount={(count) => setTotalFileCount(count)}\r\n              key=\"YJ-CP-PublishedFiles\"\r\n              paginatedLimit={20}\r\n              idKeyValue=\"fileId\"\r\n              formatValue={(value: PublishedFileType) => generateCards(value)}\r\n              notFoundContent={'No records available.'}\r\n              grid={{ column: 1, gutter: 0 }}\r\n              onRecordsChange={(records) => setItems(records)}\r\n              data={publishedFilesRecords}\r\n              isLoading={isFetching}\r\n            />\r\n          )}\r\n        </div>\r\n      </PageContent>\r\n      {/*  Download Option Menu Modal */}\r\n      <CustomModal\r\n        visible={showDownloadModal}\r\n        title={'Download File(s)'}\r\n        size={'small'}\r\n        onCancel={handleOnDownloadModalCancel}\r\n        footer={[\r\n          <Button onClick={handleOnDownloadModalCancel} key=\"submit\" type=\"primary\">\r\n            Done\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <DownloadModal\r\n            downloadAndSaveFile={downloadAndSaveFile}\r\n            hasDownloaded={(hasDownloaded: boolean) => {\r\n              if (hasDownloaded) {\r\n                setShowDownloadModal(false);\r\n              }\r\n            }}\r\n            selectedFiles={items.filter((item) => selectedFiles.includes(item.fileId))}\r\n            downloadType={downloadType}\r\n          />\r\n        </div>\r\n      </CustomModal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PublishedFiles;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\index.ts", ["464"], [], "import axios from 'axios';\r\nimport HTTPRequestConfig from './interfaces/RequestConfig';\r\nimport httpVerbsExport from './httpVerbs';\r\nimport logger from '../logger';\r\nimport constants from '../Constants';\r\nimport { store } from '@app/store/store';\r\n\r\nexport const ERROR_TYPE_CANCEL = 'ERR_CANCELED';\r\n\r\nconst setHeaders = (customHeaders: any) => {\r\n  const client = axios.create({ baseURL: constants.baseUrlPlaceholder });\r\n\r\n  const access_token = JSON.parse(localStorage.getItem(constants.tokenKey) ?? '')?.accessToken;\r\n  //common headers\r\n  client.defaults.headers.common['Authorization'] = `Bearer ${access_token?.accessToken}`;\r\n  // client.defaults.headers.common[\"Content-Type\"] = \"application/json\";\r\n  // client.defaults.headers.common[\"Cache-Control\"] = \"no-cache\";\r\n  // client.defaults.headers.common[\"Pragma\"] = \"no-cache\";\r\n  // client.defaults.headers.common[\"Expires\"] = \"0\";\r\n\r\n  //custom header override\r\n  // eg :- {authorization:\"abcd123\",Content-Type:\"something\"}\r\n  for (let header in customHeaders) {\r\n    if (header === 'authorization') {\r\n      client.defaults.headers.common['Authorization'] = `Bearer ${access_token?.accessToken}`;\r\n    }\r\n  }\r\n\r\n  return client;\r\n};\r\n\r\nconst changeUrl = (req: HTTPRequestConfig) => {\r\n  let path: string;\r\n  try {\r\n    path = new URL(req.url ?? '').pathname.slice(1);\r\n    req.url = path;\r\n  } catch (error) {\r\n    path = req.url ?? '';\r\n  }\r\n  const state = store.getState();\r\n\r\n  const { endPoints, tenant } = state.app;\r\n\r\n  let key = path.slice(0, path.indexOf('/api'));\r\n\r\n  if (key) {\r\n    let baseUrl = endPoints?.find((e) => e.serviceType.toString() == key);\r\n    if (baseUrl) {\r\n      req.baseURL = baseUrl.entrypointAddress + '/' + tenant;\r\n      req.url = path.slice(path.indexOf('/'));\r\n    }\r\n  }\r\n};\r\n\r\nconst axiosApiClient = async (options: HTTPRequestConfig, customHeaders?: any, serviceType?: String) => {\r\n  const client = setHeaders(customHeaders ? customHeaders : {});\r\n\r\n  changeUrl(options);\r\n\r\n  const onSuccess = (data: any): any => {\r\n    return data;\r\n  };\r\n  const onError = (error: any): any => {\r\n    logger.error('YJ Client Portal', 'http', error);\r\n    if (error.response) {\r\n      console.error('Status:', error.response.status);\r\n      console.error('Data:', error.response.data);\r\n      console.error('Headers:', error.response.headers);\r\n    } else {\r\n      console.error('Error Message:', error.message);\r\n    }\r\n    throw error?.response?.data ? error.response.data : error;\r\n  };\r\n\r\n  try {\r\n    const data = await client(options);\r\n    return onSuccess(data);\r\n  } catch (error) {\r\n    return onError(error);\r\n  }\r\n};\r\n\r\nexport const httpVerbs = httpVerbsExport;\r\nexport default axiosApiClient;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\layouts\\CommonLayout\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useWindowDimensions.ts", ["465"], [], "import { useState, useEffect } from 'react';\r\n\r\nfunction getWindowDimensions() {\r\n  const { innerWidth: width, innerHeight: height } = window;\r\n  return {\r\n    width,\r\n    height,\r\n  };\r\n}\r\n\r\nexport default () => {\r\n  const [windowDimensions, setWindowDimensions] = useState(getWindowDimensions());\r\n  useEffect(() => {\r\n    function handleResize() {\r\n      setWindowDimensions(getWindowDimensions());\r\n    }\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n  return windowDimensions;\r\n};\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\OktaError\\index.tsx", ["466"], [], "import { useOktaAuth } from '@okta/okta-react';\r\nimport { Button, Col, Row } from 'antd';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { AppErrorProps } from './types';\r\n\r\nconst AppError = (props: AppErrorProps) => {\r\n  const { message, showContactAdmin, autoLogoutTime } = props;\r\n  const { oktaAuth } = useOktaAuth();\r\n  const [timer, setTimer] = useState(autoLogoutTime ?? 0);\r\n\r\n  const logout = () => {\r\n    oktaAuth.signOut();\r\n    localStorage.clear();\r\n  };\r\n  const changeTimer = () => {\r\n    setTimer((timer) => (timer > 0 ? timer - 1 : 0));\r\n  };\r\n  let interval: NodeJS.Timeout | undefined;\r\n\r\n  useEffect(() => {\r\n    if (autoLogoutTime) {\r\n      setTimeout(logout, autoLogoutTime * 1000);\r\n      interval = setInterval(changeTimer, 1000);\r\n    }\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  return (\r\n    <Row justify=\"center\" align=\"middle\" style={{ minHeight: 'calc(100vh - 65px)' }}>\r\n      <Col className=\"yj_cp_error_banner\">\r\n        <Row gutter={20} justify=\"center\" align=\"middle\">\r\n          <Col span=\"24\" style={{ textAlign: 'center', marginBottom: '10px' }}>\r\n            {'Error : ' + message}\r\n          </Col>\r\n          {showContactAdmin && (\r\n            <Col span=\"24\" style={{ textAlign: 'center', marginBottom: '10px' }}>\r\n              Please contact your system administrator.\r\n            </Col>\r\n          )}\r\n          {autoLogoutTime && (\r\n            <Col span=\"24\" style={{ textAlign: 'center', marginBottom: '10px' }}>\r\n              You will be logged out in {timer} seconds.\r\n            </Col>\r\n          )}\r\n          <Button type=\"primary\" onClick={logout} className=\"yj_cp_error_btns\">\r\n            Logout\r\n          </Button>\r\n        </Row>\r\n      </Col>\r\n    </Row>\r\n  );\r\n};\r\n\r\nexport default AppError;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\types.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\SideMenuContainer.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileCard\\FileCard.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\utils\\confirmDiscard.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\index.tsx", ["467", "468", "469"], [], "import { useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { selectAppState } from '@app/appSlice';\r\nimport { warningNotification } from '@app/utils/antNotifications';\r\nimport { ERROR_TYPE_CANCEL } from '@app/utils/http/index';\r\nimport { ERROR_CODE_409 } from '@app/utils/http/statusCodes';\r\nimport logger from '@app/utils/logger';\r\nimport { notification } from 'antd';\r\nimport { RcFile, UploadFile } from 'antd/lib/upload/interface';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { FileRecord } from '../forms/UploaderSubmit/types';\r\nimport {\r\n  FILE_INVALID,\r\n  FILE_IS_TOO_LARGE,\r\n  LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,\r\n  RETRYING_MESSAGE,\r\n  TOO_MANY_FILES,\r\n  UPLOAD_FAILED_MESSAGE\r\n} from './constants/errors';\r\nimport DragAndDrop from './DragAndDrop';\r\nimport useUploader from './hooks/useUploader';\r\nimport { FileUploaderProps } from './types';\r\nimport UploadProgress from './UploadProgress';\r\nimport confirmDiscard from './utils/confirmDiscard';\r\nimport { validateFileName, validateSize } from './validators';\r\n\r\nconst MAX_RETRY_COUNT = 3;\r\nexport const FULL_PERCENTAGE = 100;\r\nexport const MAXIMUM_FILE_COUNT = 20;\r\n\r\nconst FileUploader = ({siteId, onCloseModal = () => null, onUpload, onRemove, onUploadComplete}: FileUploaderProps) => {\r\n  const {files, action} = useUploader();\r\n  const {\r\n    add,\r\n    updatePercent,\r\n    updateError,\r\n    increaseChunkCounter,\r\n    isCompleted,\r\n    changeToBeProceedStatus,\r\n    incrementRetryCount,\r\n    resetPercent,\r\n    remove,\r\n    removeAll,\r\n    retry,\r\n    retryAll,\r\n    setReferenceNumber,\r\n  } = action;\r\n  const isShowingError = useRef(false);\r\n  const filesCount = Object.keys(files).length;\r\n  const hasUploadingFiles = !!filesCount;\r\n  const hasUrlUploadPermission = useAppSelector(selectAppState).fileAreaSettings.urlFileUpload;\r\n\r\n  useEffect(() => {\r\n    Object.entries(files).forEach(([uid, info]) => {\r\n      if (!info.toBeProceed) {\r\n        fileUpload(uid);\r\n      }\r\n    });\r\n  }, [files]);\r\n\r\n  const fileUpload = (uid: string) => {\r\n    if (files[uid].error?.message === FILE_IS_TOO_LARGE) {\r\n      return;\r\n    }\r\n    const {onSuccess, onError, file, onProgress} = files[uid].uploadOptions;\r\n    const upFile = file.slice(files[uid].chunkStartSize, files[uid].chunkEndSize) as File;\r\n    changeToBeProceedStatus(uid, true);\r\n    onUpload({\r\n      fileName: file.name,\r\n      file: upFile,\r\n      siteId: siteId,\r\n      referenceNo: files[uid]?.referenceNumber,\r\n      chunkId: files[uid]?.chunkCounter,\r\n      totalChunkCount: files[uid]?.chunkCount,\r\n      onUploadProgress: (event: any) => {\r\n        const currentProgress = (event.loaded / event.total / files[uid].chunkCount) * FULL_PERCENTAGE;\r\n        const percent = (files[uid].percent as number) + currentProgress;\r\n\r\n        onProgress({percent}, file);\r\n        updatePercent(uid, percent, currentProgress);\r\n      },\r\n      cancelToken: files[uid].cancelTokenSource?.token,\r\n    })\r\n      .then((res: any) => {\r\n        if (res.error) throw res.error;\r\n        setReferenceNumber(uid, res.data.referenceNumber);\r\n        if (files[uid].chunkCounter === files[uid].chunkCount) {\r\n          isCompleted(uid, true);\r\n          onSuccess(res.data.success, file);\r\n        } else {\r\n          increaseChunkCounter(uid);\r\n        }\r\n      })\r\n      .catch((error: any) => {\r\n        if (error.code !== ERROR_TYPE_CANCEL) {\r\n          if (files[uid].retryCount < MAX_RETRY_COUNT) {\r\n            if (files[uid].retryCount === 1) {\r\n              warningNotification([''], RETRYING_MESSAGE);\r\n            }\r\n            incrementRetryCount(uid);\r\n            changeToBeProceedStatus(uid, false);\r\n          } else {\r\n            resetPercent(uid);\r\n            if (error.message) {\r\n              logger.error('File Area Module', 'Uploader Component', error.message);\r\n            } else {\r\n              logger.error('File Area Module', 'Uploader Component', error);\r\n            }\r\n\r\n            if (error.statusCode && error.statusCode === ERROR_CODE_409) {\r\n              updateError(uid, {\r\n                name: LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,\r\n                message: LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,\r\n              });\r\n            } else {\r\n              updateError(uid, {\r\n                name: UPLOAD_FAILED_MESSAGE,\r\n                message: UPLOAD_FAILED_MESSAGE,\r\n              });\r\n            }\r\n            onError(error);\r\n          }\r\n        }\r\n      });\r\n  };\r\n\r\n  const requestUpload = (options: any) => {\r\n    const {file} = options;\r\n    const castedFile = file as unknown as UploadFile;\r\n    if (!validateSize(file.size)) {\r\n      add(castedFile, options, Error(FILE_IS_TOO_LARGE));\r\n      return false;\r\n    }\r\n    if (!validateFileName(file.name)) {\r\n      add(castedFile, options, Error(FILE_INVALID));\r\n      return false;\r\n    }\r\n    add(castedFile, options);\r\n    return true;\r\n  };\r\n\r\n  const onDelete = (uid: string) => {\r\n    if (!files[uid].completed) {\r\n      onRemove({\r\n        siteId,\r\n        referenceNumber: files[uid].referenceNumber ? [files[uid].referenceNumber] : [],\r\n        removeChunks: true,\r\n      });\r\n    } else {\r\n      onRemove({\r\n        siteId,\r\n        referenceNumber: files[uid].referenceNumber ? [files[uid].referenceNumber] : [],\r\n      });\r\n    }\r\n    remove(uid);\r\n  };\r\n\r\n  const onDeleteAll = () => {\r\n    confirmDiscard(() => {\r\n      const completedReferencesreferences = Object.entries(files)\r\n        .filter(([_, info]) => info.completed && info.referenceNumber)\r\n        .map(([_, info]) => info.referenceNumber);\r\n      const chunckedReferencesreferences = Object.entries(files)\r\n        .filter(([_, info]) => !info.completed && info.referenceNumber)\r\n        .map(([_, info]) => info.referenceNumber);\r\n\r\n      if (!!completedReferencesreferences.length) {\r\n        onRemove({siteId, referenceNumber: completedReferencesreferences});\r\n      }\r\n      if (!!chunckedReferencesreferences.length) {\r\n        onRemove({siteId, referenceNumber: chunckedReferencesreferences, removeChunks: true});\r\n      }\r\n      removeAll();\r\n      onCloseModal?.();\r\n    });\r\n  };\r\n\r\n  const onRetry = (uid: string) => {\r\n    if (files[uid]) {\r\n      if (files[uid].error?.message === FILE_IS_TOO_LARGE) {\r\n        updateError(uid, {\r\n          name: FILE_IS_TOO_LARGE,\r\n          message: FILE_IS_TOO_LARGE,\r\n        });\r\n      } else {\r\n        retry(uid);\r\n        fileUpload(uid);\r\n      }\r\n    }\r\n  };\r\n\r\n  const onRetryAll = () => {\r\n    retryAll();\r\n  };\r\n\r\n  const onComplete = () => {\r\n    onUploadComplete(files);\r\n    removeAll();\r\n  };\r\n\r\n  const validateFiles = (_file: RcFile, fileList: RcFile[]) => {\r\n    debugger\r\n    if (fileList.length > MAXIMUM_FILE_COUNT) {\r\n      if (!isShowingError.current) {\r\n        isShowingError.current = true;\r\n        notification.error({\r\n          message: TOO_MANY_FILES,\r\n          onClose: () => (isShowingError.current = false),\r\n          className: 'yjErrorMsg',\r\n        });\r\n      }\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <DragAndDrop\r\n        style={{display: hasUploadingFiles ? 'none' : 'block'}}\r\n        customRequest={requestUpload}\r\n        beforeUpload={validateFiles}\r\n        hasUrlUploadPermission={hasUrlUploadPermission}\r\n      />\r\n      {hasUploadingFiles && <UploadProgress files={files} onRetry={onRetry} onRetryAll={onRetryAll} onDelete={onDelete}\r\n                                            onDeleteAll={onDeleteAll} onComplete={onComplete}/>}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FileUploader;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Header\\index.tsx", ["470", "471"], [], "import React, { useEffect, useState } from 'react';\r\nimport { Button, Layout, Menu, Select, Dropdown, MenuProps } from 'antd';\r\nimport { LogoutOutlined,MoreOutlined } from '@ant-design/icons';\r\nimport { Button as KendoButton } from '@progress/kendo-react-buttons';\r\nimport { AiOutlineSetting } from 'react-icons/ai';\r\nimport styles from './index.module.less';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { selectAppState, setTenant } from '@app/appSlice';\r\nimport {  useLazyGetFirmLogoAndNameQuery } from \"@app/api/firmApiSlice\";\r\n\r\nconst Header = (props: any) => {\r\n  const [tIds, setTIds] = useState<Array<string>>([]);\r\n  const [theme, setTheme] = useState<any>();\r\n  const { tenant, user , tenantContext} = useAppSelector(selectAppState);\r\n  const { oktaAuth, authState } = useOktaAuth();\r\n  const dispatch = useAppDispatch();\r\n  let navigate = useNavigate();\r\n  const [triggerGetFirmLogoAndName] = useLazyGetFirmLogoAndNameQuery();\r\n  const tenantLogoUrl = `data:image/png;base64,${(tenantContext?.tenantIcon)}`\r\n\r\n\r\n  useEffect(() => {\r\n    if (authState?.isAuthenticated) {\r\n      setTIds((authState?.accessToken?.claims.tid as Array<string>) ?? []);\r\n      console.info(authState?.accessToken?.claims.tid )\r\n      triggerGetFirmLogoAndName().then((res)=>{\r\n        res?.data && setTheme(res.data)\r\n      })\r\n    }\r\n  }, [authState?.isAuthenticated]);\r\n\r\n  const onSelect = (val: string) => {\r\n    dispatch(setTenant(val));\r\n  };\r\n\r\n  const logout = () => {\r\n    oktaAuth.signOut();\r\n    localStorage.clear();\r\n  };\r\n  const navStyle = {\r\n    backgroundColor: `#${theme?.secondaryColor}` ,\r\n    borderBottomColor:  `#${theme?.primaryColor}` ,\r\n    borderBottomWidth: '5px',\r\n  };\r\n\r\n  const oktaAuthUrl = authState?.accessToken?.userinfoUrl\r\n  ? new URL(authState.accessToken.userinfoUrl).hostname\r\n  : undefined;\r\n\r\n  const menuItems: MenuProps['items'] = [\r\n    {\r\n      key: '1',\r\n      label: (\r\n        <a\r\n          href={`https://${oktaAuthUrl}`}\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          Manage OKTA Profile\r\n        </a>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Layout.Header className={styles.yj_cp_header} style={navStyle}>\r\n      <div onClick={() => navigate('/')} className={styles.yj_cp_firm_logo} style={{  ...(tenantContext?.tenantIcon ? { backgroundImage: `url(${tenantLogoUrl})`, backgroundSize: 'contain' } : {}) }}></div>\r\n      <div onClick={() => navigate('/')} className={styles.yj_cp_firm_name}>x{tenantContext?.tenantDisplayName}x</div>\r\n      <div style={{flexGrow:1 }}></div>\r\n      {!props.hideLogout && (<>\r\n        <KendoButton\r\n          onClick={() => navigate('/portalControls')}\r\n          style={{marginRight: \"15px\"}}\r\n          fillMode=\"flat\"\r\n          themeColor=\"base\"\r\n        >\r\n          <AiOutlineSetting className={styles.yj_cp_portal_controls_header_icon} />\r\n          Portal Controls\r\n        </KendoButton>\r\n        <Dropdown menu={{ items: menuItems }} trigger={['hover']}>\r\n          <span className={styles.yj_cp_UserName}>\r\n            {user?.name ?? ''} <MoreOutlined className=\"icon\" />\r\n          </span>\r\n        </Dropdown>\r\n\r\n          <Button type=\"link\"  onClick={logout} icon={<LogoutOutlined style={{ fontSize: '24px', color: '#FFFFFF' }} />}  style={{margin: '5px 15px' }}/>\r\n            { tIds.length > 1 && (\r\n                <div style={{ padding: '10px' }}>\r\n                  Tenant :{' '}\r\n                  <Select defaultValue={tenant} onChange={onSelect} style={{ pointerEvents: 'auto', width: '80px' }}>\r\n                    {tIds.map((e) => (\r\n                        <Select.Option value={e}>{e}</Select.Option>\r\n                    ))}\r\n                  </Select>\r\n                </div>\r\n            )}\r\n      </>\r\n      )}\r\n\r\n    </Layout.Header>\r\n  );\r\n};\r\nexport default Header;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageTitle\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Login\\index.tsx", ["472"], [], "import React, { Fragment, useEffect } from 'react';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport constants from '@app/utils/Constants';\r\n\r\nconst Login = () => {\r\n  let navigate = useNavigate();\r\n  const { oktaAuth, authState } = useOktaAuth();\r\n\r\n  useEffect(() => {\r\n    if (authState?.isAuthenticated) navigate(constants.postSigninRedirect);\r\n    else oktaAuth.signInWithRedirect();\r\n  }, [authState, oktaAuth]);\r\n\r\n  return <Fragment></Fragment>;\r\n};\r\n\r\nexport default Login;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\PageContent\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\DownloadModal\\index.tsx", ["473", "474"], [], "import { DownloadOutlined } from '@ant-design/icons/lib';\r\nimport { FileType } from '@app/types/fileTypes';\r\nimport { infoNotification } from '@app/utils/antNotifications';\r\nimport { Button, List } from 'antd';\r\nimport React, { Fragment, useCallback, useEffect, useState } from 'react';\r\nimport styles from './index.module.less';\r\nimport { DownloadModalDownloadTypes, DownloadModalProps } from './types';\r\n\r\nconst DownloadModal = ({ downloadType = DownloadModalDownloadTypes.individual, selectedFiles, downloadAndSaveFile }: DownloadModalProps) => {\r\n  const DOWNLOAD_COUNTER_TIME = 10;\r\n  const DOWNLOAD_TIMEOUT = 1000;\r\n  const [timeLeft, setTimeLeft] = useState(DOWNLOAD_COUNTER_TIME);\r\n\r\n  useEffect(() => {\r\n    if (!timeLeft) {\r\n      return () => {};\r\n    }\r\n    const intervalId = setInterval(() => {\r\n      setTimeLeft(timeLeft - 1);\r\n    }, DOWNLOAD_TIMEOUT);\r\n    return () => clearInterval(intervalId);\r\n  }, [timeLeft]);\r\n\r\n  const bulkDownload = (fileList: FileType[] | undefined) => {\r\n    infoNotification([''], 'Files are being downloaded.');\r\n    fileList?.forEach((file) => {\r\n      downloadAndSaveFile(file.fileId, true);\r\n    });\r\n  };\r\n\r\n  const triggerFileDownload = useCallback(\r\n    (timeOutdownload: number) => {\r\n      if (selectedFiles && selectedFiles.length > 0) {\r\n        downloadType === DownloadModalDownloadTypes.individual\r\n          ? setTimeout(() => {\r\n              bulkDownload(selectedFiles);\r\n            }, timeOutdownload)\r\n          : setTimeout(() => {\r\n              downloadAndSaveFile('', false, true, selectedFiles);\r\n            }, timeOutdownload);\r\n      }\r\n    },\r\n    [downloadType, selectedFiles]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const timeOutdownload = 0;\r\n    triggerFileDownload(timeOutdownload);\r\n  }, []);\r\n\r\n  return (\r\n    <div className={styles.yjModalContentWrapper}>\r\n      <Fragment>\r\n        {downloadType === DownloadModalDownloadTypes.individual ? (\r\n          <>\r\n            If your download didn't start in <span className={styles.yjDigit}>{timeLeft}</span> seconds click below\r\n            <List\r\n              itemLayout=\"horizontal\"\r\n              dataSource={selectedFiles}\r\n              renderItem={(item) =>\r\n                item && (\r\n                  <List.Item className={styles.yjDownloadItem}>\r\n                    <List.Item.Meta title={<p className={styles.yjDownloadFileName}>{`${item.title}.${item.type ? item.type.toLowerCase() : ''}`}</p>} />\r\n                    <div className={styles.yj_cp_downloadBtn}>\r\n                      <Button\r\n                        onClick={() => {\r\n                          downloadAndSaveFile(item.fileId);\r\n                        }}\r\n                        type=\"default\"\r\n                      >\r\n                        <DownloadOutlined /> Download\r\n                      </Button>\r\n                    </div>\r\n                  </List.Item>\r\n                )\r\n              }\r\n            />\r\n          </>\r\n        ) : (\r\n          <>\r\n            If your download didn't start in <span className={styles.yjDigit}>{timeLeft}</span> seconds click below\r\n            <List.Item className={styles.yjDownloadItem}>\r\n              <List.Item.Meta title={<p className={styles.yjDownloadFileName}>{selectedFiles ? `${selectedFiles[selectedFiles.length - 1]?.title}.zip` : ''}</p>} />\r\n              <div className={styles.yj_cp_downloadBtn}>\r\n                <Button\r\n                  onClick={() => {\r\n                    downloadAndSaveFile('', false, true, selectedFiles);\r\n                  }}\r\n                  type=\"default\"\r\n                >\r\n                  <DownloadOutlined /> Download\r\n                </Button>\r\n              </div>\r\n            </List.Item>\r\n          </>\r\n        )}\r\n      </Fragment>\r\n    </div>\r\n  );\r\n};\r\nexport default DownloadModal;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Logout\\index.tsx", ["475"], [], "import { useOktaAuth } from '@okta/okta-react';\r\nimport { Button, Col, Row } from 'antd';\r\nimport React, { useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Logout: React.FC = () => {\r\n  let navigate = useNavigate();\r\n  const { authState, oktaAuth } = useOktaAuth();\r\n\r\n  useEffect(() => {\r\n    if (authState?.isAuthenticated) navigate('/');\r\n  }, [authState]);\r\n\r\n  return (\r\n    <Row justify=\"center\" align=\"middle\" style={{ minHeight: 'calc(100vh - 65px)' }}>\r\n      <Col className=\"yj_cp_error_banner\">\r\n        <Row gutter={20} justify=\"center\" align=\"middle\">\r\n          <Col span=\"24\" style={{ textAlign: 'center', marginBottom: '20px' }}>\r\n            You have been logged out !\r\n          </Col>\r\n          <Col>\r\n            <Button className=\"yj_cp_error_btns\" type=\"primary\" onClick={() => oktaAuth.signInWithRedirect()}>\r\n              Login\r\n            </Button>\r\n          </Col>\r\n        </Row>\r\n      </Col>\r\n    </Row>\r\n  );\r\n};\r\n\r\nexport default Logout;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\TenatSelection\\index.tsx", ["476", "477"], [], "import { But<PERSON>, Col, Row, Select } from 'antd';\r\nimport React, { useEffect, useState } from 'react';\r\nimport styles from './index.module.less';\r\nimport Logo from '../../../styles/assets/images/Logo.png';\r\nimport UserImg from '../../../styles/assets/images/userLogo.png';\r\nimport { useOktaAuth } from '@okta/okta-react';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport { selectAppState, setTenant } from '@app/appSlice';\r\n\r\nconst TenantSelection = () => {\r\n  const [tids, setTids] = useState<Array<string>>([]);\r\n  const { authState } = useOktaAuth();\r\n  const { tenant } = useAppSelector(selectAppState);\r\n  const [selected, setSelected] = useState(tenant);\r\n  const dispatch = useAppDispatch();\r\n\r\n  useEffect(() => {\r\n    setTids((authState?.accessToken?.claims.tid as Array<string>) ?? []);\r\n  }, [authState]);\r\n\r\n  const onChange = (val: string) => setSelected(val);\r\n\r\n  const onSelect = () => {\r\n    dispatch(setTenant(selected));\r\n  };\r\n\r\n  return (\r\n    <Row justify=\"center\" className={styles.yj_tenant_selection_container}>\r\n      <Col>\r\n        <div className={styles.yj_tenant_selection}>\r\n          <img src={Logo} style={{ paddingBottom: '10px' }} />\r\n          <div style={{ paddingBottom: '10px' }}>\r\n            <Row justify=\"space-between\" align=\"middle\">\r\n              <Col span={9}>\r\n                <hr />\r\n              </Col>\r\n              <Col>\r\n                <img className={styles.yj_tenant_userLogo} src={UserImg} />\r\n              </Col>\r\n              <Col span={9}>\r\n                <hr />\r\n              </Col>\r\n            </Row>\r\n          </div>\r\n          <hr />\r\n          <div className={styles.yj_tenant_title}>Select the tenant</div>\r\n          <div className={styles.yj_tenant_dropdown}>\r\n            <Select\r\n              placeholder={'Select a tenant'}\r\n              defaultValue={tenant}\r\n              onChange={onChange}\r\n              style={{\r\n                width: '370px',\r\n                textAlign: 'left',\r\n                marginBottom: '20px',\r\n              }}\r\n            >\r\n              {tids.map((e) => (\r\n                <Select.Option value={e}>{e}</Select.Option>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n          <hr />\r\n          <Button\r\n            style={{\r\n              marginTop: '10px',\r\n              width: '370px',\r\n              marginBottom: '20px',\r\n              height: '45px',\r\n            }}\r\n            type=\"primary\"\r\n            disabled={!selected}\r\n            onClick={onSelect}\r\n          >\r\n            Done\r\n          </Button>\r\n        </div>\r\n      </Col>\r\n    </Row>\r\n  );\r\n};\r\n\r\nexport default TenantSelection;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Breadcrumbs\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\CustomModal\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinityList\\index.tsx", ["478", "479", "480", "481", "482", "483", "484", "485", "486"], ["487"], "/* eslint-disable import/no-anonymous-default-export */\r\nimport { DownOutlined } from '@ant-design/icons';\r\nimport config from '@app/utils/config';\r\nimport { Checkbox, Dropdown, Input, List, Menu, Spin } from 'antd';\r\nimport debounce from 'lodash/debounce';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { uniqueArray } from '../../utils/array';\r\nimport styles from './index.module.less';\r\nimport { InfinityListProps } from './types';\r\n\r\nconst CheckboxGroup = Checkbox.Group;\r\nconst ListItem = List.Item;\r\nconst Search = Input.Search;\r\n\r\nconst DEFAULT_VIEW_SIZE = 50;\r\nconst DEFAULT_DIV_HEIGHT = 400;\r\nconst DEFAULT_LOAD_VALUE = 5;\r\nconst DROPDOWN_SELECT_VALUE_NONE = 'Clear All';\r\nconst DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE = 'Clear Visible';\r\nconst DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE = 'Select Visible';\r\nconst DROPDOWN_SELECT_VALUE_SELECT_ALL = 'Select All';\r\nconst LIMIT = 10;\r\n\r\nexport default (props: InfinityListProps) => {\r\n  const infinityListRef = useRef({}) as any;\r\n\r\n  const [isEndOfScroll, setIsEndOfScroll] = useState<boolean>(false);\r\n\r\n  const [checkedList, setCheckedList] = useState<any[]>([]);\r\n  const [indeterminate, setIndeterminate] = useState(false);\r\n  const [checkAll, setCheckAll] = useState<boolean | undefined>();\r\n\r\n  const [items, setItems] = useState<Array<any>>([]);\r\n  const [keyValue, setKeyValue] = useState<string>('');\r\n\r\n  const [selectedDropdownValue, setSelectedDropdownValue] = useState<string | undefined>(DROPDOWN_SELECT_VALUE_NONE);\r\n\r\n  const [historyOptions, setHistoryOptions] = useState<Array<any>>([]);\r\n\r\n  // To Keep track of previously selected Value\r\n  const [previouslySelectedDropdownValue, setPreviouslySelectedDropdownValue] = useState<string | undefined>(DROPDOWN_SELECT_VALUE_NONE);\r\n  const [previouslyCheckedList, setPreviouslyCheckedList] = useState<any[] | undefined>();\r\n\r\n  // To Keep track of Counts for validation\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [pageCount, setPageCount] = useState<number>(1);\r\n  const [totalRecordCount, setTotalRecordCount] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    if (props.data) {\r\n      if (props.data.pageNumber == 1) {\r\n        setItems([]);\r\n        setIsEndOfScroll(false);\r\n        setCheckAll(false);\r\n        let res = { data: props.data };\r\n        setResData(res);\r\n        if (res?.data?.records?.length > 0) {\r\n          setItems(uniqueArray([...res?.data?.records]));\r\n          if (props.selectedList) {\r\n            const visibleElemnts = props.selectedList.filter((x) => res.data.records.some((y: any) => y.id === x));\r\n            setIndeterminate(!!visibleElemnts.length && visibleElemnts.length < res.data.records.length);\r\n            setCheckAll(visibleElemnts.length === res.data.records.length);\r\n          }\r\n        } else {\r\n          setItems([]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      let res = { data: props.data };\r\n      setResData(res);\r\n      if (res?.data?.records?.length > 0) {\r\n        setItems(uniqueArray([...items, ...res?.data?.records]));\r\n        if (checkAll) {\r\n          setIndeterminate(true);\r\n          setCheckAll(false);\r\n        } else {\r\n          const selectedListCount = props.selectedList ? props.selectedList.length : 0;\r\n\r\n          if (res.data?.records.length && selectedListCount > 0) {\r\n            setIndeterminate(true);\r\n            setCheckAll(false);\r\n          }\r\n        }\r\n        if (props.hasCheckbox && selectedDropdownValue === DROPDOWN_SELECT_VALUE_SELECT_ALL) {\r\n          setCheckedList(res.data?.records.map((item: any) => item.id));\r\n        } else if (props.hasCheckbox && selectedDropdownValue === DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE) {\r\n          setIndeterminate(true);\r\n        } else if (props.hasCheckbox && selectedDropdownValue === DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE) {\r\n          if (previouslySelectedDropdownValue === DROPDOWN_SELECT_VALUE_SELECT_ALL) {\r\n            setCheckedList(\r\n              res.data?.records.map((item: any) => {\r\n                if (!previouslyCheckedList?.includes(item.id)) {\r\n                  return item.id;\r\n                }\r\n              })\r\n            );\r\n            setIndeterminate(true);\r\n          } else {\r\n            setIndeterminate(false);\r\n          }\r\n        } else {\r\n          if (props.selectedList && props.selectedList.length > 0) {\r\n            const visibleElemnts = props.selectedList.filter((x) => res.data?.records.some((y: any) => y.id === x));\r\n\r\n            setIndeterminate(!!visibleElemnts.length && visibleElemnts.length < res.data?.records.length);\r\n            setCheckAll(visibleElemnts.length === res.data?.records.length);\r\n          }\r\n          setCheckedList(props.checkedList ? props.checkedList.map((x: any) => x.id) : []);\r\n        }\r\n      } else {\r\n        setItems([]);\r\n      }\r\n    }\r\n  }, [props.data]);\r\n\r\n  useEffect(() => {\r\n    const deletedList = props.deletedList ? props.deletedList : [];\r\n    const selectedList = props.selectedList ? props.selectedList.filter((x) => !deletedList.includes(x)) : [];\r\n\r\n    const selectedItems = historyOptions.filter((item) => [...selectedList, ...checkedList] && [...selectedList, ...checkedList].includes(item.id));\r\n\r\n    props.onSelect && props.onSelect(checkedList, selectedItems, totalRecordCount, selectedDropdownValue === DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE);\r\n\r\n    setPreviouslyCheckedList((previousList) => {\r\n      return previousList?.map((value) => {\r\n        if (!checkedList?.includes(value)) {\r\n          return value;\r\n        }\r\n      });\r\n    });\r\n  }, [checkedList, props.deletedList]);\r\n\r\n  /**\r\n   * LoadMore will trigger if items's size less than 5\r\n   * Working\r\n   */\r\n  useEffect(() => {\r\n    if (\r\n      props.removedItem &&\r\n      props.removedItem.length >= items.length - DEFAULT_LOAD_VALUE &&\r\n      items.length > 0 &&\r\n      props.removedItem.length > 0 &&\r\n      pageCount >= currentPage &&\r\n      !props.isLoading\r\n    ) {\r\n      loadMore();\r\n    }\r\n    props.onRecordsChange && props.onRecordsChange(items);\r\n  }, [props.removedItem, items, props.isLoading]);\r\n\r\n  useEffect(() => {\r\n    props.onTotalCount && props.onTotalCount(totalRecordCount);\r\n  }, [totalRecordCount]);\r\n\r\n  const onChange = (list: any) => {\r\n    props.onChange && props.onChange(true);\r\n    setCheckedList(list);\r\n    setIndeterminate(!!list.length && list.length < items.length);\r\n    setCheckAll(list.length === items.length);\r\n  };\r\n\r\n  const setHeaderCheckboxOptions = (intermediate: boolean, selectAll: boolean, checkedListValues: any) => {\r\n    setIndeterminate(intermediate);\r\n    setCheckAll(selectAll);\r\n    setCheckedList(checkedListValues);\r\n  };\r\n\r\n  const handleCheckedChannels = () => {\r\n    const checkedListValue = props.checkedList ? props.checkedList : [];\r\n    const commonSites: any[] = [];\r\n    checkedListValue.forEach((viewListItem: any) => {\r\n      if (!items.some((list: any) => list.id === viewListItem.id)) {\r\n        commonSites.push(viewListItem);\r\n      } else {\r\n        props.onChangeCheckBox && props.onChangeCheckBox({ checked: false }, viewListItem.id);\r\n      }\r\n    });\r\n\r\n    if (props.checkedList && props.checkedList.length > 0) {\r\n      const checkedListLength = props.checkedList ? props.checkedList.length : 0;\r\n\r\n      const tmpCheckList = props.checkedList ? props.checkedList : [];\r\n      const visibleElemnts = items.filter((x) => tmpCheckList.some((y: any) => y.id === x.id));\r\n\r\n      if (!!visibleElemnts.length && checkedListLength === items.length) {\r\n        setHeaderCheckboxOptions(false, false, []);\r\n      } else {\r\n        if (props.checkedList.length > items.length) {\r\n          setHeaderCheckboxOptions(false, false, [...commonSites.map((val) => val.id)]);\r\n        } else {\r\n          const mergedSites = items.map((item: any) => item.id);\r\n          const selectedValues = props.selectedList ? props.selectedList : [];\r\n          const mergedValues = [...mergedSites, ...selectedValues];\r\n          setHeaderCheckboxOptions(false, true, mergedValues);\r\n        }\r\n      }\r\n    } else {\r\n      setHeaderCheckboxOptions(\r\n        false,\r\n        true,\r\n        items.map((item: any) => item.id)\r\n      );\r\n    }\r\n  };\r\n\r\n  const onCheckAllChange = (e: any) => {\r\n    props.onChange && props.onChange(true);\r\n    if (selectedDropdownValue !== DROPDOWN_SELECT_VALUE_SELECT_ALL) {\r\n      handleCheckedChannels();\r\n      setSelectedDropdownValue(e.target.checked ? DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE : DROPDOWN_SELECT_VALUE_NONE);\r\n    } else {\r\n      setCheckedList(e.target.checked ? items.map((item: any) => item.id) : []);\r\n      setSelectedDropdownValue(DROPDOWN_SELECT_VALUE_NONE);\r\n      setIndeterminate(e.target.checked ? true : false);\r\n      setCheckAll(e.target.checked);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Ant Design Won't allow to access the debounceFetcher\r\n   * x => React.useMemo with return\r\n   */\r\n  const onSearch = (value: any) => {\r\n    debounceFetcher(value.target.value);\r\n  };\r\n  const debounceFetcher = React.useMemo(() => {\r\n    const loadOptions = async (keyVal: string) => {\r\n      setItems([]);\r\n      setCheckAll(false);\r\n      setIndeterminate(false);\r\n      setCheckedList([]);\r\n      setSelectedDropdownValue(DROPDOWN_SELECT_VALUE_NONE);\r\n      setIsEndOfScroll(false);\r\n      props.setPaginations(1, keyVal);\r\n    };\r\n    return debounce(loadOptions, config.inputDebounceInterval);\r\n  }, [config.inputDebounceInterval]);\r\n\r\n  const loadMore = () => {\r\n    setIsEndOfScroll(false);\r\n    props.setPaginations(currentPage + 1, keyValue);\r\n  };\r\n\r\n  const setResData = (res: any) => {\r\n    setCurrentPage(res.data.pageNumber);\r\n    setPageCount(res.data.pageCount);\r\n    setTotalRecordCount(res.data.totalRecordCount);\r\n    setHistoryOptions((tmpHistoryOptions: any) => {\r\n      return uniqueArray([...tmpHistoryOptions, ...res?.data?.records]);\r\n    });\r\n  };\r\n\r\n  const onScroll = () => {\r\n    let lastElement: any = document.getElementById(items ? `item-${(items || []).length - 1}` : 'null');\r\n\r\n    if (props.idKeyValue) {\r\n      const getIds = items.map((x) => x[props.idKeyValue ? props.idKeyValue : '']);\r\n      const remainigPreSelectedContact =\r\n        props.removedItem && props.removedItem.length > 0 ? getIds.filter((val: any) => (props.removedItem ? !props.removedItem.includes(val) : val)) : getIds;\r\n\r\n      lastElement = document.getElementById(items ? `item-${remainigPreSelectedContact[remainigPreSelectedContact.length - 1]}` : 'null');\r\n    }\r\n\r\n    const containerTop = infinityListRef ? infinityListRef?.current.getBoundingClientRect().top : null;\r\n\r\n    const lastElementTopPos = lastElement?.getBoundingClientRect().top - containerTop;\r\n    const containerHeight = infinityListRef?.current?.getBoundingClientRect().height;\r\n\r\n    if (lastElementTopPos - DEFAULT_VIEW_SIZE < containerHeight && pageCount > currentPage && !props.isLoading) {\r\n      loadMore();\r\n    } else {\r\n      if (pageCount === currentPage) {\r\n        setIsEndOfScroll(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleMenuClick = (e: any) => {\r\n    setSelectedDropdownValue((oldSelectedValue) => {\r\n      setPreviouslySelectedDropdownValue(oldSelectedValue);\r\n      return e.key;\r\n    });\r\n    props.onChange && props.onChange(true);\r\n    if (e.key === DROPDOWN_SELECT_VALUE_NONE || e.key === DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE) {\r\n      setCheckAll(false);\r\n      setIndeterminate(false);\r\n\r\n      const checkedListValue = props.checkedList ? props.checkedList : [];\r\n      const commonSites: any[] = [];\r\n      checkedListValue.forEach((viewListItem: any) => {\r\n        if (!items.some((list: any) => list.id === viewListItem.id)) {\r\n          commonSites.push(viewListItem);\r\n        } else {\r\n          props.onChangeCheckBox && props.onChangeCheckBox({ checked: false }, viewListItem.id);\r\n        }\r\n      });\r\n\r\n      setCheckedList((oldCheckedList) => {\r\n        if (e.key === DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE) {\r\n          setPreviouslyCheckedList(oldCheckedList);\r\n        }\r\n        return [...commonSites.map((val) => val.id)];\r\n      });\r\n    } else {\r\n      setIndeterminate(e.key === DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE ? false : true);\r\n      setCheckAll(true);\r\n      setCheckedList((oldCheckedList) => uniqueArray([...items.map((item: any) => item.id), ...oldCheckedList]));\r\n    }\r\n  };\r\n\r\n  const generateRecordCountLabel = () => {\r\n    const recordCount = props.selectedCount ? props.selectedCount : 0;\r\n    const singleRecord = ' - 1 Record Selected';\r\n    const multipleReords = ` - ${recordCount} Records Selected`;\r\n    const displayLabel = recordCount === 1 ? singleRecord : multipleReords;\r\n    const loadedRecords = (props.paginatedLimit ? props.paginatedLimit : LIMIT) * currentPage;\r\n    const displayLoadedRecords = loadedRecords <= totalRecordCount ? loadedRecords : totalRecordCount;\r\n    return <p className={styles.yjSwitcherSelectedRecords}>{`${displayLoadedRecords} of ${totalRecordCount} Result(s)  ${recordCount > 0 ? displayLabel : ''}  `}</p>;\r\n  };\r\n\r\n  const onRemove = (id: any) => {\r\n    props.onItemRemove && props.onItemRemove(id);\r\n  };\r\n\r\n  const menu = (\r\n    <Menu onClick={handleMenuClick}>\r\n      <Menu.Item key={DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE}>{DROPDOWN_SELECT_VALUE_SELECT_ALL_VISIBLE}</Menu.Item>\r\n      <Menu.Item hidden={true} key={DROPDOWN_SELECT_VALUE_SELECT_ALL}>\r\n        {DROPDOWN_SELECT_VALUE_SELECT_ALL}\r\n      </Menu.Item>\r\n      <Menu.Item key={DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE}>{DROPDOWN_SELECT_VALUE_CLEAR_VISIBLE}</Menu.Item>\r\n      <Menu.Item hidden={true} key={DROPDOWN_SELECT_VALUE_NONE}>\r\n        {DROPDOWN_SELECT_VALUE_NONE}\r\n      </Menu.Item>\r\n    </Menu>\r\n  );\r\n  const renderList = () => (\r\n    <div\r\n      style={{\r\n        height: props?.heightInPx || DEFAULT_DIV_HEIGHT,\r\n        overflow: 'auto',\r\n      }}\r\n      onScroll={onScroll}\r\n      ref={infinityListRef}\r\n    >\r\n      <List\r\n        className={'yj_cp_primary_accordian_list'}\r\n        itemLayout=\"horizontal\"\r\n        dataSource={items}\r\n        grid={props.grid}\r\n        renderItem={(item, index) => {\r\n          if (props?.idKeyValue && !props.removedItem?.includes(item[props?.idKeyValue])) {\r\n            return (\r\n              <ListItem className={styles.yj_cp_published_files_card} key={index} id={`item-${props?.idKeyValue ? item[props?.idKeyValue] : index}`}>\r\n                <div className={props.listItemStyle ? props.listItemStyle : ''}>\r\n                  <div>\r\n                    {props.hasCheckbox && <Checkbox onChange={(event) => props.onChangeCheckBox && props.onChangeCheckBox(event.target, item.id)} value={item.id}></Checkbox>}\r\n                  </div>\r\n                  <div>{props.formatValue(item, (value: any) => onRemove(value))}</div>\r\n                </div>\r\n              </ListItem>\r\n            );\r\n          }\r\n        }}\r\n      >\r\n        <div className={styles.yjInfinityLoadMore}>\r\n          <Spin spinning={props.isLoading} />\r\n          {isEndOfScroll && (\r\n            <div>\r\n              <p>No more records to load</p>\r\n            </div>\r\n          )}\r\n          {(items.length === 0 || (props.removedItem && props.removedItem.length === items.length)) && !props.isLoading && props.notFoundContent}\r\n        </div>\r\n      </List>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {props.hasSearch && <Search placeholder=\"input search loading default\" onChange={onSearch} />}\r\n      {props.hasCheckbox ? (\r\n        <>\r\n          <div className={styles.yjSelectOptionSwitcher}>\r\n            <Dropdown overlay={menu}>\r\n              <a href=\"#drop-down\" onClick={(e) => e.preventDefault()}>\r\n                <DownOutlined />\r\n              </a>\r\n            </Dropdown>\r\n            <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}></Checkbox>\r\n            {props.showRecordCounter && totalRecordCount > 0 && generateRecordCountLabel()}\r\n          </div>\r\n          <div className={`${styles.yjInfiniteListItems}`}>\r\n            <CheckboxGroup style={{ width: '100%' }} onChange={onChange} value={checkedList}>\r\n              {renderList()}\r\n            </CheckboxGroup>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        renderList()\r\n      )}\r\n    </>\r\n  );\r\n};\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\validators\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SiteSelection\\index.tsx", ["488", "489", "490", "491", "492"], [], "import { useLazyGetSitesInfiniteRecordsQuery } from '@app/api/sitesApiSlice';\r\nimport { selectAppState, setSelectedSite } from '@app/appSlice';\r\nimport { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';\r\nimport InfinitySelect from '../InfinitySelect';\r\n\r\nimport logger from '@app/utils/logger';\r\nimport React, { useCallback, useEffect, useState } from 'react';\r\nimport { InfinitySelectGetOptions } from '../InfinitySelect/types';\r\nconst LIMIT = 10;\r\n\r\n//FIXME\r\nconst SiteSelection = () => {\r\n  const { selectedSite } = useAppSelector(selectAppState);\r\n  const [siteIds, setSiteIds] = useState();\r\n  const dispatch = useAppDispatch();\r\n  const isDefault: boolean = selectedSite !== '';\r\n\r\n  const [triggerInfiniteRecords, { isLoading, error }] = useLazyGetSitesInfiniteRecordsQuery();\r\n\r\n  type modeType = 'multiple' | 'tags' | undefined;\r\n\r\n  const mode: modeType = undefined;\r\n\r\n  const queryParameters = '';\r\n\r\n  const setSiteId = (id: string): void => {\r\n    dispatch(setSelectedSite(id));\r\n  };\r\n\r\n  const getPaginatedRecords = async  (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {\r\n      const transformFilters: any = {};\r\n      /**\r\n       * Will add the keyvalue if dropdown still visible\r\n       */\r\n      if (searchValue) {\r\n        transformFilters.search = searchValue;\r\n      }\r\n\r\n      if (queryParameters) {\r\n        transformFilters.queryParameters = queryParameters;\r\n      }\r\n\r\n      const result = await triggerInfiniteRecords({\r\n        limit: LIMIT,\r\n        offset: page - 1,\r\n        ...transformFilters,\r\n      })\r\n        .then((res: any) => {\r\n            logger.info('SideSelection','getPaginatedRecords',res.data);\r\n          if (res.data) {\r\n            setSiteIds(res.data.records[0].id);\r\n            return res.data;\r\n          } else {\r\n            logger.error('SideSelection','getPaginatedRecords',res.error);\r\n            return []\r\n          }\r\n        })\r\n        .catch((error: any) => {\r\n            logger.error('SideSelection','getPaginatedRecords',error);\r\n\r\n            return [];\r\n        });\r\n      return result;\r\n    };\r\n\r\n  const handleOnChange = (value: any, selectedValues?: Array<any>) => {\r\n    setSiteId(value);\r\n  };\r\n\r\n  useEffect(()=>{\r\n    getPaginatedRecords(1,'load','')\r\n  },[])\r\n\r\n  return (\r\n    <div className={'yj_cp_channel_selector'}>\r\n      {siteIds && <InfinitySelect\r\n        getPaginatedRecords={getPaginatedRecords}\r\n        formatValue={(value) => {\r\n          return `${value.name}`;\r\n        }}\r\n        onLoaded={(isLoaded: boolean) => console.log(isLoaded)}\r\n        isDefault={true}\r\n        notFoundContent=\"No Site Available\"\r\n        notLoadContent=\"Failed to load values in Site dropdown\"\r\n        onChange={(value, selectedValues) => {\r\n            console.log('value, selectedValues',value, selectedValues)\r\n          handleOnChange(value);\r\n        }}\r\n        placeholder={`Site Name${isDefault}${siteIds}`}\r\n        defaultValues={siteIds}\r\n      />}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SiteSelection;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\SubmittedFiles\\FilterArea\\index.tsx", ["493", "494", "495", "496", "497", "498", "499", "500", "501", "502"], [], "import { <PERSON><PERSON>, <PERSON>, <PERSON>, Select } from 'antd';\r\nimport React from 'react';\r\nimport { Tooltip } from 'antd';\r\nimport { SearchOutlined, PlusOutlined } from '@ant-design/icons';\r\nimport Search from '@app/components/Search';\r\nimport styles from './index.module.less';\r\nimport { BiSortDown } from 'react-icons/bi';\r\n\r\ntype Props = {\r\n  onSearch: (value: string) => void;\r\n  onSortChange: () => void;\r\n  onSeachByChange: (value: string) => void;\r\n  onSortByChange: (value: string) => void;\r\n  onStatusChange: (value: string) => void;\r\n  onCategoryChange: (value: string) => void;\r\n  sortBy: string;\r\n};\r\n\r\nconst FilterArea: React.FC<Props> = (props) => {\r\n  return (\r\n    <div className={'yj_cp_card_filter_section'}>\r\n      {/* <Row gutter={10}>\r\n        <Col span={10}>\r\n          <div>\r\n            <Search placeholder=\"Search by File Title\" suffix={<SearchOutlined />} onSearch={(value: string) => console.log(value)} />\r\n          </div>\r\n        </Col>\r\n        <Col span={8}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Search By</div>\r\n            </Col>\r\n            <Col flex=\"auto\">\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSeachByChange(value)}>\r\n                <Select.Option value=\"1\">File title</Select.Option>\r\n              </Select>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Sort By</div>\r\n            </Col>\r\n            <Col flex=\"auto\">\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSortByChange(value)}>\r\n                <Select.Option value=\"1\">Uploaded at</Select.Option>\r\n              </Select>\r\n            </Col>\r\n            <Col>\r\n              {(() => {\r\n                if (props.sortBy === 'desc')\r\n                  return (\r\n                    <BiSortDown\r\n                      onClick={props.onSortChange}\r\n                      style={{\r\n                        transform: 'rotateY(180deg)',\r\n                        fontSize: '1.5rem',\r\n                      }}\r\n                    />\r\n                  );\r\n                else\r\n                  return (\r\n                    <BiSortDown\r\n                      onClick={props.onSortChange}\r\n                      style={{\r\n                        transform: 'rotate(180deg)',\r\n                        fontSize: '1.5rem',\r\n                      }}\r\n                    />\r\n                  );\r\n              })()}\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n      </Row>\r\n      <Row style={{ marginTop: '10px' }} gutter={10}>\r\n        <Col span={3}>\r\n          <Button className={styles.yj_cp_add_filter_btn} type=\"link\" icon={<PlusOutlined />}>\r\n            ADD FILTER\r\n          </Button>\r\n        </Col>\r\n        <Col span={4}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Category</div>\r\n            </Col>\r\n            <Col span={12}>\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onStatusChange(value)}>\r\n                <Select.Option value=\"1\">All</Select.Option>\r\n              </Select>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n        <Col span={4}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Status</div>\r\n            </Col>\r\n            <Col span={12}>\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onCategoryChange(value)}>\r\n                <Select.Option value=\"1\">All</Select.Option>\r\n              </Select>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n      </Row> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FilterArea;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\FileDetailsModal\\index.tsx", ["503"], [], "import Modal from '@app/components/CustomModal';\r\nimport useFileList from '@app/components/FileUploader/hooks/useFileList';\r\nimport { FileEvents, FileRecord } from '@app/components/forms/UploaderSubmit/types';\r\nimport UploaderSubmitContainer from '@app/components/forms/UploaderSubmit/UploaderSubmitContainer';\r\nimport { Form } from 'antd';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { FileDetailsModalProps } from '../types';\r\nimport confirmDiscard from '../utils/confirmDiscard';\r\n\r\nconst FileDetailsModal: React.FC<FileDetailsModalProps> = ({ siteId, files, onClose, onRemove, onSaveComplete, onFinished, onLastFile = () => null }) => {\r\n  const [form] = Form.useForm();\r\n  const { fileList, actions } = useFileList({ files });\r\n  const { changeTitle, changeSelection, changeSelectAll, removeFile } = actions;\r\n  const [isFileTableValid, setIsFileTableValid] = useState(false);\r\n\r\n  const onTitleUpdate = (value: string, index: number) => {\r\n    changeTitle(value, index);\r\n  };\r\n\r\n  const onFileSelect = (value: boolean, index: number) => {\r\n    changeSelection(value, index);\r\n  };\r\n\r\n  const onFileSelectAll = (value: boolean) => {\r\n    changeSelectAll(value);\r\n  };\r\n\r\n  const onFileRemove = (referenceNumber: string) => {\r\n    confirmDiscard(() => {\r\n      deleteFile(referenceNumber);\r\n    });\r\n  };\r\n\r\n  const onSaveSuccess = (inputFileList: FileRecord[]) => {\r\n    onSaveComplete?.(inputFileList);\r\n  };\r\n\r\n  const deleteFile = (referenceNumber: string) => {\r\n    onRemove?.(referenceNumber);\r\n    removeFile(referenceNumber);\r\n  };\r\n\r\n  const fileEvents: FileEvents = {\r\n    onTitleUpdate,\r\n    onFileSelect,\r\n    onFileSelectAll,\r\n    onFileRemove,\r\n    onSaveSuccess,\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (fileList?.length === 0) {\r\n      onLastFile?.();\r\n    }\r\n    const checkedLength = fileList?.filter((v) => v.checked)?.length;\r\n    const validFileTable = !!checkedLength && !fileList?.filter((v) => v.checked && (!v.title.trim() || !!v.error)).length;\r\n    setIsFileTableValid(validFileTable);\r\n  }, [fileList]);\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        size={'medium'}\r\n        style={{ top: 20 }}\r\n        title={'Upload File(s)'}\r\n        open={!!fileList?.length}\r\n        destroyOnClose={true}\r\n        okText=\"UPLOAD\"\r\n        onOk={() => form.submit()}\r\n        onCancel={onClose}\r\n        okButtonProps={{ disabled: !isFileTableValid }}\r\n        \r\n      >\r\n        <UploaderSubmitContainer siteId={siteId} fileList={fileList} form={form} fileEvents={fileEvents} onFinished={onFinished} />\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FileDetailsModal;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PublishedFiles\\FilterArea\\index.tsx", ["504", "505", "506", "507", "508", "509", "510", "511", "512", "513"], [], "import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Tooltip } from 'antd';\r\nimport React from 'react';\r\nimport { SearchOutlined, PlusOutlined } from '@ant-design/icons';\r\nimport Search from '@app/components/Search';\r\nimport styles from './index.module.less';\r\nimport { BiSortDown } from 'react-icons/bi';\r\n\r\ntype Props = {\r\n  onSearch: (value: string) => void;\r\n  onSortChange: () => void;\r\n  onSeachByChange: (value: string) => void;\r\n  onSortByChange: (value: string) => void;\r\n  onStatusChange: (value: string) => void;\r\n  sortBy: string;\r\n};\r\n\r\nconst FilterArea: React.FC<Props> = (props) => {\r\n  return (\r\n    <div className={'yj_cp_card_filter_section'}>\r\n      {/* <Row gutter={10}>\r\n        <Col span={12}>\r\n          <div>\r\n            <Search placeholder=\"Search by File Title\" suffix={<SearchOutlined />} onSearch={(value: string) => console.log(value)} />\r\n          </div>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Search By</div>\r\n            </Col>\r\n            <Col flex=\"auto\">\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSeachByChange(value)}>\r\n                <Select.Option value=\"1\">File title</Select.Option>\r\n              </Select>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Sort By</div>\r\n            </Col>\r\n            <Col flex=\"auto\">\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSortByChange(value)}>\r\n                <Select.Option value=\"1\">Uploaded at</Select.Option>\r\n              </Select>\r\n            </Col>\r\n            <Col>\r\n              {(() => {\r\n                if (props.sortBy === 'desc')\r\n                  return (\r\n                    <BiSortDown\r\n                      onClick={props.onSortChange}\r\n                      style={{\r\n                        transform: 'rotateY(180deg)',\r\n                        fontSize: '1.5rem',\r\n                      }}\r\n                    />\r\n                  );\r\n                else\r\n                  return (\r\n                    <BiSortDown\r\n                      onClick={props.onSortChange}\r\n                      style={{\r\n                        transform: 'rotate(180deg)',\r\n                        fontSize: '1.5rem',\r\n                      }}\r\n                    />\r\n                  );\r\n              })()}\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n      </Row>\r\n      <Row style={{ marginTop: '10px' }} gutter={10}>\r\n        <Col span={3}>\r\n          <Button className={styles.yj_cp_add_filter_btn} type=\"link\" icon={<PlusOutlined />}>\r\n            ADD FILTER\r\n          </Button>\r\n        </Col>\r\n        <Col span={8}>\r\n          <Row gutter={10} align=\"middle\">\r\n            <Col>\r\n              <div className={styles.YJ_CP_FILTER_TITLE}>Status</div>\r\n            </Col>\r\n            <Col span={12}>\r\n              <Select key={1} defaultValue=\"1\" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onStatusChange(value)}>\r\n                <Select.Option value=\"1\">All</Select.Option>\r\n              </Select>\r\n            </Col>\r\n          </Row>\r\n        </Col>\r\n      </Row> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FilterArea;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\hooks\\useLocalStorage.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\menus\\menuConfig.ts", ["514", "515", "516"], [], "import MenuConfigType from '@app/types/MenuConfigType';\r\nimport { AiOutlineFileDone, AiOutlineImport, AiOutlineInbox } from 'react-icons/ai';\r\n\r\nconst RequestsMenuConfig: MenuConfigType = {\r\n  path: '/requests',\r\n  title: 'Requests',\r\n  key: 'Requests',\r\n  icon: AiOutlineInbox,\r\n  noRoute: true,\r\n};\r\n\r\nconst SubmittedFilesMenuConfig: MenuConfigType = {\r\n  path: '/submittedFiles',\r\n  title: 'Submitted Files',\r\n  key: 'SubmittedFiles',\r\n  icon: AiOutlineImport,\r\n  noRoute: false,\r\n};\r\n\r\nconst PublishedFilesMenuConfig: MenuConfigType = {\r\n  path: '/publishedFiles',\r\n  title: 'Published Files',\r\n  key: 'PublishedFiles',\r\n  icon: AiOutlineFileDone,\r\n  hasCount: true,\r\n};\r\n\r\nconst AnnouncementsMenuConfig: MenuConfigType = {\r\n  path: '/announcements',\r\n  title: 'Announcements',\r\n  key: 'Announcements',\r\n  icon: AiOutlineFileDone,\r\n  noRoute: true,\r\n};\r\n\r\nconst DiscussionsMenuConfig: MenuConfigType = {\r\n  path: '/discussions',\r\n  title: 'Discussions',\r\n  key: 'Discussions',\r\n  icon: AiOutlineFileDone,\r\n  noRoute: true,\r\n};\r\n/**\r\n * Dont remove unused Menu configs before go live\r\n */\r\nconst menuConfig: MenuConfigType[] = [ SubmittedFilesMenuConfig, PublishedFilesMenuConfig];\r\n\r\nexport default menuConfig;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\http\\statusCodes.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\utils\\mapMenuConfig.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\constants\\errors.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useUploader.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\SideMenu\\index.tsx", ["517"], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\index.tsx", ["518"], [], "import { DeleteOutlined, DownOutlined, FileExclamationOutlined, SyncOutlined, UpOutlined } from '@ant-design/icons';\r\nimport { FULL_PERCENTAGE } from '@app/components/FileUploader';\r\nimport { Button } from 'antd';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { FileList, UploadProgressProps } from '../types';\r\nimport styles from './index.module.less';\r\nimport ProgressingFile from './ProgressingFile';\r\n\r\nconst UploadProgress: React.FC<UploadProgressProps> = ({ files, onRetry, onRetryAll, onDelete, onDeleteAll, onComplete }) => {\r\n  const [isCollapsed, setIsCollapsed] = useState(true);\r\n\r\n  const total = Object.keys(files).length;\r\n  const completed = Object.entries(files).filter(\r\n    ([_, info]) => Math.round(info.percent || 0) === FULL_PERCENTAGE && info.chunkCounter === info.chunkCount && info.completed\r\n  ).length;\r\n  const hasErrored = !!Object.entries(files).filter(([_, info]) => info.error).length;\r\n\r\n  useEffect(() => {\r\n    if (completed === total && !hasErrored) {\r\n      onComplete?.();\r\n    }\r\n  }, [completed, onComplete, total]);\r\n\r\n  return (\r\n    <div className={styles.yjUploadProgressContainer}>\r\n      <div className={styles.yjProgressWrapperHeader}>\r\n        <h6>\r\n          Uploading Files{' '}\r\n          <span className={styles.uploadingCount}>\r\n            {' '}\r\n            ({completed}/{total} Files Completed)\r\n          </span>\r\n        </h6>\r\n\r\n        {hasErrored && (\r\n          <div className={styles.yjProgressErrorMessageWrapper}>\r\n            <span className={styles.attRequired}>\r\n              <FileExclamationOutlined /> Some files require your attention to complete upload\r\n            </span>\r\n          </div>\r\n        )}\r\n\r\n        <div className={styles.yjProgressHeaderButtonWrapper}>\r\n          <div className={styles.yjProgressHeaderButtonLeft}>\r\n            <Button icon={<SyncOutlined />} onClick={onRetryAll} disabled={!hasErrored} style={{ cursor: hasErrored ? 'pointer' : 'not-allowed' }} />\r\n            <Button icon={<DeleteOutlined />} onClick={onDeleteAll} />\r\n          </div>\r\n          {total > 1 && (\r\n            <div className={styles.yjProgressHeaderButtonRight}>\r\n              <Button icon={isCollapsed ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsCollapsed(!isCollapsed)} />\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className={styles.yjFileProgressWrapper}>\r\n        <div className={isCollapsed ? `${styles.yjProgressWrapper} ${styles.yjProgressWrapperCollapsed}` : styles.yjProgressWrapper}>\r\n          {renderFilesList(files, onRetry, onDelete)}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction renderFilesList(files: FileList, onRetry: (uid: string) => void, onDelete: (uid: string) => void) {\r\n  return Object.entries(files).map(([uid, info]) => {\r\n    return <ProgressingFile key={uid} uid={uid} info={info} onRetry={onRetry} onDelete={onDelete} />;\r\n  });\r\n}\r\n\r\nexport default UploadProgress;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\DragAndDrop\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\hooks\\useFileList.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\UploaderSubmitContainer.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\array\\index.ts", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\regex\\index.ts", ["519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534"], [], "const PositiveNumberPattern = /^[0-9\\b]+$/;\r\nconst PhoneNoPattern = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\\s\\./0-9]*$/;\r\nconst WebsitePattern = /^((https?|ftp|smtp):\\/\\/)?(www.)?[a-z0-9]+\\.[a-z]+(\\/[a-zA-Z0-9#]+\\/?)*$/;\r\nconst WebsitePatternWithExtensions = /^(?:http(s)?:\\/\\/)?[\\w.-]+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$/;\r\nconst AvoidWhitespace = /[^-\\s]/;\r\nconst EmailPattern = /^(([^<>()[\\]\\\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\r\nconst FileNameCharacters   = /^[a-zA-Z0-9!^@.\\(\\)~_\\\\\\-+=',\\$\\?\"\\/>£\\s]+$/;\r\n\r\nexport { PositiveNumberPattern, PhoneNoPattern, WebsitePattern, WebsitePatternWithExtensions, AvoidWhitespace, EmailPattern, FileNameCharacters };\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\InfinitySelect\\index.tsx", ["535", "536", "537", "538"], [], "import React, { ReactNode, useEffect, useRef, useState, } from \"react\";\r\nimport { Button, Select, Spin } from \"antd\";\r\nimport { DeleteOutlined } from \"@ant-design/icons\";\r\nimport debounce from \"lodash/debounce\";\r\nimport styles from \"./index.module.less\";\r\nimport { uniqueArray } from \"../../utils/array\";\r\n\r\nexport type InfinitySelectGetOptions = 'fetch' | 'load';\r\n\r\nexport interface ISelector {\r\n  data?: any;\r\n  getPaginatedRecords: (page: number, type: getOptions, keyValue: string) => Promise<Array<any>>;\r\n  onChange: (value: any, selectedValues?: Array<any>) => void;\r\n  onLoaded?: (isLoaded: boolean) => void;\r\n  formatValue: (value: any) => any;\r\n  mode?: \"multiple\" | \"tags\";\r\n  notFoundContent: string;\r\n  notLoadContent: string;\r\n  value?: any;\r\n  placeholder?: string;\r\n  isDelete?: boolean;\r\n  isDefault?: boolean;\r\n  queryParameters?: {};\r\n  excludeList?: Array<string>;\r\n  preSelected?: Array<any>;\r\n  defaultValues?: any;\r\n  removeById?: any;\r\n  maxTagCount?: any;\r\n  maxTagPlaceHolder?: (value: any) => void;\r\n  disabled?: boolean;\r\n  returnObject?: boolean;\r\n  newlyAddedValue?: any;\r\n  hideSelected?: boolean;\r\n  filterValues?: any;\r\n  suffixIcon?: ReactNode;\r\n  waitCharCount?: number;\r\n  onTop?: boolean;\r\n}\r\n\r\ntype getOptions = \"fetch\" | \"load\";\r\n\r\nconst DEFAULT_VIEW_SIZE = 15;\r\n\r\nexport default (props: ISelector) => {\r\n  const dropdown = useRef({}) as any;\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [isEndOfScroll, setIsEndOfScroll] = useState<boolean>(false);\r\n  const [isBottom, setIsBottom] = useState<boolean>(false);\r\n  const [values, setValues] = useState<Array<string | number>>([]);\r\n  const [options, setOptions] = useState<Array<any>>([]);\r\n  const [keyValue, setKeyValue] = useState<string>(\"\");\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [pageCount, setPageCount] = useState<number>(1);\r\n\r\n  window.addEventListener(\r\n    \"keydown\",\r\n    (event: any) => {\r\n      if (event.key === \"Backspace\" && !event.target.value) {\r\n        event.stopPropagation();\r\n      }\r\n    },\r\n    true\r\n  );\r\n\r\n  const fetchInitialData = () => {\r\n    setLoading(true);\r\n    props.getPaginatedRecords(1, 'fetch', keyValue).then((res: any) => {\r\n      setOptions(res.records);\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n      setLoading(false);\r\n      setValues(props.defaultValues);\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n  }, []);\r\n\r\n  const loadMore = () => {\r\n    setIsEndOfScroll(false);\r\n    setLoading(true);\r\n    props.getPaginatedRecords(currentPage + 1, \"load\", keyValue).then((res: any) => {\r\n      console.info('loadMore',uniqueArray([...options, ...res.records]))\r\n      setOptions(uniqueArray([...options, ...res.records]));\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n      setLoading(false);\r\n    });\r\n  };\r\n\r\n  const debounceFetcher = React.useMemo(() => {\r\n    const loadOptions = async (keyVal: string) => {\r\n      if (props.waitCharCount && keyVal.length < props.waitCharCount) {\r\n        setKeyValue(keyVal);\r\n        return;\r\n      }\r\n      setLoading(true);\r\n      props.getPaginatedRecords(1, \"fetch\", keyVal).then((res: any) => {\r\n        console.info('debounceFetcher',options,uniqueArray([...res.records,...options ]))\r\n\r\n        setOptions(uniqueArray([...res.records, ...options]));\r\n        setLoading(false);\r\n      });\r\n    };\r\n    return debounce(loadOptions, 500);\r\n  }, [500,options]);\r\n\r\n  const onVisibleChange = (visibleState: boolean) => {\r\n    if (keyValue && !visibleState && props.mode) {\r\n      setLoading(true);\r\n      props.getPaginatedRecords(1, 'fetch',keyValue).then((res: any) => {\r\n        console.info('onVisibleChange',uniqueArray([...options, ...res.records]))\r\n\r\n        setOptions(uniqueArray([...options, ...res.records]));\r\n        setPageCount(res.pageCount);\r\n        setCurrentPage(res.pageNumber);\r\n        setLoading(false);\r\n      });\r\n    }\r\n  };\r\n\r\n  const onScroll = (event: any) => {\r\n    setIsBottom(\r\n      event.target.scrollHeight - event.target.scrollTop ===\r\n      event.target.clientHeight\r\n    );\r\n    const lastElement: any = document.getElementById(\r\n      options ? options[(options || []).length - 1].id : \"null\"\r\n    );\r\n    const containerTop = dropdown\r\n      ? dropdown?.current.getBoundingClientRect().top\r\n      : null;\r\n    const lastElementTopPos =\r\n      lastElement?.getBoundingClientRect().top - containerTop;\r\n    const containerHeight = dropdown?.current?.getBoundingClientRect().height;\r\n    console.log('onScroll',pageCount , currentPage)\r\n    if (\r\n      lastElementTopPos - DEFAULT_VIEW_SIZE < containerHeight &&\r\n      pageCount > currentPage &&\r\n      !loading\r\n    ) {\r\n      loadMore();\r\n    } else {\r\n      if (pageCount === currentPage) {\r\n        setIsEndOfScroll(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleBlur = () => {\r\n    props.getPaginatedRecords(1, 'fetch', '').then((res: any) => {\r\n      console.info('handleBlur',options,uniqueArray([...options, ...res.records]))\r\n\r\n      setKeyValue('');\r\n      setOptions(uniqueArray([...options, ...res.records]));\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Select\r\n        showSearch\r\n        showArrow\r\n        value={values}\r\n        filterOption={false}\r\n        disabled={props.disabled}\r\n        notFoundContent={loading ? <Spin size=\"small\" /> : props.notFoundContent}\r\n        mode={props.mode}\r\n        onSearch={(e) => {\r\n          console.log('onSearch',e)\r\n          debounceFetcher(e);\r\n        }}\r\n        onFocus={() => {\r\n        }}\r\n        onBlur={() => {\r\n          setLoading(true);\r\n          setIsEndOfScroll(false);\r\n          handleBlur();\r\n        }}\r\n        onChange={(value, option) => {\r\n          console.info('OnChange', value, option)\r\n          setValues(value);\r\n          if (props.returnObject) {\r\n            // props.onChange(\r\n            //     value,\r\n            //     historyOptions.filter((x) => value.includes(x.id))\r\n            // );\r\n          } else {\r\n            props.onChange(value);\r\n          }\r\n        }}\r\n        onDropdownVisibleChange={(e) => onVisibleChange(e)}\r\n        defaultValue={values}\r\n        onPopupScroll={(e) => onScroll(e)}\r\n        getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}\r\n        placeholder={props.placeholder}\r\n        maxTagCount={props.maxTagCount}\r\n        // maxTagPlaceholder={(e) =>\r\n        //   props.maxTagPlaceHolder && props.maxTagPlaceHolder(e)\r\n        // }\r\n        listHeight={200}\r\n        dropdownRender={(menu) => (\r\n          <div ref={dropdown} id=\"dropdown\">\r\n            {menu}\r\n            <div className={styles.yjInfinityLoadMore}>\r\n              <Spin size=\"small\" spinning={loading} />\r\n              {isEndOfScroll && isBottom && (\r\n                <div className={styles.yjInfinityScrollText}>\r\n                  <p>You have seen it all !</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      >\r\n        {options?.map((value: any) => {\r\n          return (\r\n            !props.excludeList?.includes(value.id) && (\r\n              <Select.Option key={value.id} id={value.id} value={value.id}>\r\n                {props.formatValue(value)}\r\n              </Select.Option>\r\n            )\r\n          );\r\n        })}\r\n      </Select>\r\n      {props.isDelete && (\r\n        <Button\r\n          onClick={() => {\r\n            setValues([]);\r\n          }}\r\n        >\r\n          <DeleteOutlined />\r\n        </Button>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\utils\\files\\formatSize.ts", ["539"], [], "const DEFAULT_DECIMAL_PLACES = 2;\r\nexport default (bytes: number, decimals = DEFAULT_DECIMAL_PLACES) => {\r\n  if (!bytes || bytes === 0 || bytes < 0) {\r\n    return '0 Bytes';\r\n  }\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;\r\n};\r\n\r\nconst CAPACITY_FOR_A_GB = 1073741824;\r\nconst NUMBER_THREE = 3;\r\nexport const DEFAULT_CHUNK_SIZE = CAPACITY_FOR_A_GB * NUMBER_THREE;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\FileUploader\\UploadProgress\\ProgressingFile.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\index.tsx", ["540", "541"], [], "import { useLazyGetSitesInfiniteRecordsQuery } from '@app/api/sitesApiSlice';\r\nimport InfinitySelect from '@app/components/InfinitySelect';\r\nimport { Col, Form, Row, Select } from 'antd';\r\nimport Tooltip from 'antd/es/tooltip';\r\nimport React from 'react';\r\nimport FileList from './FileList';\r\nimport styles from './index.module.less';\r\nimport { UploaderSubmitProps } from './types';\r\nimport { InfinitySelectGetOptions } from \"@app/components/InfinitySelect/types\";\r\nimport logger from \"@app/utils/logger\";\r\nconst LIMIT = 10;\r\n\r\nexport const UploaderSubmit: React.FC<UploaderSubmitProps> = ({ fileList, form, onFinish, fileEvents, forManageFiles = false, onFormChange = () => null, siteId }) => {\r\n  const [fetchInfiniteRecords] = useLazyGetSitesInfiniteRecordsQuery();\r\n\r\n  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {\r\n    const transformFilters: any = {};\r\n    /**\r\n     * Will add the keyvalue if dropdown still visible\r\n     */\r\n    if (searchValue) {\r\n      transformFilters.search = searchValue;\r\n    }\r\n\r\n\r\n\r\n    const result = await fetchInfiniteRecords({\r\n      limit: LIMIT,\r\n      offset: page - 1,\r\n      ...transformFilters,\r\n    })\r\n      .then((res: any) => {\r\n        logger.info('SideSelection', 'getPaginatedRecords', res.data);\r\n        if (res.data) {\r\n          return res.data;\r\n        } else {\r\n          logger.error('SideSelection', 'getPaginatedRecords', res.error);\r\n          return []\r\n        }\r\n      })\r\n      .catch((error: any) => {\r\n        logger.error('SideSelection', 'getPaginatedRecords', error);\r\n\r\n        return [];\r\n      });\r\n    return result;\r\n  };\r\n  return (\r\n    <Form size=\"middle\" name=\"fileDetails\" layout=\"vertical\" form={form} onFinish={(values) => onFinish(values, fileList)}>\r\n      <Row>\r\n        <Col span={24}>\r\n          <div className={styles.YJ_CP_UPLOADER_ITEM_TITLE}>SITE</div>\r\n          <div className={styles.YJ_CP_UPLOADER_ITEM_SELECT}>\r\n            <InfinitySelect\r\n              getPaginatedRecords={getPaginatedRecords}\r\n              formatValue={(value) => {\r\n                return `${value.name}`;\r\n              }}\r\n              defaultValues={[siteId]}\r\n              disabled\r\n              placeholder=\"Site Name\"\r\n              onChange={() => null}\r\n              notFoundContent={''}\r\n              notLoadContent={''}\r\n            />\r\n          </div>\r\n        </Col>\r\n        <Col span={24}>\r\n          <Row gutter={0}>\r\n            <FileList fileList={fileList ?? []} fileEvents={fileEvents} forManageFiles={forManageFiles} onDataChange={onFormChange} />\r\n          </Row>\r\n        </Col>\r\n        {/* <Col span={24}>\r\n          <div className={styles.YJ_CP_UPLOADER_ITEM_TITLE}>Category</div>\r\n          <div className={styles.YJ_CP_UPLOADER_ITEM_SELECT}>\r\n            <Select disabled placeholder=\"Select Category\" />\r\n          </div>\r\n        </Col> */}\r\n      </Row>\r\n    </Form>\r\n  );\r\n};\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\forms\\UploaderSubmit\\FileList.tsx", ["542"], [], "import { CloseOutlined } from '@ant-design/icons';\r\nimport { Button, Input, Table } from 'antd';\r\nimport Tooltip from 'antd/es/tooltip';\r\nimport { ColumnsType } from 'antd/lib/table';\r\nimport React, { useEffect, useState } from 'react';\r\nimport styles from './index.module.less';\r\nimport { FileRecord, UploaderSubmitFileListProps } from './types';\r\n\r\nconst FileList: React.FC<UploaderSubmitFileListProps> = ({\r\n  fileList,\r\n  fileEvents = {\r\n    onFileRemove: () => null,\r\n    onFileSelect: () => null,\r\n    onFileSelectAll: () => null,\r\n    onSaveSuccess: () => null,\r\n    onTitleUpdate: () => null,\r\n  },\r\n  forManageFiles = false,\r\n  onDataChange = () => null,\r\n}) => {\r\n  const { onFileSelectAll, onFileSelect, onTitleUpdate, onFileRemove } = fileEvents;\r\n  const checkedLength = fileList.filter((v) => v.checked).length;\r\n  const baseColumns: ColumnsType<FileRecord> = [\r\n    {\r\n      key: 'remove',\r\n      render: (record: FileRecord) => (\r\n        <Tooltip title=\"Remove Files\">\r\n          <Button key={record.referenceNumber} icon={<CloseOutlined />} className={styles.yjDeteleFile} onClick={() => onFileRemove(record.referenceNumber)} />\r\n        </Tooltip>\r\n      ),\r\n      width: 50,\r\n    },\r\n\r\n    {\r\n      key: 'title',\r\n      title: 'Title',\r\n      dataIndex: 'title',\r\n      render: (text: string, record: FileRecord, index: number) => {\r\n        return (\r\n          <>\r\n            <Input disabled={!record.checked} key={record.referenceNumber} value={text} onChange={(e) => onTitleUpdate(e.target.value, index)} />\r\n            {record.checked && record.error && <span style={{ color: 'red', display: 'block' }}>{record.error}</span>}\r\n          </>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  const [columns, setColumns] = useState<ColumnsType<FileRecord>>(baseColumns);\r\n  useEffect(() => {\r\n    if (forManageFiles) {\r\n      const customColumns: ColumnsType<FileRecord> = [\r\n        {\r\n          key: 'title',\r\n          title: 'Title',\r\n          dataIndex: 'title',\r\n          render: (text: string, record: FileRecord) => {\r\n            return (\r\n              <>\r\n                <Input\r\n                  defaultValue={text}\r\n                  onChange={(e) => {\r\n                    onDataChange(e.target.value !== '');\r\n                    onTitleUpdate(e.target.value, 0);\r\n                  }}\r\n                />\r\n                {record.checked && record.error && <span style={{ color: 'red' }}>{record.error}</span>}\r\n              </>\r\n            );\r\n          },\r\n        },\r\n      ];\r\n      setColumns(customColumns);\r\n    }\r\n  }, [forManageFiles]);\r\n\r\n  const rowSelection = {\r\n    selectedRowKeys: fileList.filter((e) => e.checked).map((e) => e.referenceNumber),\r\n    onSelect: (record: any, selected: any) => {\r\n      const index = fileList.findIndex((fileRecord: FileRecord) => fileRecord.referenceNumber === record.referenceNumber);\r\n      onFileSelect(selected, index);\r\n    },\r\n    onSelectAll: (selected: any) => {\r\n      onFileSelectAll(selected);\r\n    },\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {!checkedLength && <span style={{ color: 'red' }}>Select files to set properties</span>}\r\n      <Table\r\n        rowSelection={rowSelection}\r\n        tableLayout=\"fixed\"\r\n        className={styles.yjTblFileList}\r\n        columns={columns}\r\n        dataSource={fileList}\r\n        scroll={{\r\n          y: '31vh',\r\n        }}\r\n        pagination={false}\r\n        rowKey={'referenceNumber'}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FileList;\r\n", "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\components\\Search\\index.tsx", [], [], "D:\\Zone24x7\\Workspaces\\FrontEnd-Portal\\ReactApp\\ClientApp\\src\\app\\pages\\PortalControls\\index.tsx", [], [], {"ruleId": "543", "severity": 1, "message": "544", "line": 49, "column": 80, "nodeType": "545", "messageId": "546", "endLine": 49, "endColumn": 81}, {"ruleId": "543", "severity": 1, "message": "544", "line": 74, "column": 81, "nodeType": "545", "messageId": "546", "endLine": 74, "endColumn": 82}, {"ruleId": "547", "severity": 1, "message": "548", "line": 66, "column": 16, "nodeType": "549", "messageId": "550", "endLine": 66, "endColumn": 23}, {"ruleId": "547", "severity": 1, "message": "551", "line": 66, "column": 25, "nodeType": "549", "messageId": "550", "endLine": 66, "endColumn": 29}, {"ruleId": "552", "severity": 1, "message": "553", "line": 15, "column": 72, "nodeType": "545", "messageId": "554", "endLine": 15, "endColumn": 74}, {"ruleId": "547", "severity": 1, "message": "555", "line": 13, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 13, "endColumn": 30}, {"ruleId": "547", "severity": 1, "message": "556", "line": 17, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 17, "endColumn": 16}, {"ruleId": "547", "severity": 1, "message": "557", "line": 19, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 19, "endColumn": 16}, {"ruleId": "558", "severity": 1, "message": "559", "line": 44, "column": 18, "nodeType": "560", "endLine": 50, "endColumn": 8}, {"ruleId": "547", "severity": 1, "message": "561", "line": 77, "column": 18, "nodeType": "549", "messageId": "550", "endLine": 77, "endColumn": 33}, {"ruleId": "547", "severity": 1, "message": "562", "line": 78, "column": 18, "nodeType": "549", "messageId": "550", "endLine": 78, "endColumn": 32}, {"ruleId": "552", "severity": 1, "message": "553", "line": 82, "column": 31, "nodeType": "545", "messageId": "554", "endLine": 82, "endColumn": 33}, {"ruleId": "558", "severity": 1, "message": "563", "line": 95, "column": 6, "nodeType": "564", "endLine": 95, "endColumn": 13, "suggestions": "565"}, {"ruleId": "552", "severity": 1, "message": "553", "line": 102, "column": 36, "nodeType": "545", "messageId": "554", "endLine": 102, "endColumn": 38}, {"ruleId": "558", "severity": 1, "message": "566", "line": 120, "column": 6, "nodeType": "564", "endLine": 120, "endColumn": 53, "suggestions": "567"}, {"ruleId": "547", "severity": 1, "message": "568", "line": 3, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 3, "endColumn": 23}, {"ruleId": "558", "severity": 1, "message": "569", "line": 23, "column": 6, "nodeType": "564", "endLine": 23, "endColumn": 57, "suggestions": "570"}, {"ruleId": "558", "severity": 1, "message": "571", "line": 23, "column": 17, "nodeType": "572", "endLine": 23, "endColumn": 28}, {"ruleId": "547", "severity": 1, "message": "573", "line": 18, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 18, "endColumn": 16}, {"ruleId": "547", "severity": 1, "message": "574", "line": 18, "column": 24, "nodeType": "549", "messageId": "550", "endLine": 18, "endColumn": 37}, {"ruleId": "547", "severity": 1, "message": "575", "line": 19, "column": 20, "nodeType": "549", "messageId": "550", "endLine": 19, "endColumn": 29}, {"ruleId": "576", "severity": 1, "message": "577", "line": 38, "column": 127, "nodeType": "578", "messageId": "579", "endLine": 38, "endColumn": 128, "suggestions": "580"}, {"ruleId": "576", "severity": 1, "message": "577", "line": 48, "column": 127, "nodeType": "578", "messageId": "579", "endLine": 48, "endColumn": 128, "suggestions": "581"}, {"ruleId": "547", "severity": 1, "message": "582", "line": 1, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 25}, {"ruleId": "547", "severity": 1, "message": "583", "line": 1, "column": 75, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 87}, {"ruleId": "547", "severity": 1, "message": "584", "line": 29, "column": 63, "nodeType": "549", "messageId": "550", "endLine": 29, "endColumn": 70}, {"ruleId": "552", "severity": 1, "message": "585", "line": 84, "column": 22, "nodeType": "545", "messageId": "554", "endLine": 84, "endColumn": 24}, {"ruleId": "586", "severity": 1, "message": "587", "line": 458, "column": 15, "nodeType": "588", "endLine": 465, "endColumn": 16}, {"ruleId": "552", "severity": 1, "message": "585", "line": 61, "column": 22, "nodeType": "545", "messageId": "554", "endLine": 61, "endColumn": 24}, {"ruleId": "586", "severity": 1, "message": "587", "line": 260, "column": 15, "nodeType": "588", "endLine": 267, "endColumn": 16}, {"ruleId": "552", "severity": 1, "message": "553", "line": 47, "column": 67, "nodeType": "545", "messageId": "554", "endLine": 47, "endColumn": 69}, {"ruleId": "589", "severity": 1, "message": "590", "line": 11, "column": 1, "nodeType": "591", "endLine": 21, "endColumn": 3}, {"ruleId": "558", "severity": 1, "message": "559", "line": 23, "column": 18, "nodeType": "560", "endLine": 23, "endColumn": 48}, {"ruleId": "547", "severity": 1, "message": "592", "line": 9, "column": 36, "nodeType": "549", "messageId": "550", "endLine": 9, "endColumn": 44}, {"ruleId": "547", "severity": 1, "message": "593", "line": 10, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 10, "endColumn": 20}, {"ruleId": "558", "severity": 1, "message": "594", "line": 58, "column": 6, "nodeType": "564", "endLine": 58, "endColumn": 13, "suggestions": "595"}, {"ruleId": "547", "severity": 1, "message": "596", "line": 2, "column": 26, "nodeType": "549", "messageId": "550", "endLine": 2, "endColumn": 30}, {"ruleId": "558", "severity": 1, "message": "597", "line": 32, "column": 6, "nodeType": "564", "endLine": 32, "endColumn": 34, "suggestions": "598"}, {"ruleId": "558", "severity": 1, "message": "599", "line": 13, "column": 6, "nodeType": "564", "endLine": 13, "endColumn": 27, "suggestions": "600"}, {"ruleId": "558", "severity": 1, "message": "601", "line": 43, "column": 5, "nodeType": "564", "endLine": 43, "endColumn": 34, "suggestions": "602"}, {"ruleId": "558", "severity": 1, "message": "603", "line": 49, "column": 6, "nodeType": "564", "endLine": 49, "endColumn": 8, "suggestions": "604"}, {"ruleId": "558", "severity": 1, "message": "599", "line": 12, "column": 6, "nodeType": "564", "endLine": 12, "endColumn": 17, "suggestions": "605"}, {"ruleId": "606", "severity": 1, "message": "607", "line": 31, "column": 11, "nodeType": "588", "endLine": 31, "endColumn": 63}, {"ruleId": "606", "severity": 1, "message": "607", "line": 38, "column": 17, "nodeType": "588", "endLine": 38, "endColumn": 76}, {"ruleId": "547", "severity": 1, "message": "608", "line": 34, "column": 20, "nodeType": "549", "messageId": "550", "endLine": 34, "endColumn": 31}, {"ruleId": "552", "severity": 1, "message": "553", "line": 51, "column": 33, "nodeType": "545", "messageId": "554", "endLine": 51, "endColumn": 35}, {"ruleId": "609", "severity": 1, "message": "610", "line": 92, "column": 49, "nodeType": "611", "messageId": "612", "endLine": 92, "endColumn": 51}, {"ruleId": "558", "severity": 1, "message": "613", "line": 115, "column": 6, "nodeType": "564", "endLine": 115, "endColumn": 18, "suggestions": "614"}, {"ruleId": "609", "severity": 1, "message": "610", "line": 126, "column": 40, "nodeType": "611", "messageId": "612", "endLine": 126, "endColumn": 42}, {"ruleId": "558", "severity": 1, "message": "615", "line": 132, "column": 6, "nodeType": "564", "endLine": 132, "endColumn": 38, "suggestions": "616"}, {"ruleId": "558", "severity": 1, "message": "617", "line": 150, "column": 6, "nodeType": "564", "endLine": 150, "endColumn": 49, "suggestions": "618"}, {"ruleId": "558", "severity": 1, "message": "619", "line": 154, "column": 6, "nodeType": "564", "endLine": 154, "endColumn": 24, "suggestions": "620"}, {"ruleId": "558", "severity": 1, "message": "621", "line": 238, "column": 6, "nodeType": "564", "endLine": 238, "endColumn": 36, "suggestions": "622"}, {"ruleId": "589", "severity": 1, "message": "590", "line": 24, "column": 1, "nodeType": "591", "endLine": 405, "endColumn": 3, "suppressions": "623"}, {"ruleId": "547", "severity": 1, "message": "624", "line": 7, "column": 17, "nodeType": "549", "messageId": "550", "endLine": 7, "endColumn": 28}, {"ruleId": "547", "severity": 1, "message": "625", "line": 18, "column": 36, "nodeType": "549", "messageId": "550", "endLine": 18, "endColumn": 45}, {"ruleId": "547", "severity": 1, "message": "626", "line": 18, "column": 47, "nodeType": "549", "messageId": "550", "endLine": 18, "endColumn": 52}, {"ruleId": "547", "severity": 1, "message": "627", "line": 22, "column": 9, "nodeType": "549", "messageId": "550", "endLine": 22, "endColumn": 23}, {"ruleId": "558", "severity": 1, "message": "628", "line": 72, "column": 5, "nodeType": "564", "endLine": 72, "endColumn": 7, "suggestions": "629"}, {"ruleId": "547", "severity": 1, "message": "630", "line": 1, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 16}, {"ruleId": "547", "severity": 1, "message": "631", "line": 1, "column": 18, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 21}, {"ruleId": "547", "severity": 1, "message": "632", "line": 1, "column": 23, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 26}, {"ruleId": "547", "severity": 1, "message": "633", "line": 1, "column": 28, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 34}, {"ruleId": "547", "severity": 1, "message": "584", "line": 3, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 3, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "634", "line": 4, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 4, "endColumn": 24}, {"ruleId": "547", "severity": 1, "message": "635", "line": 4, "column": 26, "nodeType": "549", "messageId": "550", "endLine": 4, "endColumn": 38}, {"ruleId": "547", "severity": 1, "message": "636", "line": 5, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 5, "endColumn": 14}, {"ruleId": "547", "severity": 1, "message": "637", "line": 6, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 6, "endColumn": 14}, {"ruleId": "547", "severity": 1, "message": "638", "line": 7, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 7, "endColumn": 20}, {"ruleId": "558", "severity": 1, "message": "639", "line": 58, "column": 6, "nodeType": "564", "endLine": 58, "endColumn": 16, "suggestions": "640"}, {"ruleId": "547", "severity": 1, "message": "630", "line": 1, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 16}, {"ruleId": "547", "severity": 1, "message": "631", "line": 1, "column": 18, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 21}, {"ruleId": "547", "severity": 1, "message": "632", "line": 1, "column": 23, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 26}, {"ruleId": "547", "severity": 1, "message": "633", "line": 1, "column": 28, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 34}, {"ruleId": "547", "severity": 1, "message": "584", "line": 1, "column": 36, "nodeType": "549", "messageId": "550", "endLine": 1, "endColumn": 43}, {"ruleId": "547", "severity": 1, "message": "634", "line": 3, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 3, "endColumn": 24}, {"ruleId": "547", "severity": 1, "message": "635", "line": 3, "column": 26, "nodeType": "549", "messageId": "550", "endLine": 3, "endColumn": 38}, {"ruleId": "547", "severity": 1, "message": "636", "line": 4, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 4, "endColumn": 14}, {"ruleId": "547", "severity": 1, "message": "637", "line": 5, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 5, "endColumn": 14}, {"ruleId": "547", "severity": 1, "message": "638", "line": 6, "column": 10, "nodeType": "549", "messageId": "550", "endLine": 6, "endColumn": 20}, {"ruleId": "547", "severity": 1, "message": "641", "line": 4, "column": 7, "nodeType": "549", "messageId": "550", "endLine": 4, "endColumn": 41}, {"ruleId": "547", "severity": 1, "message": "642", "line": 28, "column": 7, "nodeType": "549", "messageId": "550", "endLine": 28, "endColumn": 46}, {"ruleId": "547", "severity": 1, "message": "643", "line": 36, "column": 7, "nodeType": "549", "messageId": "550", "endLine": 36, "endColumn": 44}, {"ruleId": "558", "severity": 1, "message": "644", "line": 35, "column": 5, "nodeType": "564", "endLine": 35, "endColumn": 7, "suggestions": "645"}, {"ruleId": "558", "severity": 1, "message": "646", "line": 22, "column": 6, "nodeType": "564", "endLine": 22, "endColumn": 36, "suggestions": "647"}, {"ruleId": "576", "severity": 1, "message": "648", "line": 2, "column": 60, "nodeType": "649", "messageId": "579", "endLine": 2, "endColumn": 61, "suggestions": "650"}, {"ruleId": "576", "severity": 1, "message": "648", "line": 4, "column": 70, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 71, "suggestions": "651"}, {"ruleId": "576", "severity": 1, "message": "652", "line": 4, "column": 83, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 84, "suggestions": "653"}, {"ruleId": "576", "severity": 1, "message": "654", "line": 4, "column": 87, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 88, "suggestions": "655"}, {"ruleId": "576", "severity": 1, "message": "656", "line": 4, "column": 89, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 90, "suggestions": "657"}, {"ruleId": "576", "severity": 1, "message": "658", "line": 4, "column": 91, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 92, "suggestions": "659"}, {"ruleId": "576", "severity": 1, "message": "660", "line": 4, "column": 93, "nodeType": "649", "messageId": "579", "endLine": 4, "endColumn": 94, "suggestions": "661"}, {"ruleId": "576", "severity": 1, "message": "662", "line": 6, "column": 44, "nodeType": "649", "messageId": "579", "endLine": 6, "endColumn": 45, "suggestions": "663"}, {"ruleId": "576", "severity": 1, "message": "662", "line": 6, "column": 69, "nodeType": "649", "messageId": "579", "endLine": 6, "endColumn": 70, "suggestions": "664"}, {"ruleId": "576", "severity": 1, "message": "662", "line": 6, "column": 78, "nodeType": "649", "messageId": "579", "endLine": 6, "endColumn": 79, "suggestions": "665"}, {"ruleId": "576", "severity": 1, "message": "662", "line": 6, "column": 82, "nodeType": "649", "messageId": "579", "endLine": 6, "endColumn": 83, "suggestions": "666"}, {"ruleId": "576", "severity": 1, "message": "654", "line": 7, "column": 46, "nodeType": "649", "messageId": "579", "endLine": 7, "endColumn": 47, "suggestions": "667"}, {"ruleId": "576", "severity": 1, "message": "656", "line": 7, "column": 48, "nodeType": "649", "messageId": "579", "endLine": 7, "endColumn": 49, "suggestions": "668"}, {"ruleId": "576", "severity": 1, "message": "652", "line": 7, "column": 60, "nodeType": "649", "messageId": "579", "endLine": 7, "endColumn": 61, "suggestions": "669"}, {"ruleId": "576", "severity": 1, "message": "670", "line": 7, "column": 62, "nodeType": "649", "messageId": "579", "endLine": 7, "endColumn": 63, "suggestions": "671"}, {"ruleId": "576", "severity": 1, "message": "672", "line": 7, "column": 65, "nodeType": "649", "messageId": "579", "endLine": 7, "endColumn": 66, "suggestions": "673"}, {"ruleId": "589", "severity": 1, "message": "590", "line": 44, "column": 1, "nodeType": "591", "endLine": 240, "endColumn": 3}, {"ruleId": "558", "severity": 1, "message": "674", "line": 78, "column": 6, "nodeType": "564", "endLine": 78, "endColumn": 8, "suggestions": "675"}, {"ruleId": "558", "severity": 1, "message": "676", "line": 107, "column": 6, "nodeType": "564", "endLine": 107, "endColumn": 19, "suggestions": "677"}, {"ruleId": "558", "severity": 1, "message": "678", "line": 107, "column": 7, "nodeType": "649", "endLine": 107, "endColumn": 10}, {"ruleId": "589", "severity": 1, "message": "590", "line": 2, "column": 1, "nodeType": "591", "endLine": 11, "endColumn": 3}, {"ruleId": "547", "severity": 1, "message": "633", "line": 3, "column": 26, "nodeType": "549", "messageId": "550", "endLine": 3, "endColumn": 32}, {"ruleId": "547", "severity": 1, "message": "584", "line": 4, "column": 8, "nodeType": "549", "messageId": "550", "endLine": 4, "endColumn": 15}, {"ruleId": "558", "severity": 1, "message": "679", "line": 75, "column": 6, "nodeType": "564", "endLine": 75, "endColumn": 22, "suggestions": "680"}, "no-useless-concat", "Unexpected string concatenation of literals.", "BinaryExpression", "unexpectedConcat", "@typescript-eslint/no-unused-vars", "'baseUrl' is assigned a value but never used.", "Identifier", "unusedVar", "'path' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "unexpected", "'formatDownloadFileName' is defined but never used.", "'download' is defined but never used.", "'upload' is defined but never used.", "react-hooks/exhaustive-deps", "Assignments to the 'interval' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userPermissions' is assigned a value but never used.", "'userPreference' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'isLoading'. Either include them or remove the dependency array.", "ArrayExpression", ["681"], "React Hook useEffect has missing dependencies: 'authState?.accessToken?.claims.tid', 'dispatch', 'fetchEndpointsAndUser', 'fetchMenusAndSites', 'fetchTenantContext', and 'menuItems.length'. Either include them or remove the dependency array.", ["682"], "'RouteConfigType' is defined but never used.", "React Hook useEffect has a missing dependency: 'authState'. Either include it or remove the dependency array.", ["683"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "UnaryExpression", "'tenant' is assigned a value but never used.", "'tenantContext' is assigned a value but never used.", "'authState' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\'.", "TemplateElement", "unnecessaryEscape", ["684", "685"], ["686", "687"], "'ControlOutlined' is defined but never used.", "'SyncOutlined' is defined but never used.", "'Tooltip' is defined but never used.", "Expected '!==' and instead saw '!='.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "import/no-anonymous-default-export", "Assign arrow function to a variable before exporting as module default", "ExportDefaultDeclaration", "'useState' is defined but never used.", "'FileRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'fileUpload'. Either include it or remove the dependency array.", ["688"], "'Menu' is defined but never used.", "React Hook useEffect has missing dependencies: 'authState?.accessToken?.claims.tid' and 'triggerGetFirmLogoAndName'. Either include them or remove the dependency array.", ["689"], "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["690"], "React Hook useCallback has missing dependencies: 'bulkDownload' and 'downloadAndSaveFile'. Either include them or remove the dependency array. If 'downloadAndSaveFile' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["691"], "React Hook useEffect has a missing dependency: 'triggerFileDownload'. Either include it or remove the dependency array.", ["692"], ["693"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'setKeyValue' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "React Hook useEffect has missing dependencies: 'checkAll', 'items', 'previouslyCheckedList', 'previouslySelectedDropdownValue', 'props.checkedList', 'props.hasCheckbox', 'props.selectedList', and 'selectedDropdownValue'. Either include them or remove the dependency array. If 'setCheckedList' needs the current value of 'props.checkedList', you can also switch to useReducer instead of useState and read 'props.checkedList' in the reducer.", ["694"], "React Hook useEffect has missing dependencies: 'historyOptions', 'props', 'selectedDropdownValue', and 'totalRecordCount'. Either include them or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["695"], "React Hook useEffect has missing dependencies: 'currentPage', 'loadMore', 'pageCount', and 'props'. Either include them or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["696"], "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["697"], "React Hook React.useMemo has a missing dependency: 'props'. Either include it or remove the dependency array. Outer scope values like 'config.inputDebounceInterval' aren't valid dependencies because mutating them doesn't re-render the component.", ["698"], ["699"], "'useCallback' is defined but never used.", "'isLoading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'mode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getPaginatedRecords'. Either include it or remove the dependency array.", ["700"], "'Button' is defined but never used.", "'Col' is defined but never used.", "'Row' is defined but never used.", "'Select' is defined but never used.", "'SearchOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "'Search' is defined but never used.", "'styles' is defined but never used.", "'BiSortDown' is defined but never used.", "React Hook useEffect has a missing dependency: 'onLastFile'. Either include it or remove the dependency array. If 'onLastFile' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["701"], "'RequestsMenuConfig' is assigned a value but never used.", "'AnnouncementsMenuConfig' is assigned a value but never used.", "'DiscussionsMenuConfig' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'triggerGetFirmLogoAndName'. Either include it or remove the dependency array.", ["702"], "React Hook useEffect has a missing dependency: 'hasErrored'. Either include it or remove the dependency array.", ["703"], "Unnecessary escape character: \\..", "Literal", ["704", "705"], ["706", "707"], "Unnecessary escape character: \\$.", ["708", "709"], "Unnecessary escape character: \\(.", ["710", "711"], "Unnecessary escape character: \\).", ["712", "713"], "Unnecessary escape character: \\*.", ["714", "715"], "Unnecessary escape character: \\+.", ["716", "717"], "Unnecessary escape character: \\\".", ["718", "719"], ["720", "721"], ["722", "723"], ["724", "725"], ["726", "727"], ["728", "729"], ["730", "731"], "Unnecessary escape character: \\?.", ["732", "733"], "Unnecessary escape character: \\/.", ["734", "735"], "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["736"], "React Hook React.useMemo has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useMemo call and refer to those specific props inside React.useMemo.", ["737"], "The 500 literal is not a valid dependency because it never changes. You can safely remove it.", "React Hook useEffect has missing dependencies: 'onDataChange' and 'onTitleUpdate'. Either include them or remove the dependency array. If 'onDataChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["738"], {"desc": "739", "fix": "740"}, {"desc": "741", "fix": "742"}, {"desc": "743", "fix": "744"}, {"messageId": "745", "fix": "746", "desc": "747"}, {"messageId": "748", "fix": "749", "desc": "750"}, {"messageId": "745", "fix": "751", "desc": "747"}, {"messageId": "748", "fix": "752", "desc": "750"}, {"desc": "753", "fix": "754"}, {"desc": "755", "fix": "756"}, {"desc": "757", "fix": "758"}, {"desc": "759", "fix": "760"}, {"desc": "761", "fix": "762"}, {"desc": "763", "fix": "764"}, {"desc": "765", "fix": "766"}, {"desc": "767", "fix": "768"}, {"desc": "769", "fix": "770"}, {"desc": "771", "fix": "772"}, {"desc": "773", "fix": "774"}, {"kind": "775", "justification": "776"}, {"desc": "777", "fix": "778"}, {"desc": "779", "fix": "780"}, {"desc": "781", "fix": "782"}, {"desc": "783", "fix": "784"}, {"messageId": "745", "fix": "785", "desc": "747"}, {"messageId": "748", "fix": "786", "desc": "750"}, {"messageId": "745", "fix": "787", "desc": "747"}, {"messageId": "748", "fix": "788", "desc": "750"}, {"messageId": "745", "fix": "789", "desc": "747"}, {"messageId": "748", "fix": "790", "desc": "750"}, {"messageId": "745", "fix": "791", "desc": "747"}, {"messageId": "748", "fix": "792", "desc": "750"}, {"messageId": "745", "fix": "793", "desc": "747"}, {"messageId": "748", "fix": "794", "desc": "750"}, {"messageId": "745", "fix": "795", "desc": "747"}, {"messageId": "748", "fix": "796", "desc": "750"}, {"messageId": "745", "fix": "797", "desc": "747"}, {"messageId": "748", "fix": "798", "desc": "750"}, {"messageId": "745", "fix": "799", "desc": "747"}, {"messageId": "748", "fix": "800", "desc": "750"}, {"messageId": "745", "fix": "801", "desc": "747"}, {"messageId": "748", "fix": "802", "desc": "750"}, {"messageId": "745", "fix": "803", "desc": "747"}, {"messageId": "748", "fix": "804", "desc": "750"}, {"messageId": "745", "fix": "805", "desc": "747"}, {"messageId": "748", "fix": "806", "desc": "750"}, {"messageId": "745", "fix": "807", "desc": "747"}, {"messageId": "748", "fix": "808", "desc": "750"}, {"messageId": "745", "fix": "809", "desc": "747"}, {"messageId": "748", "fix": "810", "desc": "750"}, {"messageId": "745", "fix": "811", "desc": "747"}, {"messageId": "748", "fix": "812", "desc": "750"}, {"messageId": "745", "fix": "813", "desc": "747"}, {"messageId": "748", "fix": "814", "desc": "750"}, {"messageId": "745", "fix": "815", "desc": "747"}, {"messageId": "748", "fix": "816", "desc": "750"}, {"desc": "817", "fix": "818"}, {"desc": "819", "fix": "820"}, {"desc": "821", "fix": "822"}, "Update the dependencies array to be: [dispatch, error, isLoading]", {"range": "823", "text": "824"}, "Update the dependencies array to be: [authState?.isAuthenticated, tenant, endPoints, authState?.accessToken?.claims.tid, dispatch, menuItems.length, fetchEndpointsAndUser, fetchMenusAndSites, fetchTenantContext]", {"range": "825", "text": "826"}, "Update the dependencies array to be: [oktaAuth, authState?.isAuthenticated, authState]", {"range": "827", "text": "828"}, "removeEscape", {"range": "829", "text": "776"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "830", "text": "831"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "832", "text": "776"}, {"range": "833", "text": "831"}, "Update the dependencies array to be: [fileUpload, files]", {"range": "834", "text": "835"}, "Update the dependencies array to be: [authState?.accessToken?.claims.tid, authState?.isAuthenticated, triggerGetFirmLogoAndName]", {"range": "836", "text": "837"}, "Update the dependencies array to be: [authState, navigate, oktaAuth]", {"range": "838", "text": "839"}, "Update the dependencies array to be: [bulkDownload, downloadAndSaveFile, downloadType, selectedFiles]", {"range": "840", "text": "841"}, "Update the dependencies array to be: [triggerFileDownload]", {"range": "842", "text": "843"}, "Update the dependencies array to be: [authState, navigate]", {"range": "844", "text": "845"}, "Update the dependencies array to be: [checkAll, items, previouslyCheckedList, previouslySelectedDropdownValue, props.checkedList, props.data, props.hasCheckbox, props.selectedList, selectedDropdownValue]", {"range": "846", "text": "847"}, "Update the dependencies array to be: [checkedList, historyOptions, props, props.deletedList, selectedDropdownValue, totalRecordCount]", {"range": "848", "text": "849"}, "Update the dependencies array to be: [props.removedItem, items, props.isLoading, props, pageCount, currentPage, loadMore]", {"range": "850", "text": "851"}, "Update the dependencies array to be: [props, totalRecordCount]", {"range": "852", "text": "853"}, "Update the dependencies array to be: [props]", {"range": "854", "text": "855"}, "directive", "", "Update the dependencies array to be: [getPaginatedRecords]", {"range": "856", "text": "857"}, "Update the dependencies array to be: [fileList, onLastFile]", {"range": "858", "text": "859"}, "Update the dependencies array to be: [triggerGetFirmLogoAndName]", {"range": "860", "text": "861"}, "Update the dependencies array to be: [completed, hasErrored, onComplete, total]", {"range": "862", "text": "863"}, {"range": "864", "text": "776"}, {"range": "865", "text": "831"}, {"range": "866", "text": "776"}, {"range": "867", "text": "831"}, {"range": "868", "text": "776"}, {"range": "869", "text": "831"}, {"range": "870", "text": "776"}, {"range": "871", "text": "831"}, {"range": "872", "text": "776"}, {"range": "873", "text": "831"}, {"range": "874", "text": "776"}, {"range": "875", "text": "831"}, {"range": "876", "text": "776"}, {"range": "877", "text": "831"}, {"range": "878", "text": "776"}, {"range": "879", "text": "831"}, {"range": "880", "text": "776"}, {"range": "881", "text": "831"}, {"range": "882", "text": "776"}, {"range": "883", "text": "831"}, {"range": "884", "text": "776"}, {"range": "885", "text": "831"}, {"range": "886", "text": "776"}, {"range": "887", "text": "831"}, {"range": "888", "text": "776"}, {"range": "889", "text": "831"}, {"range": "890", "text": "776"}, {"range": "891", "text": "831"}, {"range": "892", "text": "776"}, {"range": "893", "text": "831"}, {"range": "894", "text": "776"}, {"range": "895", "text": "831"}, "Update the dependencies array to be: [fetchInitialData]", {"range": "896", "text": "897"}, "Update the dependencies array to be: [options, props]", {"range": "898", "text": "899"}, "Update the dependencies array to be: [forManageFiles, onDataChange, onTitleUpdate]", {"range": "900", "text": "901"}, [3630, 3637], "[dispatch, error, isLoading]", [4462, 4509], "[authState?.isAuthenticated, tenant, endPoints, authState?.accessToken?.claims.tid, dispatch, menuItems.length, fetchEndpointsAndUser, fetchMenusAndSites, fetchTenantContext]", [791, 842], "[oktaAuth, authState?.isAuthenticated, authState]", [1745, 1746], [1745, 1745], "\\", [2318, 2319], [2318, 2318], [1988, 1995], "[fileUpload, files]", [1467, 1495], "[authState?.accessToken?.claims.tid, authState?.isAuthenticated, triggerGetFirmLogoAndName]", [452, 473], "[authState, navigate, oktaAuth]", [1624, 1653], "[bulkDownload, downloadAndSaveFile, downloadType, selectedFiles]", [1764, 1766], "[triggerFileDownload]", [382, 393], "[authState, navigate]", [4845, 4857], "[checkAll, items, previouslyCheckedList, previouslySelectedDropdownValue, props.checkedList, props.data, props.hasCheckbox, props.selectedList, selectedDropdownValue]", [5571, 5603], "[checkedList, historyOptions, props, props.deletedList, selectedDropdownV<PERSON><PERSON>, totalRecordCount]", [6049, 6092], "[props.removedItem, items, props.isLoading, props, pageCount, currentPage, loadMore]", [6189, 6207], "[props, totalRecordCount]", [9387, 9417], "[props]", [2303, 2305], "[getPaginatedRecords]", [2038, 2048], "[fileList, onLastFile]", [1543, 1545], "[triggerGetFirmLogoAndName]", [1039, 1069], "[completed, hasErrored, onComplete, total]", [104, 105], [104, 104], [286, 287], [286, 286], [299, 300], [299, 299], [303, 304], [303, 303], [305, 306], [305, 305], [307, 308], [307, 307], [309, 310], [309, 309], [400, 401], [400, 400], [425, 426], [425, 425], [434, 435], [434, 434], [438, 439], [438, 438], [581, 582], [581, 581], [583, 584], [583, 583], [595, 596], [595, 595], [597, 598], [597, 597], [600, 601], [600, 600], [2454, 2456], "[fetchInitialData]", [3463, 3476], "[options, props]", [2563, 2579], "[forManageFiles, onDataChange, onTitleUpdate]"]