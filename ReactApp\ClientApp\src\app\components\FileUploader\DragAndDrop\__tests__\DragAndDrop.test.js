import { shallow, mount } from "enzyme";
import React from "react";
import Drag<PERSON>nd<PERSON><PERSON> from "..";
import { Button } from "antd";
import { render } from "@testing-library/react";
import { getByTestId, fireEvent } from "@testing-library/dom";

describe("<DragAndDrop />", () => {
  it("should render drag n drop component", () => {
    const component = shallow(<DragAndDrop />);
    expect(component.html()).not.toBe(null);
  });

  it("should render with props", () => {
    const component = shallow(<DragAndDrop hasUrlUploadPermsission={false} onURLOptionSelect={() => null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should have FILE Uploader button", () => {
    const component = mount(<DragAndDrop hasUrlUploadPermsission={false} onURLOptionSelect={() => null} />);
    //console.log(component.find(Button).at(0).props());
    expect(component.find(Button).at(0).props().children).toEqual("FILE Uploader");
  });

  it("should fire FILE Uploader button Click", () => {
    const { container } = render(<DragAndDrop />);
    const button = getByTestId(container, "expandViewButton");
    fireEvent.click(button);
    const expandedView = getByTestId(container, "expandedView");
    expect(expandedView).not.toBe(null);
  });

  it("should have a close button to close expanded view", () => {
    const { container } = render(<DragAndDrop />);
    const button = getByTestId(container, "expandViewButton");
    fireEvent.click(button);
    const buttonClose = getByTestId(container, "expandCloseButton");
    expect(buttonClose).not.toBe(null);
  });

  it("should have a File upload Browse Button", () => {
    const { container } = render(<DragAndDrop />);
    const button = getByTestId(container, "expandViewButton");
    fireEvent.click(button);
    const drowseButton = getByTestId(container, "fileUploadBrowseButton");
    expect(drowseButton).not.toBe(null);
  });
});
