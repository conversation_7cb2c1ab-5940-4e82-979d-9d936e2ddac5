import { useLazyGetSitesInfiniteRecordsQuery } from '@app/api/sitesApiSlice';
import InfinitySelect from '@app/components/InfinitySelect';
import { Col, Form, Row, Select } from 'antd';
import Tooltip from 'antd/es/tooltip';
import React from 'react';
import FileList from './FileList';
import styles from './index.module.less';
import { UploaderSubmitProps } from './types';
import { InfinitySelectGetOptions } from "@app/components/InfinitySelect/types";
import logger from "@app/utils/logger";
const LIMIT = 10;

export const UploaderSubmit: React.FC<UploaderSubmitProps> = ({ fileList, form, onFinish, fileEvents, forManageFiles = false, onFormChange = () => null, siteId }) => {
  const [fetchInfiniteRecords] = useLazyGetSitesInfiniteRecordsQuery();

  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }



    const result = await fetchInfiniteRecords({
      limit: LIMIT,
      offset: page - 1,
      ...transformFilters,
    })
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
    return result;
  };
  return (
    <Form size="middle" name="fileDetails" layout="vertical" form={form} onFinish={(values) => onFinish(values, fileList)}>
      <Row>
        <Col span={24}>
          <div className={styles.YJ_CP_UPLOADER_ITEM_TITLE}>SITE</div>
          <div className={styles.YJ_CP_UPLOADER_ITEM_SELECT}>
            <InfinitySelect
              getPaginatedRecords={getPaginatedRecords}
              formatValue={(value) => {
                return `${value.name}`;
              }}
              defaultValues={[siteId]}
              disabled
              placeholder="Site Name"
              onChange={() => null}
              notFoundContent={''}
              notLoadContent={''}
            />
          </div>
        </Col>
        <Col span={24}>
          <Row gutter={0}>
            <FileList fileList={fileList ?? []} fileEvents={fileEvents} forManageFiles={forManageFiles} onDataChange={onFormChange} />
          </Row>
        </Col>
        {/* <Col span={24}>
          <div className={styles.YJ_CP_UPLOADER_ITEM_TITLE}>Category</div>
          <div className={styles.YJ_CP_UPLOADER_ITEM_SELECT}>
            <Select disabled placeholder="Select Category" />
          </div>
        </Col> */}
      </Row>
    </Form>
  );
};
