{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as v, IconWrap as R, toIconName as T, getTabIndex as D, svgIconPropType as P } from \"@progress/kendo-react-common\";\nimport { DrawerContext as j } from \"./context/DrawerContext.mjs\";\nconst d = e.forwardRef((s, p) => {\n  const {\n      className: u,\n      style: f,\n      text: l,\n      icon: n,\n      svgIcon: c,\n      separator: b,\n      disabled: a,\n      selected: I,\n      onSelect: o,\n      index: m,\n      tabIndex: x,\n      children: k,\n      ...g\n    } = s,\n    {\n      expanded: F,\n      mini: H,\n      item: N\n    } = e.useContext(j),\n    r = e.useRef(null),\n    i = e.useCallback(() => {\n      r.current && r.current.focus();\n    }, []);\n  e.useImperativeHandle(p, () => ({\n    element: r.current,\n    focus: i,\n    props: s\n  }));\n  const w = v(\"k-drawer-item\", {\n      \"k-selected\": I,\n      \"k-disabled\": a\n    }, u),\n    C = e.useCallback(E => {\n      if (o && !a) {\n        const h = {\n          element: r.current,\n          focus: i,\n          props: s\n        };\n        o(h, m, E);\n      }\n    }, [o, m, a]),\n    y = /* @__PURE__ */e.createElement(e.Fragment, null, (n || c) && /* @__PURE__ */e.createElement(R, {\n      name: n && T(n),\n      icon: c\n    }), /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-item-text\"\n    }, l));\n  return b ? /* @__PURE__ */e.createElement(\"li\", {\n    className: \"k-drawer-item k-drawer-separator\",\n    role: \"separator\"\n  }) : /* @__PURE__ */e.createElement(\"li\", {\n    ref: r,\n    className: w,\n    style: f,\n    role: \"menuitem\",\n    \"aria-label\": l,\n    \"aria-disabled\": a,\n    onClick: C,\n    tabIndex: D(x, a),\n    ...g\n  }, N ? k : y);\n});\nd.propTypes = {\n  className: t.string,\n  style: t.object,\n  text: t.string,\n  icon: t.string,\n  svgIcon: P,\n  separator: t.bool,\n  selected: t.bool,\n  disabled: t.bool,\n  targetItem: t.any\n};\nd.displayName = \"KendoDrawerItem\";\nexport { d as DrawerItem };", "map": {"version": 3, "names": ["e", "t", "classNames", "v", "IconWrap", "R", "toIconName", "T", "getTabIndex", "D", "svgIconPropType", "P", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "j", "d", "forwardRef", "s", "p", "className", "u", "style", "f", "text", "l", "icon", "n", "svgIcon", "c", "separator", "b", "disabled", "a", "selected", "I", "onSelect", "o", "index", "m", "tabIndex", "x", "children", "k", "g", "expanded", "F", "mini", "H", "item", "N", "useContext", "r", "useRef", "i", "useCallback", "current", "focus", "useImperativeHandle", "element", "props", "w", "C", "E", "h", "y", "createElement", "Fragment", "name", "role", "ref", "onClick", "propTypes", "string", "object", "bool", "targetItem", "any", "displayName", "DrawerItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/drawer/DrawerItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as v, IconWrap as R, toIconName as T, getTabIndex as D, svgIconPropType as P } from \"@progress/kendo-react-common\";\nimport { DrawerContext as j } from \"./context/DrawerContext.mjs\";\nconst d = e.forwardRef((s, p) => {\n  const {\n    className: u,\n    style: f,\n    text: l,\n    icon: n,\n    svgIcon: c,\n    separator: b,\n    disabled: a,\n    selected: I,\n    onSelect: o,\n    index: m,\n    tabIndex: x,\n    children: k,\n    ...g\n  } = s, { expanded: F, mini: H, item: N } = e.useContext(j), r = e.useRef(null), i = e.useCallback(() => {\n    r.current && r.current.focus();\n  }, []);\n  e.useImperativeHandle(\n    p,\n    () => ({\n      element: r.current,\n      focus: i,\n      props: s\n    })\n  );\n  const w = v(\n    \"k-drawer-item\",\n    {\n      \"k-selected\": I,\n      \"k-disabled\": a\n    },\n    u\n  ), C = e.useCallback(\n    (E) => {\n      if (o && !a) {\n        const h = {\n          element: r.current,\n          focus: i,\n          props: s\n        };\n        o(h, m, E);\n      }\n    },\n    [o, m, a]\n  ), y = /* @__PURE__ */ e.createElement(e.Fragment, null, (n || c) && /* @__PURE__ */ e.createElement(R, { name: n && T(n), icon: c }), /* @__PURE__ */ e.createElement(\"span\", { className: \"k-item-text\" }, l));\n  return b ? /* @__PURE__ */ e.createElement(\"li\", { className: \"k-drawer-item k-drawer-separator\", role: \"separator\" }) : /* @__PURE__ */ e.createElement(\n    \"li\",\n    {\n      ref: r,\n      className: w,\n      style: f,\n      role: \"menuitem\",\n      \"aria-label\": l,\n      \"aria-disabled\": a,\n      onClick: C,\n      tabIndex: D(x, a),\n      ...g\n    },\n    N ? k : y\n  );\n});\nd.propTypes = {\n  className: t.string,\n  style: t.object,\n  text: t.string,\n  icon: t.string,\n  svgIcon: P,\n  separator: t.bool,\n  selected: t.bool,\n  disabled: t.bool,\n  targetItem: t.any\n};\nd.displayName = \"KendoDrawerItem\";\nexport {\n  d as DrawerItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,8BAA8B;AACtI,SAASC,aAAa,IAAIC,CAAC,QAAQ,6BAA6B;AAChE,MAAMC,CAAC,GAAGd,CAAC,CAACe,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MACJC,SAAS,EAAEC,CAAC;MACZC,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,IAAI,EAAEC,CAAC;MACPC,OAAO,EAAEC,CAAC;MACVC,SAAS,EAAEC,CAAC;MACZC,QAAQ,EAAEC,CAAC;MACXC,QAAQ,EAAEC,CAAC;MACXC,QAAQ,EAAEC,CAAC;MACXC,KAAK,EAAEC,CAAC;MACRC,QAAQ,EAAEC,CAAC;MACXC,QAAQ,EAAEC,CAAC;MACX,GAAGC;IACL,CAAC,GAAG1B,CAAC;IAAE;MAAE2B,QAAQ,EAAEC,CAAC;MAAEC,IAAI,EAAEC,CAAC;MAAEC,IAAI,EAAEC;IAAE,CAAC,GAAGhD,CAAC,CAACiD,UAAU,CAACpC,CAAC,CAAC;IAAEqC,CAAC,GAAGlD,CAAC,CAACmD,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGpD,CAAC,CAACqD,WAAW,CAAC,MAAM;MACtGH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACNvD,CAAC,CAACwD,mBAAmB,CACnBvC,CAAC,EACD,OAAO;IACLwC,OAAO,EAAEP,CAAC,CAACI,OAAO;IAClBC,KAAK,EAAEH,CAAC;IACRM,KAAK,EAAE1C;EACT,CAAC,CACH,CAAC;EACD,MAAM2C,CAAC,GAAGxD,CAAC,CACT,eAAe,EACf;MACE,YAAY,EAAE8B,CAAC;MACf,YAAY,EAAEF;IAChB,CAAC,EACDZ,CACF,CAAC;IAAEyC,CAAC,GAAG5D,CAAC,CAACqD,WAAW,CACjBQ,CAAC,IAAK;MACL,IAAI1B,CAAC,IAAI,CAACJ,CAAC,EAAE;QACX,MAAM+B,CAAC,GAAG;UACRL,OAAO,EAAEP,CAAC,CAACI,OAAO;UAClBC,KAAK,EAAEH,CAAC;UACRM,KAAK,EAAE1C;QACT,CAAC;QACDmB,CAAC,CAAC2B,CAAC,EAAEzB,CAAC,EAAEwB,CAAC,CAAC;MACZ;IACF,CAAC,EACD,CAAC1B,CAAC,EAAEE,CAAC,EAAEN,CAAC,CACV,CAAC;IAAEgC,CAAC,GAAG,eAAgB/D,CAAC,CAACgE,aAAa,CAAChE,CAAC,CAACiE,QAAQ,EAAE,IAAI,EAAE,CAACxC,CAAC,IAAIE,CAAC,KAAK,eAAgB3B,CAAC,CAACgE,aAAa,CAAC3D,CAAC,EAAE;MAAE6D,IAAI,EAAEzC,CAAC,IAAIlB,CAAC,CAACkB,CAAC,CAAC;MAAED,IAAI,EAAEG;IAAE,CAAC,CAAC,EAAE,eAAgB3B,CAAC,CAACgE,aAAa,CAAC,MAAM,EAAE;MAAE9C,SAAS,EAAE;IAAc,CAAC,EAAEK,CAAC,CAAC,CAAC;EAChN,OAAOM,CAAC,GAAG,eAAgB7B,CAAC,CAACgE,aAAa,CAAC,IAAI,EAAE;IAAE9C,SAAS,EAAE,kCAAkC;IAAEiD,IAAI,EAAE;EAAY,CAAC,CAAC,GAAG,eAAgBnE,CAAC,CAACgE,aAAa,CACtJ,IAAI,EACJ;IACEI,GAAG,EAAElB,CAAC;IACNhC,SAAS,EAAEyC,CAAC;IACZvC,KAAK,EAAEC,CAAC;IACR8C,IAAI,EAAE,UAAU;IAChB,YAAY,EAAE5C,CAAC;IACf,eAAe,EAAEQ,CAAC;IAClBsC,OAAO,EAAET,CAAC;IACVtB,QAAQ,EAAE7B,CAAC,CAAC8B,CAAC,EAAER,CAAC,CAAC;IACjB,GAAGW;EACL,CAAC,EACDM,CAAC,GAAGP,CAAC,GAAGsB,CACV,CAAC;AACH,CAAC,CAAC;AACFjD,CAAC,CAACwD,SAAS,GAAG;EACZpD,SAAS,EAAEjB,CAAC,CAACsE,MAAM;EACnBnD,KAAK,EAAEnB,CAAC,CAACuE,MAAM;EACflD,IAAI,EAAErB,CAAC,CAACsE,MAAM;EACd/C,IAAI,EAAEvB,CAAC,CAACsE,MAAM;EACd7C,OAAO,EAAEf,CAAC;EACViB,SAAS,EAAE3B,CAAC,CAACwE,IAAI;EACjBzC,QAAQ,EAAE/B,CAAC,CAACwE,IAAI;EAChB3C,QAAQ,EAAE7B,CAAC,CAACwE,IAAI;EAChBC,UAAU,EAAEzE,CAAC,CAAC0E;AAChB,CAAC;AACD7D,CAAC,CAAC8D,WAAW,GAAG,iBAAiB;AACjC,SACE9D,CAAC,IAAI+D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}