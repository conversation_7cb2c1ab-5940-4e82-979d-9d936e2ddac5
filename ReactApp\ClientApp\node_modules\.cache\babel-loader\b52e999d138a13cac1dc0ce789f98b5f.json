{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as C from \"react\";\nimport { createId as g, ZERO_LEVEL_ZERO_ITEM_ID as y } from \"./itemsIdsUtils.mjs\";\nimport { MenuItem as x } from \"../components/MenuItem.mjs\";\nfunction L(e, n) {\n  if (e && e.length > 0) return {\n    items: c(e),\n    inputItems: e\n  };\n  if (s(n).length > 0) {\n    const t = R(s(n));\n    return {\n      items: c(t),\n      inputItems: t\n    };\n  }\n  return {\n    items: [],\n    inputItems: []\n  };\n}\nfunction h(e, n) {\n  const t = {},\n    {\n      text: i,\n      url: o,\n      icon: r,\n      svgIcon: d,\n      disabled: u,\n      cssClass: f,\n      cssStyle: a,\n      render: l,\n      linkRender: p,\n      contentRender: m,\n      data: I,\n      separator: v\n    } = e || n.props;\n  return i !== void 0 && (t.text = i), o !== void 0 && (t.url = o), r !== void 0 && (t.icon = r), d !== void 0 && (t.svgIcon = d), u !== void 0 && (t.disabled = u), f !== void 0 && (t.cssClass = f), a !== void 0 && (t.cssStyle = a), l !== void 0 && (t.render = l), p !== void 0 && (t.linkRender = p), m !== void 0 && (t.contentRender = m), I !== void 0 && (t.data = I), v !== void 0 && (t.separator = v), t;\n}\nfunction s(e) {\n  return C.Children.toArray(e).filter(n => n && n.type === x);\n}\nfunction R(e) {\n  const n = [];\n  for (let t = 0; t < e.length; t++) {\n    const i = e[t],\n      o = h(void 0, i),\n      r = R(s(i.props.children));\n    r.length > 0 && (o.items = r), n.push(o);\n  }\n  return n;\n}\nfunction c(e, n) {\n  const t = [];\n  for (let i = 0; i < e.length; i++) {\n    const o = e[i],\n      r = h(o);\n    r.id = g(i.toString(), n), r.isLastFromSiblings = i === e.length - 1, r.separator = o.separator === !0, r.items = E(o, r), t.push(r);\n  }\n  return t;\n}\nfunction E(e, n) {\n  return e.contentRender ? [{\n    contentParentItemId: n.id,\n    id: g(y, n.id),\n    isLastFromSiblings: !0,\n    separator: !1,\n    contentRender: e.contentRender,\n    items: []\n  }] : e.items ? c(e.items, n.id) : [];\n}\nexport { L as prepareInputItemsForInternalWork };", "map": {"version": 3, "names": ["C", "createId", "g", "ZERO_LEVEL_ZERO_ITEM_ID", "y", "MenuItem", "x", "L", "e", "n", "length", "items", "c", "inputItems", "s", "t", "R", "h", "text", "i", "url", "o", "icon", "r", "svgIcon", "d", "disabled", "u", "cssClass", "f", "cssStyle", "a", "render", "l", "linkRender", "p", "contentRender", "m", "data", "I", "separator", "v", "props", "Children", "toArray", "filter", "type", "children", "push", "id", "toString", "isLastFromSiblings", "E", "contentParentItemId", "prepareInputItemsForInternalWork"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/prepareInputItemsForInternalWork.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as C from \"react\";\nimport { createId as g, ZERO_LEVEL_ZERO_ITEM_ID as y } from \"./itemsIdsUtils.mjs\";\nimport { MenuItem as x } from \"../components/MenuItem.mjs\";\nfunction L(e, n) {\n  if (e && e.length > 0)\n    return {\n      items: c(e),\n      inputItems: e\n    };\n  if (s(n).length > 0) {\n    const t = R(s(n));\n    return { items: c(t), inputItems: t };\n  }\n  return { items: [], inputItems: [] };\n}\nfunction h(e, n) {\n  const t = {}, {\n    text: i,\n    url: o,\n    icon: r,\n    svgIcon: d,\n    disabled: u,\n    cssClass: f,\n    cssStyle: a,\n    render: l,\n    linkRender: p,\n    contentRender: m,\n    data: I,\n    separator: v\n  } = e || n.props;\n  return i !== void 0 && (t.text = i), o !== void 0 && (t.url = o), r !== void 0 && (t.icon = r), d !== void 0 && (t.svgIcon = d), u !== void 0 && (t.disabled = u), f !== void 0 && (t.cssClass = f), a !== void 0 && (t.cssStyle = a), l !== void 0 && (t.render = l), p !== void 0 && (t.linkRender = p), m !== void 0 && (t.contentRender = m), I !== void 0 && (t.data = I), v !== void 0 && (t.separator = v), t;\n}\nfunction s(e) {\n  return C.Children.toArray(e).filter((n) => n && n.type === x);\n}\nfunction R(e) {\n  const n = [];\n  for (let t = 0; t < e.length; t++) {\n    const i = e[t], o = h(void 0, i), r = R(s(i.props.children));\n    r.length > 0 && (o.items = r), n.push(o);\n  }\n  return n;\n}\nfunction c(e, n) {\n  const t = [];\n  for (let i = 0; i < e.length; i++) {\n    const o = e[i], r = h(o);\n    r.id = g(i.toString(), n), r.isLastFromSiblings = i === e.length - 1, r.separator = o.separator === !0, r.items = E(o, r), t.push(r);\n  }\n  return t;\n}\nfunction E(e, n) {\n  return e.contentRender ? [\n    {\n      contentParentItemId: n.id,\n      id: g(y, n.id),\n      isLastFromSiblings: !0,\n      separator: !1,\n      contentRender: e.contentRender,\n      items: []\n    }\n  ] : e.items ? c(e.items, n.id) : [];\n}\nexport {\n  L as prepareInputItemsForInternalWork\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,QAAQ,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,qBAAqB;AACjF,SAASC,QAAQ,IAAIC,CAAC,QAAQ,4BAA4B;AAC1D,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACf,IAAID,CAAC,IAAIA,CAAC,CAACE,MAAM,GAAG,CAAC,EACnB,OAAO;IACLC,KAAK,EAAEC,CAAC,CAACJ,CAAC,CAAC;IACXK,UAAU,EAAEL;EACd,CAAC;EACH,IAAIM,CAAC,CAACL,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;IACnB,MAAMK,CAAC,GAAGC,CAAC,CAACF,CAAC,CAACL,CAAC,CAAC,CAAC;IACjB,OAAO;MAA<PERSON>,KAAK,EAAEC,CAAC,CAACG,CAAC,CAAC;MAAEF,UAAU,EAAEE;IAAE,CAAC;EACvC;EACA,OAAO;IAAEJ,KAAK,EAAE,EAAE;IAAEE,UAAU,EAAE;EAAG,CAAC;AACtC;AACA,SAASI,CAACA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACf,MAAMM,CAAC,GAAG,CAAC,CAAC;IAAE;MACZG,IAAI,EAAEC,CAAC;MACPC,GAAG,EAAEC,CAAC;MACNC,IAAI,EAAEC,CAAC;MACPC,OAAO,EAAEC,CAAC;MACVC,QAAQ,EAAEC,CAAC;MACXC,QAAQ,EAAEC,CAAC;MACXC,QAAQ,EAAEC,CAAC;MACXC,MAAM,EAAEC,CAAC;MACTC,UAAU,EAAEC,CAAC;MACbC,aAAa,EAAEC,CAAC;MAChBC,IAAI,EAAEC,CAAC;MACPC,SAAS,EAAEC;IACb,CAAC,GAAGjC,CAAC,IAAIC,CAAC,CAACiC,KAAK;EAChB,OAAOvB,CAAC,KAAK,KAAK,CAAC,KAAKJ,CAAC,CAACG,IAAI,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKN,CAAC,CAACK,GAAG,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKR,CAAC,CAACO,IAAI,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKV,CAAC,CAACS,OAAO,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKZ,CAAC,CAACW,QAAQ,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKd,CAAC,CAACa,QAAQ,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKhB,CAAC,CAACe,QAAQ,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKlB,CAAC,CAACiB,MAAM,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKpB,CAAC,CAACmB,UAAU,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKtB,CAAC,CAACqB,aAAa,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKxB,CAAC,CAACuB,IAAI,GAAGC,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAK1B,CAAC,CAACyB,SAAS,GAAGC,CAAC,CAAC,EAAE1B,CAAC;AACtZ;AACA,SAASD,CAACA,CAACN,CAAC,EAAE;EACZ,OAAOR,CAAC,CAAC2C,QAAQ,CAACC,OAAO,CAACpC,CAAC,CAAC,CAACqC,MAAM,CAAEpC,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACqC,IAAI,KAAKxC,CAAC,CAAC;AAC/D;AACA,SAASU,CAACA,CAACR,CAAC,EAAE;EACZ,MAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,CAACE,MAAM,EAAEK,CAAC,EAAE,EAAE;IACjC,MAAMI,CAAC,GAAGX,CAAC,CAACO,CAAC,CAAC;MAAEM,CAAC,GAAGJ,CAAC,CAAC,KAAK,CAAC,EAAEE,CAAC,CAAC;MAAEI,CAAC,GAAGP,CAAC,CAACF,CAAC,CAACK,CAAC,CAACuB,KAAK,CAACK,QAAQ,CAAC,CAAC;IAC5DxB,CAAC,CAACb,MAAM,GAAG,CAAC,KAAKW,CAAC,CAACV,KAAK,GAAGY,CAAC,CAAC,EAAEd,CAAC,CAACuC,IAAI,CAAC3B,CAAC,CAAC;EAC1C;EACA,OAAOZ,CAAC;AACV;AACA,SAASG,CAACA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACf,MAAMM,CAAC,GAAG,EAAE;EACZ,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,CAAC,CAACE,MAAM,EAAES,CAAC,EAAE,EAAE;IACjC,MAAME,CAAC,GAAGb,CAAC,CAACW,CAAC,CAAC;MAAEI,CAAC,GAAGN,CAAC,CAACI,CAAC,CAAC;IACxBE,CAAC,CAAC0B,EAAE,GAAG/C,CAAC,CAACiB,CAAC,CAAC+B,QAAQ,CAAC,CAAC,EAAEzC,CAAC,CAAC,EAAEc,CAAC,CAAC4B,kBAAkB,GAAGhC,CAAC,KAAKX,CAAC,CAACE,MAAM,GAAG,CAAC,EAAEa,CAAC,CAACiB,SAAS,GAAGnB,CAAC,CAACmB,SAAS,KAAK,CAAC,CAAC,EAAEjB,CAAC,CAACZ,KAAK,GAAGyC,CAAC,CAAC/B,CAAC,EAAEE,CAAC,CAAC,EAAER,CAAC,CAACiC,IAAI,CAACzB,CAAC,CAAC;EACtI;EACA,OAAOR,CAAC;AACV;AACA,SAASqC,CAACA,CAAC5C,CAAC,EAAEC,CAAC,EAAE;EACf,OAAOD,CAAC,CAAC4B,aAAa,GAAG,CACvB;IACEiB,mBAAmB,EAAE5C,CAAC,CAACwC,EAAE;IACzBA,EAAE,EAAE/C,CAAC,CAACE,CAAC,EAAEK,CAAC,CAACwC,EAAE,CAAC;IACdE,kBAAkB,EAAE,CAAC,CAAC;IACtBX,SAAS,EAAE,CAAC,CAAC;IACbJ,aAAa,EAAE5B,CAAC,CAAC4B,aAAa;IAC9BzB,KAAK,EAAE;EACT,CAAC,CACF,GAAGH,CAAC,CAACG,KAAK,GAAGC,CAAC,CAACJ,CAAC,CAACG,KAAK,EAAEF,CAAC,CAACwC,EAAE,CAAC,GAAG,EAAE;AACrC;AACA,SACE1C,CAAC,IAAI+C,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}