{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { Card as N } from \"../card/Card.mjs\";\nimport { CardHeader as b } from \"../card/CardHeader.mjs\";\nimport { CardTitle as A } from \"../card/CardTitle.mjs\";\nimport { CardSubtitle as D } from \"../card/CardSubtitle.mjs\";\nimport { CardBody as h } from \"../card/CardBody.mjs\";\nimport { CardImage as I } from \"../card/CardImage.mjs\";\nimport { CardActions as g } from \"../card/CardActions.mjs\";\nimport { classNames as u } from \"@progress/kendo-react-common\";\nimport { Reveal as y } from \"@progress/kendo-react-animation\";\nimport { chevronRightIcon as T } from \"@progress/kendo-svg-icons\";\nimport { Button as O } from \"@progress/kendo-react-buttons\";\nconst x = e => {\n  const {\n      title: E,\n      subtitle: l,\n      actions: i,\n      images: o,\n      description: v\n    } = e.eventData,\n    [c, k] = t.useState(e.eventData.opened || !1),\n    [r, m] = t.useState(e.eventData.opened || !!e.collapsible),\n    f = a => {\n      if (k(!c), e.onChange) {\n        const n = {\n          syntheticEvent: a,\n          nativeEvent: a.nativeEvent,\n          eventData: e.eventData\n        };\n        e.onChange.call(void 0, n);\n      }\n    },\n    C = a => {\n      if (e.onActionClick) {\n        const n = {\n          syntheticEvent: a,\n          nativeEvent: a.nativeEvent,\n          eventData: e.eventData\n        };\n        e.onActionClick.call(void 0, n);\n      }\n    },\n    d = () => /* @__PURE__ */t.createElement(t.Fragment, null, /* @__PURE__ */t.createElement(h, null, /* @__PURE__ */t.createElement(\"div\", {\n      className: \"k-card-description\"\n    }, /* @__PURE__ */t.createElement(\"p\", null, v), o && o.map((a, n) => /* @__PURE__ */t.createElement(I, {\n      key: n,\n      src: a.src\n    })))), i && /* @__PURE__ */t.createElement(g, null, i.map((a, n) => /* @__PURE__ */t.createElement(\"a\", {\n      key: n,\n      href: a.url,\n      className: \"k-button k-button-md k-rounded-md k-button-flat k-button-flat-primary\",\n      onClick: C\n    }, a.text))));\n  return /* @__PURE__ */t.createElement(\"div\", {\n    \"data-testid\": \"k-timeline-card\",\n    className: u(\"k-timeline-card\", {\n      \"k-collapsed\": e.collapsible && r\n    })\n  }, /* @__PURE__ */t.createElement(N, {\n    \"aria-live\": \"polite\",\n    \"aria-describedby\": e.id,\n    \"aria-atomic\": \"true\",\n    tabIndex: e.tabindex,\n    role: e.horizontal ? \"tabpanel\" : \"button\",\n    \"aria-expanded\": e.collapsible && r,\n    className: \"k-card-with-callout\",\n    onClick: a => f(a)\n  }, /* @__PURE__ */t.createElement(\"span\", {\n    style: e.calloutStyle,\n    className: u(\"k-timeline-card-callout\", \"k-card-callout\", {\n      \"k-callout-n\": e.horizontal\n    }, {\n      \"k-callout-e\": e.alternated && !e.horizontal\n    }, {\n      \"k-callout-w\": !e.alternated && !e.horizontal\n    })\n  }), /* @__PURE__ */t.createElement(\"div\", {\n    className: \"k-card-inner\"\n  }, /* @__PURE__ */t.createElement(b, null, /* @__PURE__ */t.createElement(A, null, /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-event-title\"\n  }, E), e.collapsible && /* @__PURE__ */t.createElement(O, {\n    className: \"k-event-collapse\",\n    fillMode: \"flat\",\n    svgIcon: T\n  })), l && /* @__PURE__ */t.createElement(D, null, l)), e.collapsible ? /* @__PURE__ */t.createElement(y, {\n    transitionEnterDuration: e.transitionDuration || 400,\n    transitionExitDuration: e.transitionDuration || 400,\n    onBeforeEnter: () => m(!1),\n    onAfterExited: () => m(!0)\n  }, c ? d() : null) : d())));\n};\nx.propTypes = {\n  onChange: s.func,\n  onActionClick: s.func\n};\nexport { x as TimelineCard };", "map": {"version": 3, "names": ["t", "s", "Card", "N", "<PERSON><PERSON><PERSON><PERSON>", "b", "CardTitle", "A", "CardSubtitle", "D", "CardBody", "h", "CardImage", "I", "CardActions", "g", "classNames", "u", "Reveal", "y", "chevronRightIcon", "T", "<PERSON><PERSON>", "O", "x", "e", "title", "E", "subtitle", "l", "actions", "i", "images", "o", "description", "v", "eventData", "c", "k", "useState", "opened", "r", "m", "collapsible", "f", "a", "onChange", "n", "syntheticEvent", "nativeEvent", "call", "C", "onActionClick", "d", "createElement", "Fragment", "className", "map", "key", "src", "href", "url", "onClick", "text", "id", "tabIndex", "tabindex", "role", "horizontal", "style", "calloutStyle", "alternated", "fillMode", "svgIcon", "transitionEnterDuration", "transitionDuration", "transitionExitDuration", "onBeforeEnter", "onAfterExited", "propTypes", "func", "TimelineCard"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/timeline/TimelineCard.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { Card as N } from \"../card/Card.mjs\";\nimport { CardHeader as b } from \"../card/CardHeader.mjs\";\nimport { CardTitle as A } from \"../card/CardTitle.mjs\";\nimport { CardSubtitle as D } from \"../card/CardSubtitle.mjs\";\nimport { CardBody as h } from \"../card/CardBody.mjs\";\nimport { CardImage as I } from \"../card/CardImage.mjs\";\nimport { CardActions as g } from \"../card/CardActions.mjs\";\nimport { classNames as u } from \"@progress/kendo-react-common\";\nimport { Reveal as y } from \"@progress/kendo-react-animation\";\nimport { chevronRightIcon as T } from \"@progress/kendo-svg-icons\";\nimport { Button as O } from \"@progress/kendo-react-buttons\";\nconst x = (e) => {\n  const { title: E, subtitle: l, actions: i, images: o, description: v } = e.eventData, [c, k] = t.useState(e.eventData.opened || !1), [r, m] = t.useState(e.eventData.opened || !!e.collapsible), f = (a) => {\n    if (k(!c), e.onChange) {\n      const n = {\n        syntheticEvent: a,\n        nativeEvent: a.nativeEvent,\n        eventData: e.eventData\n      };\n      e.onChange.call(void 0, n);\n    }\n  }, C = (a) => {\n    if (e.onActionClick) {\n      const n = {\n        syntheticEvent: a,\n        nativeEvent: a.nativeEvent,\n        eventData: e.eventData\n      };\n      e.onActionClick.call(void 0, n);\n    }\n  }, d = () => /* @__PURE__ */ t.createElement(t.Fragment, null, /* @__PURE__ */ t.createElement(h, null, /* @__PURE__ */ t.createElement(\"div\", { className: \"k-card-description\" }, /* @__PURE__ */ t.createElement(\"p\", null, v), o && o.map((a, n) => /* @__PURE__ */ t.createElement(I, { key: n, src: a.src })))), i && /* @__PURE__ */ t.createElement(g, null, i.map((a, n) => /* @__PURE__ */ t.createElement(\n    \"a\",\n    {\n      key: n,\n      href: a.url,\n      className: \"k-button k-button-md k-rounded-md k-button-flat k-button-flat-primary\",\n      onClick: C\n    },\n    a.text\n  ))));\n  return /* @__PURE__ */ t.createElement(\n    \"div\",\n    {\n      \"data-testid\": \"k-timeline-card\",\n      className: u(\"k-timeline-card\", { \"k-collapsed\": e.collapsible && r })\n    },\n    /* @__PURE__ */ t.createElement(\n      N,\n      {\n        \"aria-live\": \"polite\",\n        \"aria-describedby\": e.id,\n        \"aria-atomic\": \"true\",\n        tabIndex: e.tabindex,\n        role: e.horizontal ? \"tabpanel\" : \"button\",\n        \"aria-expanded\": e.collapsible && r,\n        className: \"k-card-with-callout\",\n        onClick: (a) => f(a)\n      },\n      /* @__PURE__ */ t.createElement(\n        \"span\",\n        {\n          style: e.calloutStyle,\n          className: u(\n            \"k-timeline-card-callout\",\n            \"k-card-callout\",\n            { \"k-callout-n\": e.horizontal },\n            { \"k-callout-e\": e.alternated && !e.horizontal },\n            { \"k-callout-w\": !e.alternated && !e.horizontal }\n          )\n        }\n      ),\n      /* @__PURE__ */ t.createElement(\"div\", { className: \"k-card-inner\" }, /* @__PURE__ */ t.createElement(b, null, /* @__PURE__ */ t.createElement(A, null, /* @__PURE__ */ t.createElement(\"span\", { className: \"k-event-title\" }, E), e.collapsible && /* @__PURE__ */ t.createElement(O, { className: \"k-event-collapse\", fillMode: \"flat\", svgIcon: T })), l && /* @__PURE__ */ t.createElement(D, null, l)), e.collapsible ? /* @__PURE__ */ t.createElement(\n        y,\n        {\n          transitionEnterDuration: e.transitionDuration || 400,\n          transitionExitDuration: e.transitionDuration || 400,\n          onBeforeEnter: () => m(!1),\n          onAfterExited: () => m(!0)\n        },\n        c ? d() : null\n      ) : d())\n    )\n  );\n};\nx.propTypes = {\n  onChange: s.func,\n  onActionClick: s.func\n};\nexport {\n  x as TimelineCard\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,IAAI,IAAIC,CAAC,QAAQ,kBAAkB;AAC5C,SAASC,UAAU,IAAIC,CAAC,QAAQ,wBAAwB;AACxD,SAASC,SAAS,IAAIC,CAAC,QAAQ,uBAAuB;AACtD,SAASC,YAAY,IAAIC,CAAC,QAAQ,0BAA0B;AAC5D,SAASC,QAAQ,IAAIC,CAAC,QAAQ,sBAAsB;AACpD,SAASC,SAAS,IAAIC,CAAC,QAAQ,uBAAuB;AACtD,SAASC,WAAW,IAAIC,CAAC,QAAQ,yBAAyB;AAC1D,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,SAASC,MAAM,IAAIC,CAAC,QAAQ,iCAAiC;AAC7D,SAASC,gBAAgB,IAAIC,CAAC,QAAQ,2BAA2B;AACjE,SAASC,MAAM,IAAIC,CAAC,QAAQ,+BAA+B;AAC3D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAM;MAAEC,KAAK,EAAEC,CAAC;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,OAAO,EAAEC,CAAC;MAAEC,MAAM,EAAEC,CAAC;MAAEC,WAAW,EAAEC;IAAE,CAAC,GAAGV,CAAC,CAACW,SAAS;IAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGtC,CAAC,CAACuC,QAAQ,CAACd,CAAC,CAACW,SAAS,CAACI,MAAM,IAAI,CAAC,CAAC,CAAC;IAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG1C,CAAC,CAACuC,QAAQ,CAACd,CAAC,CAACW,SAAS,CAACI,MAAM,IAAI,CAAC,CAACf,CAAC,CAACkB,WAAW,CAAC;IAAEC,CAAC,GAAIC,CAAC,IAAK;MAC1M,IAAIP,CAAC,CAAC,CAACD,CAAC,CAAC,EAAEZ,CAAC,CAACqB,QAAQ,EAAE;QACrB,MAAMC,CAAC,GAAG;UACRC,cAAc,EAAEH,CAAC;UACjBI,WAAW,EAAEJ,CAAC,CAACI,WAAW;UAC1Bb,SAAS,EAAEX,CAAC,CAACW;QACf,CAAC;QACDX,CAAC,CAACqB,QAAQ,CAACI,IAAI,CAAC,KAAK,CAAC,EAAEH,CAAC,CAAC;MAC5B;IACF,CAAC;IAAEI,CAAC,GAAIN,CAAC,IAAK;MACZ,IAAIpB,CAAC,CAAC2B,aAAa,EAAE;QACnB,MAAML,CAAC,GAAG;UACRC,cAAc,EAAEH,CAAC;UACjBI,WAAW,EAAEJ,CAAC,CAACI,WAAW;UAC1Bb,SAAS,EAAEX,CAAC,CAACW;QACf,CAAC;QACDX,CAAC,CAAC2B,aAAa,CAACF,IAAI,CAAC,KAAK,CAAC,EAAEH,CAAC,CAAC;MACjC;IACF,CAAC;IAAEM,CAAC,GAAGA,CAAA,KAAM,eAAgBrD,CAAC,CAACsD,aAAa,CAACtD,CAAC,CAACuD,QAAQ,EAAE,IAAI,EAAE,eAAgBvD,CAAC,CAACsD,aAAa,CAAC3C,CAAC,EAAE,IAAI,EAAE,eAAgBX,CAAC,CAACsD,aAAa,CAAC,KAAK,EAAE;MAAEE,SAAS,EAAE;IAAqB,CAAC,EAAE,eAAgBxD,CAAC,CAACsD,aAAa,CAAC,GAAG,EAAE,IAAI,EAAEnB,CAAC,CAAC,EAAEF,CAAC,IAAIA,CAAC,CAACwB,GAAG,CAAC,CAACZ,CAAC,EAAEE,CAAC,KAAK,eAAgB/C,CAAC,CAACsD,aAAa,CAACzC,CAAC,EAAE;MAAE6C,GAAG,EAAEX,CAAC;MAAEY,GAAG,EAAEd,CAAC,CAACc;IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE5B,CAAC,IAAI,eAAgB/B,CAAC,CAACsD,aAAa,CAACvC,CAAC,EAAE,IAAI,EAAEgB,CAAC,CAAC0B,GAAG,CAAC,CAACZ,CAAC,EAAEE,CAAC,KAAK,eAAgB/C,CAAC,CAACsD,aAAa,CAClZ,GAAG,EACH;MACEI,GAAG,EAAEX,CAAC;MACNa,IAAI,EAAEf,CAAC,CAACgB,GAAG;MACXL,SAAS,EAAE,uEAAuE;MAClFM,OAAO,EAAEX;IACX,CAAC,EACDN,CAAC,CAACkB,IACJ,CAAC,CAAC,CAAC,CAAC;EACJ,OAAO,eAAgB/D,CAAC,CAACsD,aAAa,CACpC,KAAK,EACL;IACE,aAAa,EAAE,iBAAiB;IAChCE,SAAS,EAAEvC,CAAC,CAAC,iBAAiB,EAAE;MAAE,aAAa,EAAEQ,CAAC,CAACkB,WAAW,IAAIF;IAAE,CAAC;EACvE,CAAC,EACD,eAAgBzC,CAAC,CAACsD,aAAa,CAC7BnD,CAAC,EACD;IACE,WAAW,EAAE,QAAQ;IACrB,kBAAkB,EAAEsB,CAAC,CAACuC,EAAE;IACxB,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAExC,CAAC,CAACyC,QAAQ;IACpBC,IAAI,EAAE1C,CAAC,CAAC2C,UAAU,GAAG,UAAU,GAAG,QAAQ;IAC1C,eAAe,EAAE3C,CAAC,CAACkB,WAAW,IAAIF,CAAC;IACnCe,SAAS,EAAE,qBAAqB;IAChCM,OAAO,EAAGjB,CAAC,IAAKD,CAAC,CAACC,CAAC;EACrB,CAAC,EACD,eAAgB7C,CAAC,CAACsD,aAAa,CAC7B,MAAM,EACN;IACEe,KAAK,EAAE5C,CAAC,CAAC6C,YAAY;IACrBd,SAAS,EAAEvC,CAAC,CACV,yBAAyB,EACzB,gBAAgB,EAChB;MAAE,aAAa,EAAEQ,CAAC,CAAC2C;IAAW,CAAC,EAC/B;MAAE,aAAa,EAAE3C,CAAC,CAAC8C,UAAU,IAAI,CAAC9C,CAAC,CAAC2C;IAAW,CAAC,EAChD;MAAE,aAAa,EAAE,CAAC3C,CAAC,CAAC8C,UAAU,IAAI,CAAC9C,CAAC,CAAC2C;IAAW,CAClD;EACF,CACF,CAAC,EACD,eAAgBpE,CAAC,CAACsD,aAAa,CAAC,KAAK,EAAE;IAAEE,SAAS,EAAE;EAAe,CAAC,EAAE,eAAgBxD,CAAC,CAACsD,aAAa,CAACjD,CAAC,EAAE,IAAI,EAAE,eAAgBL,CAAC,CAACsD,aAAa,CAAC/C,CAAC,EAAE,IAAI,EAAE,eAAgBP,CAAC,CAACsD,aAAa,CAAC,MAAM,EAAE;IAAEE,SAAS,EAAE;EAAgB,CAAC,EAAE7B,CAAC,CAAC,EAAEF,CAAC,CAACkB,WAAW,IAAI,eAAgB3C,CAAC,CAACsD,aAAa,CAAC/B,CAAC,EAAE;IAAEiC,SAAS,EAAE,kBAAkB;IAAEgB,QAAQ,EAAE,MAAM;IAAEC,OAAO,EAAEpD;EAAE,CAAC,CAAC,CAAC,EAAEQ,CAAC,IAAI,eAAgB7B,CAAC,CAACsD,aAAa,CAAC7C,CAAC,EAAE,IAAI,EAAEoB,CAAC,CAAC,CAAC,EAAEJ,CAAC,CAACkB,WAAW,GAAG,eAAgB3C,CAAC,CAACsD,aAAa,CAC3bnC,CAAC,EACD;IACEuD,uBAAuB,EAAEjD,CAAC,CAACkD,kBAAkB,IAAI,GAAG;IACpDC,sBAAsB,EAAEnD,CAAC,CAACkD,kBAAkB,IAAI,GAAG;IACnDE,aAAa,EAAEA,CAAA,KAAMnC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1BoC,aAAa,EAAEA,CAAA,KAAMpC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,EACDL,CAAC,GAAGgB,CAAC,CAAC,CAAC,GAAG,IACZ,CAAC,GAAGA,CAAC,CAAC,CAAC,CACT,CACF,CAAC;AACH,CAAC;AACD7B,CAAC,CAACuD,SAAS,GAAG;EACZjC,QAAQ,EAAE7C,CAAC,CAAC+E,IAAI;EAChB5B,aAAa,EAAEnD,CAAC,CAAC+E;AACnB,CAAC;AACD,SACExD,CAAC,IAAIyD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}