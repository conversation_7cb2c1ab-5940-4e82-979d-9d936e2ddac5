{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport I from \"prop-types\";\nimport { TimelineCard as w } from \"./TimelineCard.mjs\";\nimport { addYearsFlags as F } from \"./utils.mjs\";\nimport { Navigation as T, useId as x, classNames as A } from \"@progress/kendo-react-common\";\nimport { useInternationalization as R } from \"@progress/kendo-react-intl\";\nconst S = l => {\n  const u = R(),\n    [c, p] = e.useState(),\n    {\n      collapsibleEvents: v,\n      transitionDuration: E,\n      eventsData: g,\n      dateFormat: k,\n      alterMode: o,\n      navigatable: m,\n      onChange: y,\n      onActionClick: b\n    } = l,\n    s = e.useRef(null),\n    a = e.useRef(new T({\n      tabIndex: 0,\n      root: s,\n      selectors: [\".k-card-vertical\"]\n    }));\n  e.useEffect(() => {\n    s.current && l.navigatable && (setTimeout(() => {\n      const t = a.current.first;\n      t && t.setAttribute(\"tabindex\", String(0));\n    }, 0), a.current.keyboardEvents = {\n      keydown: {\n        Space: d,\n        Enter: d\n      }\n    });\n  }, [l.navigatable]), e.useEffect(() => {\n    p(F(g));\n  }, []), e.useEffect(() => {\n    a.current.update();\n  });\n  const N = t => {\n      m && a.current && a.current.triggerKeyboardEvent(t);\n    },\n    d = (t, r, i) => {\n      i.preventDefault();\n      const n = t.querySelector(\".k-card-title .k-event-collapse\");\n      n && n.click();\n    };\n  let f = 0;\n  const C = x();\n  return /* @__PURE__ */e.createElement(\"ul\", {\n    ref: s,\n    onKeyDown: N\n  }, c && c.map((t, r) => {\n    const i = (r + f) % 2 === 0;\n    t.yearFlag && (f += 1);\n    const n = C + \"-\" + r,\n      D = o ? {\n        \"k-reverse\": i\n      } : void 0;\n    return /* @__PURE__ */e.createElement(e.Fragment, {\n      key: r\n    }, t.yearFlag ? /* @__PURE__ */e.createElement(\"li\", {\n      className: \"k-timeline-flag-wrap\"\n    }, /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-timeline-flag\"\n    }, t.yearFlag)) : /* @__PURE__ */e.createElement(\"li\", {\n      className: A(\"k-timeline-event\", D)\n    }, /* @__PURE__ */e.createElement(\"div\", {\n      className: \"k-timeline-date-wrap\"\n    }, /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-timeline-date\",\n      id: n\n    }, u.formatDate(t.date, k))), /* @__PURE__ */e.createElement(\"span\", {\n      className: \"k-timeline-circle\"\n    }), /* @__PURE__ */e.createElement(w, {\n      id: n,\n      tabindex: m ? 0 : -1,\n      eventData: t,\n      alternated: o && i,\n      collapsible: v,\n      transitionDuration: E,\n      onChange: y,\n      onActionClick: b\n    })));\n  }));\n};\nS.propTypes = {\n  onActionClick: I.func\n};\nexport { S as TimelineVertical };", "map": {"version": 3, "names": ["e", "I", "TimelineCard", "w", "addYearsFlags", "F", "Navigation", "T", "useId", "x", "classNames", "A", "useInternationalization", "R", "S", "l", "u", "c", "p", "useState", "collapsibleEvents", "v", "transitionDuration", "E", "eventsData", "g", "dateFormat", "k", "alterMode", "o", "navigatable", "m", "onChange", "y", "onActionClick", "b", "s", "useRef", "a", "tabIndex", "root", "selectors", "useEffect", "current", "setTimeout", "t", "first", "setAttribute", "String", "keyboardEvents", "keydown", "Space", "d", "Enter", "update", "N", "triggerKeyboardEvent", "r", "i", "preventDefault", "n", "querySelector", "click", "f", "C", "createElement", "ref", "onKeyDown", "map", "yearFlag", "D", "Fragment", "key", "className", "id", "formatDate", "date", "tabindex", "eventData", "alternated", "collapsible", "propTypes", "func", "TimelineVertical"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/timeline/TimelineVertical.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport I from \"prop-types\";\nimport { TimelineCard as w } from \"./TimelineCard.mjs\";\nimport { addYearsFlags as F } from \"./utils.mjs\";\nimport { Navigation as T, useId as x, classNames as A } from \"@progress/kendo-react-common\";\nimport { useInternationalization as R } from \"@progress/kendo-react-intl\";\nconst S = (l) => {\n  const u = R(), [c, p] = e.useState(), {\n    collapsibleEvents: v,\n    transitionDuration: E,\n    eventsData: g,\n    dateFormat: k,\n    alterMode: o,\n    navigatable: m,\n    onChange: y,\n    onActionClick: b\n  } = l, s = e.useRef(null), a = e.useRef(\n    new T({\n      tabIndex: 0,\n      root: s,\n      selectors: [\".k-card-vertical\"]\n    })\n  );\n  e.useEffect(() => {\n    s.current && l.navigatable && (setTimeout(() => {\n      const t = a.current.first;\n      t && t.setAttribute(\"tabindex\", String(0));\n    }, 0), a.current.keyboardEvents = {\n      keydown: {\n        Space: d,\n        Enter: d\n      }\n    });\n  }, [l.navigatable]), e.useEffect(() => {\n    p(F(g));\n  }, []), e.useEffect(() => {\n    a.current.update();\n  });\n  const N = (t) => {\n    m && a.current && a.current.triggerKeyboardEvent(t);\n  }, d = (t, r, i) => {\n    i.preventDefault();\n    const n = t.querySelector(\".k-card-title .k-event-collapse\");\n    n && n.click();\n  };\n  let f = 0;\n  const C = x();\n  return /* @__PURE__ */ e.createElement(\"ul\", { ref: s, onKeyDown: N }, c && c.map((t, r) => {\n    const i = (r + f) % 2 === 0;\n    t.yearFlag && (f += 1);\n    const n = C + \"-\" + r, D = o ? { \"k-reverse\": i } : void 0;\n    return /* @__PURE__ */ e.createElement(e.Fragment, { key: r }, t.yearFlag ? /* @__PURE__ */ e.createElement(\"li\", { className: \"k-timeline-flag-wrap\" }, /* @__PURE__ */ e.createElement(\"span\", { className: \"k-timeline-flag\" }, t.yearFlag)) : /* @__PURE__ */ e.createElement(\"li\", { className: A(\"k-timeline-event\", D) }, /* @__PURE__ */ e.createElement(\"div\", { className: \"k-timeline-date-wrap\" }, /* @__PURE__ */ e.createElement(\"span\", { className: \"k-timeline-date\", id: n }, u.formatDate(t.date, k))), /* @__PURE__ */ e.createElement(\"span\", { className: \"k-timeline-circle\" }), /* @__PURE__ */ e.createElement(\n      w,\n      {\n        id: n,\n        tabindex: m ? 0 : -1,\n        eventData: t,\n        alternated: o && i,\n        collapsible: v,\n        transitionDuration: E,\n        onChange: y,\n        onActionClick: b\n      }\n    )));\n  }));\n};\nS.propTypes = {\n  onActionClick: I.func\n};\nexport {\n  S as TimelineVertical\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,YAAY,IAAIC,CAAC,QAAQ,oBAAoB;AACtD,SAASC,aAAa,IAAIC,CAAC,QAAQ,aAAa;AAChD,SAASC,UAAU,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC3F,SAASC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AACzE,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAGH,CAAC,CAAC,CAAC;IAAE,CAACI,CAAC,EAAEC,CAAC,CAAC,GAAGlB,CAAC,CAACmB,QAAQ,CAAC,CAAC;IAAE;MACpCC,iBAAiB,EAAEC,CAAC;MACpBC,kBAAkB,EAAEC,CAAC;MACrBC,UAAU,EAAEC,CAAC;MACbC,UAAU,EAAEC,CAAC;MACbC,SAAS,EAAEC,CAAC;MACZC,WAAW,EAAEC,CAAC;MACdC,QAAQ,EAAEC,CAAC;MACXC,aAAa,EAAEC;IACjB,CAAC,GAAGpB,CAAC;IAAEqB,CAAC,GAAGpC,CAAC,CAACqC,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGtC,CAAC,CAACqC,MAAM,CACrC,IAAI9B,CAAC,CAAC;MACJgC,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAEJ,CAAC;MACPK,SAAS,EAAE,CAAC,kBAAkB;IAChC,CAAC,CACH,CAAC;EACDzC,CAAC,CAAC0C,SAAS,CAAC,MAAM;IAChBN,CAAC,CAACO,OAAO,IAAI5B,CAAC,CAACe,WAAW,KAAKc,UAAU,CAAC,MAAM;MAC9C,MAAMC,CAAC,GAAGP,CAAC,CAACK,OAAO,CAACG,KAAK;MACzBD,CAAC,IAAIA,CAAC,CAACE,YAAY,CAAC,UAAU,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC,EAAEV,CAAC,CAACK,OAAO,CAACM,cAAc,GAAG;MAChCC,OAAO,EAAE;QACPC,KAAK,EAAEC,CAAC;QACRC,KAAK,EAAED;MACT;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrC,CAAC,CAACe,WAAW,CAAC,CAAC,EAAE9B,CAAC,CAAC0C,SAAS,CAAC,MAAM;IACrCxB,CAAC,CAACb,CAAC,CAACoB,CAAC,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC,EAAEzB,CAAC,CAAC0C,SAAS,CAAC,MAAM;IACxBJ,CAAC,CAACK,OAAO,CAACW,MAAM,CAAC,CAAC;EACpB,CAAC,CAAC;EACF,MAAMC,CAAC,GAAIV,CAAC,IAAK;MACfd,CAAC,IAAIO,CAAC,CAACK,OAAO,IAAIL,CAAC,CAACK,OAAO,CAACa,oBAAoB,CAACX,CAAC,CAAC;IACrD,CAAC;IAAEO,CAAC,GAAGA,CAACP,CAAC,EAAEY,CAAC,EAAEC,CAAC,KAAK;MAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,MAAMC,CAAC,GAAGf,CAAC,CAACgB,aAAa,CAAC,iCAAiC,CAAC;MAC5DD,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAC,CAAC;IAChB,CAAC;EACD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,CAAC,GAAGvD,CAAC,CAAC,CAAC;EACb,OAAO,eAAgBT,CAAC,CAACiE,aAAa,CAAC,IAAI,EAAE;IAAEC,GAAG,EAAE9B,CAAC;IAAE+B,SAAS,EAAEZ;EAAE,CAAC,EAAEtC,CAAC,IAAIA,CAAC,CAACmD,GAAG,CAAC,CAACvB,CAAC,EAAEY,CAAC,KAAK;IAC1F,MAAMC,CAAC,GAAG,CAACD,CAAC,GAAGM,CAAC,IAAI,CAAC,KAAK,CAAC;IAC3BlB,CAAC,CAACwB,QAAQ,KAAKN,CAAC,IAAI,CAAC,CAAC;IACtB,MAAMH,CAAC,GAAGI,CAAC,GAAG,GAAG,GAAGP,CAAC;MAAEa,CAAC,GAAGzC,CAAC,GAAG;QAAE,WAAW,EAAE6B;MAAE,CAAC,GAAG,KAAK,CAAC;IAC1D,OAAO,eAAgB1D,CAAC,CAACiE,aAAa,CAACjE,CAAC,CAACuE,QAAQ,EAAE;MAAEC,GAAG,EAAEf;IAAE,CAAC,EAAEZ,CAAC,CAACwB,QAAQ,GAAG,eAAgBrE,CAAC,CAACiE,aAAa,CAAC,IAAI,EAAE;MAAEQ,SAAS,EAAE;IAAuB,CAAC,EAAE,eAAgBzE,CAAC,CAACiE,aAAa,CAAC,MAAM,EAAE;MAAEQ,SAAS,EAAE;IAAkB,CAAC,EAAE5B,CAAC,CAACwB,QAAQ,CAAC,CAAC,GAAG,eAAgBrE,CAAC,CAACiE,aAAa,CAAC,IAAI,EAAE;MAAEQ,SAAS,EAAE9D,CAAC,CAAC,kBAAkB,EAAE2D,CAAC;IAAE,CAAC,EAAE,eAAgBtE,CAAC,CAACiE,aAAa,CAAC,KAAK,EAAE;MAAEQ,SAAS,EAAE;IAAuB,CAAC,EAAE,eAAgBzE,CAAC,CAACiE,aAAa,CAAC,MAAM,EAAE;MAAEQ,SAAS,EAAE,iBAAiB;MAAEC,EAAE,EAAEd;IAAE,CAAC,EAAE5C,CAAC,CAAC2D,UAAU,CAAC9B,CAAC,CAAC+B,IAAI,EAAEjD,CAAC,CAAC,CAAC,CAAC,EAAE,eAAgB3B,CAAC,CAACiE,aAAa,CAAC,MAAM,EAAE;MAAEQ,SAAS,EAAE;IAAoB,CAAC,CAAC,EAAE,eAAgBzE,CAAC,CAACiE,aAAa,CACrmB9D,CAAC,EACD;MACEuE,EAAE,EAAEd,CAAC;MACLiB,QAAQ,EAAE9C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACpB+C,SAAS,EAAEjC,CAAC;MACZkC,UAAU,EAAElD,CAAC,IAAI6B,CAAC;MAClBsB,WAAW,EAAE3D,CAAC;MACdC,kBAAkB,EAAEC,CAAC;MACrBS,QAAQ,EAAEC,CAAC;MACXC,aAAa,EAAEC;IACjB,CACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC;AACDrB,CAAC,CAACmE,SAAS,GAAG;EACZ/C,aAAa,EAAEjC,CAAC,CAACiF;AACnB,CAAC;AACD,SACEpE,CAAC,IAAIqE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}