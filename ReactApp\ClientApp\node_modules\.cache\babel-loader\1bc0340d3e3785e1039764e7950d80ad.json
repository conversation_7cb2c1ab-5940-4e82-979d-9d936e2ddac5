{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport e from \"prop-types\";\nimport { useId as T, classNames as j } from \"@progress/kendo-react-common\";\nconst c = s.forwardRef((t, g) => {\n    const r = s.useRef(null),\n      d = s.useCallback(() => ({\n        element: r.current\n      }), []);\n    s.useImperativeHandle(g, d);\n    const {\n        className: m,\n        style: u,\n        id: h,\n        children: y,\n        gap: i = o.gap\n      } = t,\n      f = T(),\n      a = s.useMemo(() => t.align && t.align.horizontal ? t.align.horizontal : o.hAlign, [t.align]),\n      l = s.useMemo(() => t.align && t.align.vertical ? t.align.vertical : o.vAlign, [t.align]),\n      p = s.useMemo(() => j(\"k-grid-layout\", {\n        \"k-justify-items-start\": a === \"start\",\n        \"k-justify-items-center\": a === \"center\",\n        \"k-justify-items-end\": a === \"end\",\n        \"k-justify-items-stretch\": a === \"stretch\",\n        \"k-align-items-start\": l === \"top\",\n        \"k-align-items-center\": l === \"middle\",\n        \"k-align-items-end\": l === \"bottom\",\n        \"k-align-items-stretch\": l === \"stretch\"\n      }, m), [a, l, m]),\n      v = i ? `${typeof i.rows == \"number\" ? i.rows + \"px\" : i.rows} ${typeof i.cols == \"number\" ? i.cols + \"px\" : i.cols}` : void 0,\n      b = t.rows && t.rows.map(n => `${typeof n.height == \"number\" ? n.height + \"px\" : n.height}`).join(\" \"),\n      k = t.cols && t.cols.map(n => `${typeof n.width == \"number\" ? n.width + \"px\" : n.width}`).join(\" \"),\n      w = {\n        gap: v,\n        gridTemplateColumns: k,\n        gridTemplateRows: b,\n        ...u\n      };\n    return /* @__PURE__ */s.createElement(\"div\", {\n      ref: r,\n      className: p,\n      style: w,\n      id: h || f\n    }, y);\n  }),\n  o = {\n    hAlign: \"stretch\",\n    vAlign: \"stretch\",\n    gap: void 0\n  };\nc.propTypes = {\n  className: e.string,\n  style: e.object,\n  children: e.any,\n  id: e.string,\n  gap: e.shape({\n    rows: e.oneOfType([e.string, e.number]),\n    columns: e.oneOfType([e.string, e.number])\n  }),\n  align: e.shape({\n    vertical: e.oneOf([\"top\", \"middle\", \"bottom\", \"stretch\"]),\n    horizontal: e.oneOf([\"start\", \"center\", \"end\", \"stretch\"])\n  })\n};\nc.displayName = \"KendoReactGridLayout\";\nexport { c as GridLayout };", "map": {"version": 3, "names": ["s", "e", "useId", "T", "classNames", "j", "c", "forwardRef", "t", "g", "r", "useRef", "d", "useCallback", "element", "current", "useImperativeHandle", "className", "m", "style", "u", "id", "h", "children", "y", "gap", "i", "o", "f", "a", "useMemo", "align", "horizontal", "hAlign", "l", "vertical", "vAlign", "p", "v", "rows", "cols", "b", "map", "n", "height", "join", "k", "width", "w", "gridTemplateColumns", "gridTemplateRows", "createElement", "ref", "propTypes", "string", "object", "any", "shape", "oneOfType", "number", "columns", "oneOf", "displayName", "GridLayout"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/gridlayout/GridLayout.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport e from \"prop-types\";\nimport { useId as T, classNames as j } from \"@progress/kendo-react-common\";\nconst c = s.forwardRef((t, g) => {\n  const r = s.useRef(null), d = s.useCallback(\n    () => ({\n      element: r.current\n    }),\n    []\n  );\n  s.useImperativeHandle(g, d);\n  const { className: m, style: u, id: h, children: y, gap: i = o.gap } = t, f = T(), a = s.useMemo(\n    () => t.align && t.align.horizontal ? t.align.horizontal : o.hAlign,\n    [t.align]\n  ), l = s.useMemo(\n    () => t.align && t.align.vertical ? t.align.vertical : o.vAlign,\n    [t.align]\n  ), p = s.useMemo(\n    () => j(\n      \"k-grid-layout\",\n      {\n        \"k-justify-items-start\": a === \"start\",\n        \"k-justify-items-center\": a === \"center\",\n        \"k-justify-items-end\": a === \"end\",\n        \"k-justify-items-stretch\": a === \"stretch\",\n        \"k-align-items-start\": l === \"top\",\n        \"k-align-items-center\": l === \"middle\",\n        \"k-align-items-end\": l === \"bottom\",\n        \"k-align-items-stretch\": l === \"stretch\"\n      },\n      m\n    ),\n    [a, l, m]\n  ), v = i ? `${typeof i.rows == \"number\" ? i.rows + \"px\" : i.rows} ${typeof i.cols == \"number\" ? i.cols + \"px\" : i.cols}` : void 0, b = t.rows && t.rows.map((n) => `${typeof n.height == \"number\" ? n.height + \"px\" : n.height}`).join(\" \"), k = t.cols && t.cols.map((n) => `${typeof n.width == \"number\" ? n.width + \"px\" : n.width}`).join(\" \"), w = {\n    gap: v,\n    gridTemplateColumns: k,\n    gridTemplateRows: b,\n    ...u\n  };\n  return /* @__PURE__ */ s.createElement(\"div\", { ref: r, className: p, style: w, id: h || f }, y);\n}), o = {\n  hAlign: \"stretch\",\n  vAlign: \"stretch\",\n  gap: void 0\n};\nc.propTypes = {\n  className: e.string,\n  style: e.object,\n  children: e.any,\n  id: e.string,\n  gap: e.shape({\n    rows: e.oneOfType([e.string, e.number]),\n    columns: e.oneOfType([e.string, e.number])\n  }),\n  align: e.shape({\n    vertical: e.oneOf([\"top\", \"middle\", \"bottom\", \"stretch\"]),\n    horizontal: e.oneOf([\"start\", \"center\", \"end\", \"stretch\"])\n  })\n};\nc.displayName = \"KendoReactGridLayout\";\nexport {\n  c as GridLayout\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,KAAK,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC1E,MAAMC,CAAC,GAAGN,CAAC,CAACO,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC/B,MAAMC,CAAC,GAAGV,CAAC,CAACW,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGZ,CAAC,CAACa,WAAW,CACzC,OAAO;QACLC,OAAO,EAAEJ,CAAC,CAACK;MACb,CAAC,CAAC,EACF,EACF,CAAC;IACDf,CAAC,CAACgB,mBAAmB,CAACP,CAAC,EAAEG,CAAC,CAAC;IAC3B,MAAM;QAAEK,SAAS,EAAEC,CAAC;QAAEC,KAAK,EAAEC,CAAC;QAAEC,EAAE,EAAEC,CAAC;QAAEC,QAAQ,EAAEC,CAAC;QAAEC,GAAG,EAAEC,CAAC,GAAGC,CAAC,CAACF;MAAI,CAAC,GAAGjB,CAAC;MAAEoB,CAAC,GAAGzB,CAAC,CAAC,CAAC;MAAE0B,CAAC,GAAG7B,CAAC,CAAC8B,OAAO,CAC9F,MAAMtB,CAAC,CAACuB,KAAK,IAAIvB,CAAC,CAACuB,KAAK,CAACC,UAAU,GAAGxB,CAAC,CAACuB,KAAK,CAACC,UAAU,GAAGL,CAAC,CAACM,MAAM,EACnE,CAACzB,CAAC,CAACuB,KAAK,CACV,CAAC;MAAEG,CAAC,GAAGlC,CAAC,CAAC8B,OAAO,CACd,MAAMtB,CAAC,CAACuB,KAAK,IAAIvB,CAAC,CAACuB,KAAK,CAACI,QAAQ,GAAG3B,CAAC,CAACuB,KAAK,CAACI,QAAQ,GAAGR,CAAC,CAACS,MAAM,EAC/D,CAAC5B,CAAC,CAACuB,KAAK,CACV,CAAC;MAAEM,CAAC,GAAGrC,CAAC,CAAC8B,OAAO,CACd,MAAMzB,CAAC,CACL,eAAe,EACf;QACE,uBAAuB,EAAEwB,CAAC,KAAK,OAAO;QACtC,wBAAwB,EAAEA,CAAC,KAAK,QAAQ;QACxC,qBAAqB,EAAEA,CAAC,KAAK,KAAK;QAClC,yBAAyB,EAAEA,CAAC,KAAK,SAAS;QAC1C,qBAAqB,EAAEK,CAAC,KAAK,KAAK;QAClC,sBAAsB,EAAEA,CAAC,KAAK,QAAQ;QACtC,mBAAmB,EAAEA,CAAC,KAAK,QAAQ;QACnC,uBAAuB,EAAEA,CAAC,KAAK;MACjC,CAAC,EACDhB,CACF,CAAC,EACD,CAACW,CAAC,EAAEK,CAAC,EAAEhB,CAAC,CACV,CAAC;MAAEoB,CAAC,GAAGZ,CAAC,GAAG,GAAG,OAAOA,CAAC,CAACa,IAAI,IAAI,QAAQ,GAAGb,CAAC,CAACa,IAAI,GAAG,IAAI,GAAGb,CAAC,CAACa,IAAI,IAAI,OAAOb,CAAC,CAACc,IAAI,IAAI,QAAQ,GAAGd,CAAC,CAACc,IAAI,GAAG,IAAI,GAAGd,CAAC,CAACc,IAAI,EAAE,GAAG,KAAK,CAAC;MAAEC,CAAC,GAAGjC,CAAC,CAAC+B,IAAI,IAAI/B,CAAC,CAAC+B,IAAI,CAACG,GAAG,CAAEC,CAAC,IAAK,GAAG,OAAOA,CAAC,CAACC,MAAM,IAAI,QAAQ,GAAGD,CAAC,CAACC,MAAM,GAAG,IAAI,GAAGD,CAAC,CAACC,MAAM,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAGtC,CAAC,CAACgC,IAAI,IAAIhC,CAAC,CAACgC,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAK,GAAG,OAAOA,CAAC,CAACI,KAAK,IAAI,QAAQ,GAAGJ,CAAC,CAACI,KAAK,GAAG,IAAI,GAAGJ,CAAC,CAACI,KAAK,EAAE,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;MAAEG,CAAC,GAAG;QACtVvB,GAAG,EAAEa,CAAC;QACNW,mBAAmB,EAAEH,CAAC;QACtBI,gBAAgB,EAAET,CAAC;QACnB,GAAGrB;MACL,CAAC;IACD,OAAO,eAAgBpB,CAAC,CAACmD,aAAa,CAAC,KAAK,EAAE;MAAEC,GAAG,EAAE1C,CAAC;MAAEO,SAAS,EAAEoB,CAAC;MAAElB,KAAK,EAAE6B,CAAC;MAAE3B,EAAE,EAAEC,CAAC,IAAIM;IAAE,CAAC,EAAEJ,CAAC,CAAC;EAClG,CAAC,CAAC;EAAEG,CAAC,GAAG;IACNM,MAAM,EAAE,SAAS;IACjBG,MAAM,EAAE,SAAS;IACjBX,GAAG,EAAE,KAAK;EACZ,CAAC;AACDnB,CAAC,CAAC+C,SAAS,GAAG;EACZpC,SAAS,EAAEhB,CAAC,CAACqD,MAAM;EACnBnC,KAAK,EAAElB,CAAC,CAACsD,MAAM;EACfhC,QAAQ,EAAEtB,CAAC,CAACuD,GAAG;EACfnC,EAAE,EAAEpB,CAAC,CAACqD,MAAM;EACZ7B,GAAG,EAAExB,CAAC,CAACwD,KAAK,CAAC;IACXlB,IAAI,EAAEtC,CAAC,CAACyD,SAAS,CAAC,CAACzD,CAAC,CAACqD,MAAM,EAAErD,CAAC,CAAC0D,MAAM,CAAC,CAAC;IACvCC,OAAO,EAAE3D,CAAC,CAACyD,SAAS,CAAC,CAACzD,CAAC,CAACqD,MAAM,EAAErD,CAAC,CAAC0D,MAAM,CAAC;EAC3C,CAAC,CAAC;EACF5B,KAAK,EAAE9B,CAAC,CAACwD,KAAK,CAAC;IACbtB,QAAQ,EAAElC,CAAC,CAAC4D,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;IACzD7B,UAAU,EAAE/B,CAAC,CAAC4D,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC;EAC3D,CAAC;AACH,CAAC;AACDvD,CAAC,CAACwD,WAAW,GAAG,sBAAsB;AACtC,SACExD,CAAC,IAAIyD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}