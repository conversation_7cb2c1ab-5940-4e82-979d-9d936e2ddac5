import React from 'react';
import PageTitle from '..';
import { render, screen } from '@testing-library/react';

const sampleTitle = 'Sample Title';

describe('<PageTitle/>', () => {
  it('should render PageTitle component', () => {
    const { container } = render(<PageTitle />);
    expect(container.innerHTML).not.toBe(null);
  });

  it('should contain Title', () => {
    render(<PageTitle title={sampleTitle} />);
    const outputElement = screen.getByRole('heading');
    expect(outputElement).toBeInTheDocument();
  });

  it('should render children', () => {
    render(
      <PageTitle title={sampleTitle}>
        <div>Hello</div>
      </PageTitle>
    );
    const outputElement = screen.getByText('Sample Title');
    expect(outputElement).toBeInTheDocument();
  });
});
