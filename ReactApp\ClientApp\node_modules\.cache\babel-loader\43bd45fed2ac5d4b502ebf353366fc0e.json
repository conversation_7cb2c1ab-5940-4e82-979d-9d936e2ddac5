{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as u, classNames as f } from \"@progress/kendo-react-common\";\nconst c = e.forwardRef((o, n) => {\n  const {\n      children: l,\n      className: a,\n      style: m\n    } = o,\n    s = e.useRef(null),\n    r = e.useCallback(() => {\n      s.current && u(s.current);\n    }, []),\n    p = e.useCallback(() => ({\n      element: s.current,\n      focus: r\n    }), [r]);\n  e.useImperativeHandle(n, p);\n  const i = e.useMemo(() => f(\"k-appbar-section\", a), [a]);\n  return /* @__PURE__ */e.createElement(\"div\", {\n    className: i,\n    style: m\n  }, l);\n});\nc.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object\n};\nc.displayName = \"KendoAppBarSection\";\nexport { c as AppBarSection };", "map": {"version": 3, "names": ["e", "t", "focusFirstFocusableChild", "u", "classNames", "f", "c", "forwardRef", "o", "n", "children", "l", "className", "a", "style", "m", "s", "useRef", "r", "useCallback", "current", "p", "element", "focus", "useImperativeHandle", "i", "useMemo", "createElement", "propTypes", "any", "string", "object", "displayName", "AppBarSection"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/appbar/AppBarSection.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as u, classNames as f } from \"@progress/kendo-react-common\";\nconst c = e.forwardRef((o, n) => {\n  const { children: l, className: a, style: m } = o, s = e.useRef(null), r = e.useCallback(() => {\n    s.current && u(s.current);\n  }, []), p = e.useCallback(\n    () => ({\n      element: s.current,\n      focus: r\n    }),\n    [r]\n  );\n  e.useImperativeHandle(n, p);\n  const i = e.useMemo(() => f(\"k-appbar-section\", a), [a]);\n  return /* @__PURE__ */ e.createElement(\"div\", { className: i, style: m }, l);\n});\nc.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object\n};\nc.displayName = \"KendoAppBarSection\";\nexport {\n  c as AppBarSection\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,wBAAwB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC7F,MAAMC,CAAC,GAAGN,CAAC,CAACO,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,SAAS,EAAEC,CAAC;MAAEC,KAAK,EAAEC;IAAE,CAAC,GAAGP,CAAC;IAAEQ,CAAC,GAAGhB,CAAC,CAACiB,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGlB,CAAC,CAACmB,WAAW,CAAC,MAAM;MAC7FH,CAAC,CAACI,OAAO,IAAIjB,CAAC,CAACa,CAAC,CAACI,OAAO,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;IAAEC,CAAC,GAAGrB,CAAC,CAACmB,WAAW,CACvB,OAAO;MACLG,OAAO,EAAEN,CAAC,CAACI,OAAO;MAClBG,KAAK,EAAEL;IACT,CAAC,CAAC,EACF,CAACA,CAAC,CACJ,CAAC;EACDlB,CAAC,CAACwB,mBAAmB,CAACf,CAAC,EAAEY,CAAC,CAAC;EAC3B,MAAMI,CAAC,GAAGzB,CAAC,CAAC0B,OAAO,CAAC,MAAMrB,CAAC,CAAC,kBAAkB,EAAEQ,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC;EACxD,OAAO,eAAgBb,CAAC,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAAEf,SAAS,EAAEa,CAAC;IAAEX,KAAK,EAAEC;EAAE,CAAC,EAAEJ,CAAC,CAAC;AAC9E,CAAC,CAAC;AACFL,CAAC,CAACsB,SAAS,GAAG;EACZlB,QAAQ,EAAET,CAAC,CAAC4B,GAAG;EACfjB,SAAS,EAAEX,CAAC,CAAC6B,MAAM;EACnBhB,KAAK,EAAEb,CAAC,CAAC8B;AACX,CAAC;AACDzB,CAAC,CAAC0B,WAAW,GAAG,oBAAoB;AACpC,SACE1B,CAAC,IAAI2B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}