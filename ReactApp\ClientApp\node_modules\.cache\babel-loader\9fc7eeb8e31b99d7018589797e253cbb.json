{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as C, useId as M, classNames as y } from \"@progress/kendo-react-common\";\nconst l = e.forwardRef((o, m) => {\n  const {\n      children: d,\n      className: c,\n      style: u,\n      id: f\n    } = o,\n    i = e.useRef(null),\n    p = e.useCallback(() => {\n      i.current && C(i.current);\n    }, []),\n    b = e.useCallback(() => ({\n      element: i.current,\n      focus: p\n    }), [p]);\n  e.useImperativeHandle(m, b);\n  const k = M(),\n    a = e.useMemo(() => o.themeColor || r.themeColor, [o.themeColor]),\n    n = e.useMemo(() => o.position || r.position, [o.position]),\n    s = e.useMemo(() => o.positionMode || r.positionMode, [o.positionMode]),\n    h = e.useMemo(() => y(\"k-appbar\", {\n      \"k-appbar-top\": n === \"top\",\n      \"k-appbar-bottom\": n === \"bottom\",\n      \"k-appbar-static\": s === \"static\",\n      \"k-appbar-sticky\": s === \"sticky\",\n      \"k-appbar-fixed\": s === \"fixed\",\n      [`k-appbar-${a}`]: !!a\n    }, c), [n, s, a, c]);\n  return /* @__PURE__ */e.createElement(\"div\", {\n    className: h,\n    style: u,\n    id: f || k\n  }, d);\n});\nl.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  themeColor: t.string,\n  position: t.oneOf([\"top\", \"bottom\"]),\n  positionMode: t.oneOf([\"static\", \"sticky\", \"fixed\"])\n};\nconst r = {\n  themeColor: \"light\",\n  position: \"top\",\n  positionMode: \"static\"\n};\nl.displayName = \"KendoAppBar\";\nexport { l as AppBar };", "map": {"version": 3, "names": ["e", "t", "focusFirstFocusableChild", "C", "useId", "M", "classNames", "y", "l", "forwardRef", "o", "m", "children", "d", "className", "c", "style", "u", "id", "f", "i", "useRef", "p", "useCallback", "current", "b", "element", "focus", "useImperativeHandle", "k", "a", "useMemo", "themeColor", "r", "n", "position", "s", "positionMode", "h", "createElement", "propTypes", "any", "string", "object", "oneOf", "displayName", "AppBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/appbar/AppBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { focusFirstFocusableChild as C, useId as M, classNames as y } from \"@progress/kendo-react-common\";\nconst l = e.forwardRef((o, m) => {\n  const { children: d, className: c, style: u, id: f } = o, i = e.useRef(null), p = e.useCallback(() => {\n    i.current && C(i.current);\n  }, []), b = e.useCallback(\n    () => ({\n      element: i.current,\n      focus: p\n    }),\n    [p]\n  );\n  e.useImperativeHandle(m, b);\n  const k = M(), a = e.useMemo(() => o.themeColor || r.themeColor, [o.themeColor]), n = e.useMemo(() => o.position || r.position, [o.position]), s = e.useMemo(() => o.positionMode || r.positionMode, [o.positionMode]), h = e.useMemo(\n    () => y(\n      \"k-appbar\",\n      {\n        \"k-appbar-top\": n === \"top\",\n        \"k-appbar-bottom\": n === \"bottom\",\n        \"k-appbar-static\": s === \"static\",\n        \"k-appbar-sticky\": s === \"sticky\",\n        \"k-appbar-fixed\": s === \"fixed\",\n        [`k-appbar-${a}`]: !!a\n      },\n      c\n    ),\n    [n, s, a, c]\n  );\n  return /* @__PURE__ */ e.createElement(\"div\", { className: h, style: u, id: f || k }, d);\n});\nl.propTypes = {\n  children: t.any,\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  themeColor: t.string,\n  position: t.oneOf([\"top\", \"bottom\"]),\n  positionMode: t.oneOf([\"static\", \"sticky\", \"fixed\"])\n};\nconst r = {\n  themeColor: \"light\",\n  position: \"top\",\n  positionMode: \"static\"\n};\nl.displayName = \"KendoAppBar\";\nexport {\n  l as AppBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,wBAAwB,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AACzG,MAAMC,CAAC,GAAGR,CAAC,CAACS,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,SAAS,EAAEC,CAAC;MAAEC,KAAK,EAAEC,CAAC;MAAEC,EAAE,EAAEC;IAAE,CAAC,GAAGT,CAAC;IAAEU,CAAC,GAAGpB,CAAC,CAACqB,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGtB,CAAC,CAACuB,WAAW,CAAC,MAAM;MACpGH,CAAC,CAACI,OAAO,IAAIrB,CAAC,CAACiB,CAAC,CAACI,OAAO,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;IAAEC,CAAC,GAAGzB,CAAC,CAACuB,WAAW,CACvB,OAAO;MACLG,OAAO,EAAEN,CAAC,CAACI,OAAO;MAClBG,KAAK,EAAEL;IACT,CAAC,CAAC,EACF,CAACA,CAAC,CACJ,CAAC;EACDtB,CAAC,CAAC4B,mBAAmB,CAACjB,CAAC,EAAEc,CAAC,CAAC;EAC3B,MAAMI,CAAC,GAAGxB,CAAC,CAAC,CAAC;IAAEyB,CAAC,GAAG9B,CAAC,CAAC+B,OAAO,CAAC,MAAMrB,CAAC,CAACsB,UAAU,IAAIC,CAAC,CAACD,UAAU,EAAE,CAACtB,CAAC,CAACsB,UAAU,CAAC,CAAC;IAAEE,CAAC,GAAGlC,CAAC,CAAC+B,OAAO,CAAC,MAAMrB,CAAC,CAACyB,QAAQ,IAAIF,CAAC,CAACE,QAAQ,EAAE,CAACzB,CAAC,CAACyB,QAAQ,CAAC,CAAC;IAAEC,CAAC,GAAGpC,CAAC,CAAC+B,OAAO,CAAC,MAAMrB,CAAC,CAAC2B,YAAY,IAAIJ,CAAC,CAACI,YAAY,EAAE,CAAC3B,CAAC,CAAC2B,YAAY,CAAC,CAAC;IAAEC,CAAC,GAAGtC,CAAC,CAAC+B,OAAO,CACnO,MAAMxB,CAAC,CACL,UAAU,EACV;MACE,cAAc,EAAE2B,CAAC,KAAK,KAAK;MAC3B,iBAAiB,EAAEA,CAAC,KAAK,QAAQ;MACjC,iBAAiB,EAAEE,CAAC,KAAK,QAAQ;MACjC,iBAAiB,EAAEA,CAAC,KAAK,QAAQ;MACjC,gBAAgB,EAAEA,CAAC,KAAK,OAAO;MAC/B,CAAC,YAAYN,CAAC,EAAE,GAAG,CAAC,CAACA;IACvB,CAAC,EACDf,CACF,CAAC,EACD,CAACmB,CAAC,EAAEE,CAAC,EAAEN,CAAC,EAAEf,CAAC,CACb,CAAC;EACD,OAAO,eAAgBf,CAAC,CAACuC,aAAa,CAAC,KAAK,EAAE;IAAEzB,SAAS,EAAEwB,CAAC;IAAEtB,KAAK,EAAEC,CAAC;IAAEC,EAAE,EAAEC,CAAC,IAAIU;EAAE,CAAC,EAAEhB,CAAC,CAAC;AAC1F,CAAC,CAAC;AACFL,CAAC,CAACgC,SAAS,GAAG;EACZ5B,QAAQ,EAAEX,CAAC,CAACwC,GAAG;EACf3B,SAAS,EAAEb,CAAC,CAACyC,MAAM;EACnB1B,KAAK,EAAEf,CAAC,CAAC0C,MAAM;EACfzB,EAAE,EAAEjB,CAAC,CAACyC,MAAM;EACZV,UAAU,EAAE/B,CAAC,CAACyC,MAAM;EACpBP,QAAQ,EAAElC,CAAC,CAAC2C,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;EACpCP,YAAY,EAAEpC,CAAC,CAAC2C,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;AACrD,CAAC;AACD,MAAMX,CAAC,GAAG;EACRD,UAAU,EAAE,OAAO;EACnBG,QAAQ,EAAE,KAAK;EACfE,YAAY,EAAE;AAChB,CAAC;AACD7B,CAAC,CAACqC,WAAW,GAAG,aAAa;AAC7B,SACErC,CAAC,IAAIsC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}