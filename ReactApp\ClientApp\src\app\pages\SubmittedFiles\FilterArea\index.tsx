import { <PERSON><PERSON>, <PERSON>, <PERSON>, Select } from 'antd';
import React from 'react';
import { Tooltip } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import Search from '@app/components/Search';
import styles from './index.module.less';
import { BiSortDown } from 'react-icons/bi';

type Props = {
  onSearch: (value: string) => void;
  onSortChange: () => void;
  onSeachByChange: (value: string) => void;
  onSortByChange: (value: string) => void;
  onStatusChange: (value: string) => void;
  onCategoryChange: (value: string) => void;
  sortBy: string;
};

const FilterArea: React.FC<Props> = (props) => {
  return (
    <div className={'yj_cp_card_filter_section'}>
      {/* <Row gutter={10}>
        <Col span={10}>
          <div>
            <Search placeholder="Search by File Title" suffix={<SearchOutlined />} onSearch={(value: string) => console.log(value)} />
          </div>
        </Col>
        <Col span={8}>
          <Row gutter={10} align="middle">
            <Col>
              <div className={styles.YJ_CP_FILTER_TITLE}>Search By</div>
            </Col>
            <Col flex="auto">
              <Select key={1} defaultValue="1" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSeachByChange(value)}>
                <Select.Option value="1">File title</Select.Option>
              </Select>
            </Col>
          </Row>
        </Col>
        <Col span={6}>
          <Row gutter={10} align="middle">
            <Col>
              <div className={styles.YJ_CP_FILTER_TITLE}>Sort By</div>
            </Col>
            <Col flex="auto">
              <Select key={1} defaultValue="1" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onSortByChange(value)}>
                <Select.Option value="1">Uploaded at</Select.Option>
              </Select>
            </Col>
            <Col>
              {(() => {
                if (props.sortBy === 'desc')
                  return (
                    <BiSortDown
                      onClick={props.onSortChange}
                      style={{
                        transform: 'rotateY(180deg)',
                        fontSize: '1.5rem',
                      }}
                    />
                  );
                else
                  return (
                    <BiSortDown
                      onClick={props.onSortChange}
                      style={{
                        transform: 'rotate(180deg)',
                        fontSize: '1.5rem',
                      }}
                    />
                  );
              })()}
            </Col>
          </Row>
        </Col>
      </Row>
      <Row style={{ marginTop: '10px' }} gutter={10}>
        <Col span={3}>
          <Button className={styles.yj_cp_add_filter_btn} type="link" icon={<PlusOutlined />}>
            ADD FILTER
          </Button>
        </Col>
        <Col span={4}>
          <Row gutter={10} align="middle">
            <Col>
              <div className={styles.YJ_CP_FILTER_TITLE}>Category</div>
            </Col>
            <Col span={12}>
              <Select key={1} defaultValue="1" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onStatusChange(value)}>
                <Select.Option value="1">All</Select.Option>
              </Select>
            </Col>
          </Row>
        </Col>
        <Col span={4}>
          <Row gutter={10} align="middle">
            <Col>
              <div className={styles.YJ_CP_FILTER_TITLE}>Status</div>
            </Col>
            <Col span={12}>
              <Select key={1} defaultValue="1" className={styles.YJ_CP_FILTER_SELECT} onChange={(value: string) => props.onCategoryChange(value)}>
                <Select.Option value="1">All</Select.Option>
              </Select>
            </Col>
          </Row>
        </Col>
      </Row> */}
    </div>
  );
};

export default FilterArea;
