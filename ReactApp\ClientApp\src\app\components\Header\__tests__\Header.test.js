import React from "react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import Header from "..";
import { render, screen } from "@testing-library/react";
jest.mock("../index.module.less", () => ({
  yjAppLogo: "yjAppLogo",
}));

const ReduxProvider = ({ children, store }) => <Provider store={store}>{children}</Provider>;
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const createHeaderComponent = (headerProps) => {
  const INITIAL_STATE = {
    grid: {
      selectedElement: {},
      columnQueryParameters: {},
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <Header {...headerProps} />
    </ReduxProvider>
  );
};

describe("<Header/>", () => {
  it("should render Header component", () => {
    const { container } = render(createHeaderComponent());
    expect(container.outerHTML).not.toBe(null);
  });

  it("should contain logo image", () => {
    render(createHeaderComponent());
    const outputElement = screen.getAllByRole("img");
    expect(outputElement).not.toBe(null);
  });

  it("should contain right side top menu", () => {
    render(createHeaderComponent());
    const outputElement = screen.getAllByRole("menu");
    expect(outputElement).not.toBe(null);
  });
});
