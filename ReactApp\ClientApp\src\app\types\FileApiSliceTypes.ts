import { FileRecord } from '@app/components/forms/UploaderSubmit/types';
import { CancelToken } from 'axios';
import { DownloadURIType, FileIdType, PaginatedRecordsType, PaginationType, SelectedSiteType } from './commonApiSliceTypes';
import { PublishedFileType, SubmittedFilesType } from './fileTypes';

export type GetPublishedFileRequest = SelectedSiteType & { options: PaginationType };
export type GetPublishedFileResponse = PaginatedRecordsType<PublishedFileType>;
export type GetPublishedFileCountRequest = SelectedSiteType;
export type GetPublishedFileCountResponse = { totalRecordCount: number };
export type GetSubmittedFilesRequest = SelectedSiteType & { options: PaginationType };
export type GetSubmittedFilesResponse = PaginatedRecordsType<SubmittedFilesType>;
export type SaveFileDataRequest = { files?: FileRecord[]; siteId: string };
export type SaveFileDataResponse = { batchId: string; status: string };
export type DownloadFileURIRequest = FileIdType;
export type DownloadFileURIResponse = { uri: string };
export type DownloadPortalFileURIRequest = FileIdType;
export type DownloadPortalFileURIResponse = DownloadURIType;
export type DownloadZipFileURIRequest = { fileIds: string[] };
export type DownloadZipFileURIResponse = DownloadURIType;
export type DownloadPortalZipFileURIRequest = { fileIds: string[] };
export type DownloadPortalZipFileURIResponse = DownloadURIType;
export type DownloadFileRequest = { uri: string };
export type DownloadFileResponse = any;
export type DownloadZipFileRequest = { uri: string; files: string[] };
export type DownloadZipFileResponse = any;
export type UploadFilesRequest = { formData: FormData; onUploadProgress: (progressEvent: any) => void; cancelToken: CancelToken };
export type UploadFilesResponse = { referenceNumber: string; success: boolean };
export type RemoveFilesRequest = { siteId: string; referenceNumber: any[]; removeChunks?: boolean };
export type RemoveFilesResponse = { deletedFiles: string[] };
export type SoftDeleteFilesRequest = { siteId: string; fileId: any[]};
export type SoftDeleteFilesResponse = { softDeletedFiles: string[] };
