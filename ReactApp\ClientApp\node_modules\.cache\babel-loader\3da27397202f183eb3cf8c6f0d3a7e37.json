{"ast": null, "code": "function replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n/**\n * Removes a CSS class from a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nexport default function removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else if (typeof element.className === 'string') {\n    element.className = replaceClassName(element.className, className);\n  } else {\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n  }\n}", "map": {"version": 3, "names": ["replaceClassName", "origClass", "classToRemove", "replace", "RegExp", "removeClass", "element", "className", "classList", "remove", "setAttribute", "baseVal"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/dom-helpers/esm/removeClass.js"], "sourcesContent": ["function replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n/**\n * Removes a CSS class from a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\n\nexport default function removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else if (typeof element.className === 'string') {\n    element.className = replaceClassName(element.className, className);\n  } else {\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n  }\n}"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EAClD,OAAOD,SAAS,CAACE,OAAO,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGF,aAAa,GAAG,WAAW,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACzI;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,eAAe,SAASE,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACtD,IAAID,OAAO,CAACE,SAAS,EAAE;IACrBF,OAAO,CAACE,SAAS,CAACC,MAAM,CAACF,SAAS,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOD,OAAO,CAACC,SAAS,KAAK,QAAQ,EAAE;IAChDD,OAAO,CAACC,SAAS,GAAGP,gBAAgB,CAACM,OAAO,CAACC,SAAS,EAAEA,SAAS,CAAC;EACpE,CAAC,MAAM;IACLD,OAAO,CAACI,YAAY,CAAC,OAAO,EAAEV,gBAAgB,CAACM,OAAO,CAACC,SAAS,IAAID,OAAO,CAACC,SAAS,CAACI,OAAO,IAAI,EAAE,EAAEJ,SAAS,CAAC,CAAC;EAClH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}