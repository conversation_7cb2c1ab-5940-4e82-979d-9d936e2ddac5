import { DownloadOutlined, DownOutlined } from '@ant-design/icons';
import { useDownloadFileMutation, useDownloadZipFileMutation } from '@app/api/fileApiSlice';
import { useDownloadZipFileURIMutation, useLazyDownloadFileURIQuery, useGetPublishedFileQuery } from '@app/api/fileManagementApiSlice';
import CustomModal from '@app/components/CustomModal';
import DownloadModal from '@app/components/DownloadModal';
import { DownloadModalDownloadTypes } from '@app/components/DownloadModal/types';
import FileCard from '@app/components/FileCard/FileCard';
import InfinityList from '@app/components/InfinityList';
import SiteSelection from '@app/components/SiteSelection';
import { useAppSelector } from '@app/hooks/useAppSelector';
import useWindowDimensions from '@app/hooks/useWindowDimensions';
import { PageContent, PageTitle } from '@app/layouts/MasterLayout';
import { selectAppState } from '@app/appSlice';
import { FileType, PublishedFileType } from '@app/types/fileTypes';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import { errorNotification, infoNotification } from '@app/utils/antNotifications';
import logger from '@app/utils/logger';
import { Button, Checkbox, Col, Divider, Dropdown, Menu, Row } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import FilterArea from './FilterArea';
import styles from './index.module.less';

const MIN_SCRREN_RESOLUTION = 1366;
const MIN_INFINITY_HEIGHT = 315;
const MAX_INFINITY_HEIGHT = 600;
const LIMIT = 10;

const PublishedFiles = (props: any) => {
  const [sortDir, setSortDir] = useState('desc');
  const [items, setItems] = useState<Array<PublishedFileType>>([]);
  const [selectedFiles, setSelectedFiles] = useState<Array<string>>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [selectTotalFiles, setSelectTotalFiles] = useState(false);
  const [totalFileCount, setTotalFileCount] = useState(0);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadType, setDownloadType] = useState<DownloadModalDownloadTypes | undefined>(DownloadModalDownloadTypes.individual);
  const { selectedSite } = useAppSelector(selectAppState);
  const { width } = useWindowDimensions();
  const [triggerDownloadFile] = useDownloadFileMutation();
  const [triggerDownloadZipFile] = useDownloadZipFileMutation();
  const [pageOptions, setPageOptions] = useState<any>({
    limit: LIMIT,
    offset: 0,
  });
  const { data: publishedFilesRecords, isFetching } = useGetPublishedFileQuery({
    siteId: selectedSite,
    options: pageOptions,
  });
  const [triggerDownloadFileURI] = useLazyDownloadFileURIQuery();
  const [triggerDownloadZipFileURI] = useDownloadZipFileURIMutation();
  let navigate = useNavigate();

  useEffect(() => {
    setSelectedFiles([]);
  }, [selectedSite]);

  useEffect(() => {
    if (items.length != 0) {
      setIndeterminate(selectedFiles.length > 0 && selectedFiles.length < items.length);
      setSelectAll(selectedFiles.length === items.length);
      if (selectedFiles.length !== items.length) setSelectTotalFiles(false);
    }
  }, [selectedFiles, totalFileCount, items]);

  const toggleSortDir = () => {
    setSortDir(sortDir === 'desc' ? 'asc' : 'desc');
  };

  const onSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setSelectedFiles(checked ? items.map((item: PublishedFileType) => item.fileId) : []);
  };

  const downloadAndSaveFile = async (fileId: string, isMultiple?: boolean, isZip?: boolean, fileList?: FileType[]) => {
    if (isZip) {
      infoNotification([''], 'Zip is being downloaded.');
      const fileIdList = [] as any;
      fileList?.forEach((file) => {
        fileIdList.push(file.fileId);
      });

      if (fileIdList.length > 0) {
        try {
          let { payload, uri } = await triggerDownloadZipFileURI({
            fileIds: fileIdList,
          }).unwrap();
          triggerDownloadZipFile({ files: payload, uri });
        } catch (error: any) {
          const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));

          if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
            navigate('/forbidden');
          } else {
            errorNotification([''], 'Download Failed');
          }

          logger.error('File Management Module', 'Download Component', errorObject);
        }
      }
      return;
    }
    if (!isMultiple) infoNotification([''], 'File is being downloaded.');
    try {
      let { data: URIData } = await triggerDownloadFileURI({ fileId });
      if (URIData) triggerDownloadFile(URIData).unwrap();
    } catch (error: any) {
      const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));

      if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
        navigate('/forbidden');
      } else {
        errorNotification([''], 'Download Failed');
      }

      logger.error('File Management Module', 'Download Component', errorObject);
    }
  };

  const cardContent = (file: PublishedFileType) => {
    return (
      <div>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Size:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.size}</div>
          </Col>
        </Row>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Year:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.year}</div>
          </Col>
        </Row>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Published at:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.publishedAt).format('YYYY-MM-DD hh:mmA')}</div>
          </Col>
        </Row>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Published by:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.publishedBy.name}</div>
          </Col>
        </Row>
      </div>
    );
  };

  const generateCards = (file: PublishedFileType) => {
    return (
      <FileCard
        fileId={file.fileId}
        fileType={file.type}
        title={file.title}
        isSelected={selectedFiles.includes(file.fileId)}
        content={cardContent(file)}
        trailingIcons={
          <DownloadOutlined
            className={styles.YJ_CP_FileCard_DownloadIcon}
            onClick={(event) => {
              downloadAndSaveFile(file.fileId);
              event.stopPropagation();
            }}
          />
        }
        onSelectChange={(isSelected) => {
          if (isSelected) {
            setSelectedFiles([...selectedFiles, file.fileId]);
          } else {
            setSelectedFiles(selectedFiles.filter((id) => id !== file.fileId));
          }
        }}
      />
    );
  };

  const handleMenuClick = (e: any) => {
    if (e.key === '1') {
      if (selectedFiles.length === 1) {
        downloadAndSaveFile(selectedFiles[0]);
      } else {
        displaydownloadModal(DownloadModalDownloadTypes.individual, true);
      }
    } else {
      displaydownloadModal(DownloadModalDownloadTypes.zip, true);
    }
  };

  const menu: any = [
    {
      label: 'Download files',
      key: '1',
    },
  ];

  if (selectedFiles.length > 1) {
    menu.push({
      label: 'Download as a zip files',
      key: '2',
    });
  }

  const downloadMenu = <Menu onClick={handleMenuClick} items={menu} />;

  const handleOnDownloadModalCancel = () => {
    setShowDownloadModal(false);
  };

  const displaydownloadModal = (downloadTypeInput: DownloadModalDownloadTypes, display: boolean) => {
    setDownloadType(downloadTypeInput);
    setShowDownloadModal(display);
  };

  return (
    <>
      <PageTitle title={props.title}>
        <SiteSelection />
      </PageTitle>
      <PageContent>
        <FilterArea
          sortBy={sortDir}
          onSeachByChange={(value) => console.log(value)}
          onSortChange={toggleSortDir}
          onSearch={(value) => console.log(value)}
          onSortByChange={(value) => console.log(value)}
          onStatusChange={(value) => console.log(value)}
        />
        <Divider />
        <Row style={{ marginTop: '-15px', padding: '0 0 10px 18px' }} justify="space-between">
          <Col>
            <Checkbox checked={selectAll} indeterminate={indeterminate} onChange={(e) => onSelectAll(e.target.checked)} />
            {(() => {
              if (selectedFiles.length > 0)
                return (
                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_SELECTED_COUNT}>
                    {(() => (selectTotalFiles ? totalFileCount : selectedFiles.length))()}
                    {' out of ' + totalFileCount + ' files selected'}
                  </div>
                );
              else
                return (
                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_NONE_SELECTED}>
                    None selected
                  </div>
                );
            })()}
            {selectedFiles.length > 0 && items.length < totalFileCount && (
              <a
                className={styles.YJ_CP_SELECT_TOTAL_FILES}
                type="link"
                onClick={() => {
                  onSelectAll(true);
                  setSelectTotalFiles(true);
                }}
              >
                {'Select all ' + totalFileCount + ' files'}
              </a>
            )}
          </Col>
          <Col>
            <Dropdown trigger={['click']} overlay={downloadMenu} disabled={!selectAll && !selectTotalFiles && selectedFiles.length === 0} className={styles.yj_cp_download_btn}>
              <Button icon={<DownloadOutlined />}>
                DOWNLOAD <DownOutlined />
              </Button>
            </Dropdown>
          </Col>
        </Row>
        <div style={{ width: '100%' }}>
          {selectedSite && (
            <InfinityList
              setPaginations={(page: number, searchValue?: string | undefined) => {
                setPageOptions({
                  limit: LIMIT,
                  offset: page - 1,
                  search: searchValue,
                });
              }}
              heightInPx={(() => {
                if (width <= MIN_SCRREN_RESOLUTION) return MIN_INFINITY_HEIGHT;
                else return MAX_INFINITY_HEIGHT;
              })()}
              onTotalCount={(count) => setTotalFileCount(count)}
              key="YJ-CP-PublishedFiles"
              paginatedLimit={20}
              idKeyValue="fileId"
              formatValue={(value: PublishedFileType) => generateCards(value)}
              notFoundContent={'No records available.'}
              grid={{ column: 1, gutter: 0 }}
              onRecordsChange={(records) => setItems(records)}
              data={publishedFilesRecords}
              isLoading={isFetching}
            />
          )}
        </div>
      </PageContent>
      {/*  Download Option Menu Modal */}
      <CustomModal
        visible={showDownloadModal}
        title={'Download File(s)'}
        size={'small'}
        onCancel={handleOnDownloadModalCancel}
        footer={[
          <Button onClick={handleOnDownloadModalCancel} key="submit" type="primary">
            Done
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <DownloadModal
            downloadAndSaveFile={downloadAndSaveFile}
            hasDownloaded={(hasDownloaded: boolean) => {
              if (hasDownloaded) {
                setShowDownloadModal(false);
              }
            }}
            selectedFiles={items.filter((item) => selectedFiles.includes(item.fileId))}
            downloadType={downloadType}
          />
        </div>
      </CustomModal>
    </>
  );
};

export default PublishedFiles;
