{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Keys as e } from \"@progress/kendo-react-common\";\nimport { isIdZeroLevel as i, getFirstChildId as N, getRootParentId as b, getDirectParentId as a, getShortId as O, isIdFirstLevel as q, getItemById as D, getDirectSiblingIdForLevelZero as P, createId as J } from \"./itemsIdsUtils.mjs\";\nconst Q = /\\S/;\nfunction V(g, n, G, S, l, c) {\n  const H = u();\n  switch (G) {\n    case e.left:\n      return x();\n    case e.right:\n      return B();\n    case e.up:\n      return A();\n    case e.down:\n      return z();\n    case e.enter:\n    case e.space:\n      return R();\n    case e.home:\n      return W();\n    case e.end:\n      return Z();\n    case e.esc:\n      return _();\n    default:\n      return X() ? M() : n;\n  }\n  function x() {\n    return l ? c ? F() : U() : c ? C() : K();\n  }\n  function B() {\n    return l ? c ? U() : F() : c ? K() : C();\n  }\n  function z() {\n    return l ? f() : i(n) ? d() : f();\n  }\n  function A() {\n    return l ? s() : i(n) ? E() : s();\n  }\n  function R() {\n    return H.disabled ? n : h() ? N(n) : b(n);\n  }\n  function W() {\n    return y()[0].id;\n  }\n  function Z() {\n    const t = y();\n    return t[t.length - 1].id;\n  }\n  function _() {\n    return i(n) ? n : a(n);\n  }\n  function M() {\n    const t = S.toLowerCase(),\n      r = y(),\n      o = Number(O(n)),\n      p = r.slice(o + 1).concat(r.slice(0, o + 1)).find(j => (j.text || \"\").toLowerCase().startsWith(t));\n    return p ? p.id : n;\n  }\n  function K() {\n    return i(n) ? s() : q(n) ? d(s(b(n))) : a(n);\n  }\n  function U() {\n    return i(n) ? E() : a(n);\n  }\n  function C() {\n    return i(n) ? f() : d(h() ? n : f(b(n)));\n  }\n  function F() {\n    return d(i(n) || h() ? n : f(b(n)));\n  }\n  function d(t) {\n    return v(!0, t);\n  }\n  function E(t) {\n    return v(!1, t);\n  }\n  function X() {\n    return S.length === 1 && Q.test(S);\n  }\n  function u(t, r) {\n    return t === void 0 && (t = n), r === void 0 && (r = g), D(t, r);\n  }\n  function h(t) {\n    return u(t).items.length > 0;\n  }\n  function s(t) {\n    return L(!1, t);\n  }\n  function f(t) {\n    return L(!0, t);\n  }\n  function $(t) {\n    const r = u(t).items;\n    return r[r.length - 1].id;\n  }\n  function y() {\n    return i(n) ? g : u(a(n), g).items;\n  }\n  function v(t, r) {\n    r === void 0 && (r = n);\n    const o = u(r);\n    return h(r) && !o.disabled ? t ? N(r) : $(r) : r;\n  }\n  function L(t, r) {\n    if (r === void 0 && (r = n), i(r)) return P(t, r, g.length);\n    const o = a(r),\n      w = O(r),\n      p = u(o).items.length;\n    return J(P(t, w, p), o);\n  }\n}\nexport { V as getNewItemIdUponKeyboardNavigation };", "map": {"version": 3, "names": ["Keys", "e", "isIdZeroLevel", "i", "getFirstChildId", "N", "getRootParentId", "b", "getDirectParentId", "a", "getShortId", "O", "isIdFirstLevel", "q", "getItemById", "D", "getDirectSiblingIdForLevelZero", "P", "createId", "J", "Q", "V", "g", "n", "G", "S", "l", "c", "H", "u", "left", "x", "right", "B", "up", "A", "down", "z", "enter", "space", "R", "home", "W", "end", "Z", "esc", "_", "X", "M", "F", "U", "C", "K", "f", "d", "s", "E", "disabled", "h", "y", "id", "t", "length", "toLowerCase", "r", "o", "Number", "p", "slice", "concat", "find", "j", "text", "startsWith", "v", "test", "items", "L", "$", "w", "getNewItemIdUponKeyboardNavigation"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/getNewItemIdUponKeyboardNavigation.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { Keys as e } from \"@progress/kendo-react-common\";\nimport { isIdZeroLevel as i, getFirstChildId as N, getRootParentId as b, getDirectParentId as a, getShortId as O, isIdFirstLevel as q, getItemById as D, getDirectSiblingIdForLevelZero as P, createId as J } from \"./itemsIdsUtils.mjs\";\nconst Q = /\\S/;\nfunction V(g, n, G, S, l, c) {\n  const H = u();\n  switch (G) {\n    case e.left:\n      return x();\n    case e.right:\n      return B();\n    case e.up:\n      return A();\n    case e.down:\n      return z();\n    case e.enter:\n    case e.space:\n      return R();\n    case e.home:\n      return W();\n    case e.end:\n      return Z();\n    case e.esc:\n      return _();\n    default:\n      return X() ? M() : n;\n  }\n  function x() {\n    return l ? c ? F() : U() : c ? C() : K();\n  }\n  function B() {\n    return l ? c ? U() : F() : c ? K() : C();\n  }\n  function z() {\n    return l ? f() : i(n) ? d() : f();\n  }\n  function A() {\n    return l ? s() : i(n) ? E() : s();\n  }\n  function R() {\n    return H.disabled ? n : h() ? N(n) : b(n);\n  }\n  function W() {\n    return y()[0].id;\n  }\n  function Z() {\n    const t = y();\n    return t[t.length - 1].id;\n  }\n  function _() {\n    return i(n) ? n : a(n);\n  }\n  function M() {\n    const t = S.toLowerCase(), r = y(), o = Number(O(n)), p = r.slice(o + 1).concat(r.slice(0, o + 1)).find((j) => (j.text || \"\").toLowerCase().startsWith(t));\n    return p ? p.id : n;\n  }\n  function K() {\n    return i(n) ? s() : q(n) ? d(s(b(n))) : a(n);\n  }\n  function U() {\n    return i(n) ? E() : a(n);\n  }\n  function C() {\n    return i(n) ? f() : d(\n      h() ? n : f(b(n))\n    );\n  }\n  function F() {\n    return d(\n      i(n) || h() ? n : f(b(n))\n    );\n  }\n  function d(t) {\n    return v(!0, t);\n  }\n  function E(t) {\n    return v(!1, t);\n  }\n  function X() {\n    return S.length === 1 && Q.test(S);\n  }\n  function u(t, r) {\n    return t === void 0 && (t = n), r === void 0 && (r = g), D(t, r);\n  }\n  function h(t) {\n    return u(t).items.length > 0;\n  }\n  function s(t) {\n    return L(!1, t);\n  }\n  function f(t) {\n    return L(!0, t);\n  }\n  function $(t) {\n    const r = u(t).items;\n    return r[r.length - 1].id;\n  }\n  function y() {\n    return i(n) ? g : u(a(n), g).items;\n  }\n  function v(t, r) {\n    r === void 0 && (r = n);\n    const o = u(r);\n    return h(r) && !o.disabled ? t ? N(r) : $(r) : r;\n  }\n  function L(t, r) {\n    if (r === void 0 && (r = n), i(r))\n      return P(t, r, g.length);\n    const o = a(r), w = O(r), p = u(o).items.length;\n    return J(P(t, w, p), o);\n  }\n}\nexport {\n  V as getNewItemIdUponKeyboardNavigation\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,IAAIC,CAAC,QAAQ,8BAA8B;AACxD,SAASC,aAAa,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,iBAAiB,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,8BAA8B,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,qBAAqB;AACxO,MAAMC,CAAC,GAAG,IAAI;AACd,SAASC,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,MAAMC,CAAC,GAAGC,CAAC,CAAC,CAAC;EACb,QAAQL,CAAC;IACP,KAAKvB,CAAC,CAAC6B,IAAI;MACT,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAK9B,CAAC,CAAC+B,KAAK;MACV,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKhC,CAAC,CAACiC,EAAE;MACP,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKlC,CAAC,CAACmC,IAAI;MACT,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKpC,CAAC,CAACqC,KAAK;IACZ,KAAKrC,CAAC,CAACsC,KAAK;MACV,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKvC,CAAC,CAACwC,IAAI;MACT,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAKzC,CAAC,CAAC0C,GAAG;MACR,OAAOC,CAAC,CAAC,CAAC;IACZ,KAAK3C,CAAC,CAAC4C,GAAG;MACR,OAAOC,CAAC,CAAC,CAAC;IACZ;MACE,OAAOC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,GAAGzB,CAAC;EACxB;EACA,SAASQ,CAACA,CAAA,EAAG;IACX,OAAOL,CAAC,GAAGC,CAAC,GAAGsB,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,GAAGvB,CAAC,GAAGwB,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC;EAC1C;EACA,SAASnB,CAACA,CAAA,EAAG;IACX,OAAOP,CAAC,GAAGC,CAAC,GAAGuB,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,GAAGtB,CAAC,GAAGyB,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC;EAC1C;EACA,SAASd,CAACA,CAAA,EAAG;IACX,OAAOX,CAAC,GAAG2B,CAAC,CAAC,CAAC,GAAGlD,CAAC,CAACoB,CAAC,CAAC,GAAG+B,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC;EACnC;EACA,SAASlB,CAACA,CAAA,EAAG;IACX,OAAOT,CAAC,GAAG6B,CAAC,CAAC,CAAC,GAAGpD,CAAC,CAACoB,CAAC,CAAC,GAAGiC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC;EACnC;EACA,SAASf,CAACA,CAAA,EAAG;IACX,OAAOZ,CAAC,CAAC6B,QAAQ,GAAGlC,CAAC,GAAGmC,CAAC,CAAC,CAAC,GAAGrD,CAAC,CAACkB,CAAC,CAAC,GAAGhB,CAAC,CAACgB,CAAC,CAAC;EAC3C;EACA,SAASmB,CAACA,CAAA,EAAG;IACX,OAAOiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,EAAE;EAClB;EACA,SAAShB,CAACA,CAAA,EAAG;IACX,MAAMiB,CAAC,GAAGF,CAAC,CAAC,CAAC;IACb,OAAOE,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,CAACF,EAAE;EAC3B;EACA,SAASd,CAACA,CAAA,EAAG;IACX,OAAO3C,CAAC,CAACoB,CAAC,CAAC,GAAGA,CAAC,GAAGd,CAAC,CAACc,CAAC,CAAC;EACxB;EACA,SAASyB,CAACA,CAAA,EAAG;IACX,MAAMa,CAAC,GAAGpC,CAAC,CAACsC,WAAW,CAAC,CAAC;MAAEC,CAAC,GAAGL,CAAC,CAAC,CAAC;MAAEM,CAAC,GAAGC,MAAM,CAACvD,CAAC,CAACY,CAAC,CAAC,CAAC;MAAE4C,CAAC,GAAGH,CAAC,CAACI,KAAK,CAACH,CAAC,GAAG,CAAC,CAAC,CAACI,MAAM,CAACL,CAAC,CAACI,KAAK,CAAC,CAAC,EAAEH,CAAC,GAAG,CAAC,CAAC,CAAC,CAACK,IAAI,CAAEC,CAAC,IAAK,CAACA,CAAC,CAACC,IAAI,IAAI,EAAE,EAAET,WAAW,CAAC,CAAC,CAACU,UAAU,CAACZ,CAAC,CAAC,CAAC;IAC1J,OAAOM,CAAC,GAAGA,CAAC,CAACP,EAAE,GAAGrC,CAAC;EACrB;EACA,SAAS6B,CAACA,CAAA,EAAG;IACX,OAAOjD,CAAC,CAACoB,CAAC,CAAC,GAAGgC,CAAC,CAAC,CAAC,GAAG1C,CAAC,CAACU,CAAC,CAAC,GAAG+B,CAAC,CAACC,CAAC,CAAChD,CAAC,CAACgB,CAAC,CAAC,CAAC,CAAC,GAAGd,CAAC,CAACc,CAAC,CAAC;EAC9C;EACA,SAAS2B,CAACA,CAAA,EAAG;IACX,OAAO/C,CAAC,CAACoB,CAAC,CAAC,GAAGiC,CAAC,CAAC,CAAC,GAAG/C,CAAC,CAACc,CAAC,CAAC;EAC1B;EACA,SAAS4B,CAACA,CAAA,EAAG;IACX,OAAOhD,CAAC,CAACoB,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,GAAGC,CAAC,CACnBI,CAAC,CAAC,CAAC,GAAGnC,CAAC,GAAG8B,CAAC,CAAC9C,CAAC,CAACgB,CAAC,CAAC,CAClB,CAAC;EACH;EACA,SAAS0B,CAACA,CAAA,EAAG;IACX,OAAOK,CAAC,CACNnD,CAAC,CAACoB,CAAC,CAAC,IAAImC,CAAC,CAAC,CAAC,GAAGnC,CAAC,GAAG8B,CAAC,CAAC9C,CAAC,CAACgB,CAAC,CAAC,CAC1B,CAAC;EACH;EACA,SAAS+B,CAACA,CAACO,CAAC,EAAE;IACZ,OAAOa,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC;EACjB;EACA,SAASL,CAACA,CAACK,CAAC,EAAE;IACZ,OAAOa,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC;EACjB;EACA,SAASd,CAACA,CAAA,EAAG;IACX,OAAOtB,CAAC,CAACqC,MAAM,KAAK,CAAC,IAAI1C,CAAC,CAACuD,IAAI,CAAClD,CAAC,CAAC;EACpC;EACA,SAASI,CAACA,CAACgC,CAAC,EAAEG,CAAC,EAAE;IACf,OAAOH,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAGtC,CAAC,CAAC,EAAEyC,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG1C,CAAC,CAAC,EAAEP,CAAC,CAAC8C,CAAC,EAAEG,CAAC,CAAC;EAClE;EACA,SAASN,CAACA,CAACG,CAAC,EAAE;IACZ,OAAOhC,CAAC,CAACgC,CAAC,CAAC,CAACe,KAAK,CAACd,MAAM,GAAG,CAAC;EAC9B;EACA,SAASP,CAACA,CAACM,CAAC,EAAE;IACZ,OAAOgB,CAAC,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAAC;EACjB;EACA,SAASR,CAACA,CAACQ,CAAC,EAAE;IACZ,OAAOgB,CAAC,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAAC;EACjB;EACA,SAASiB,CAACA,CAACjB,CAAC,EAAE;IACZ,MAAMG,CAAC,GAAGnC,CAAC,CAACgC,CAAC,CAAC,CAACe,KAAK;IACpB,OAAOZ,CAAC,CAACA,CAAC,CAACF,MAAM,GAAG,CAAC,CAAC,CAACF,EAAE;EAC3B;EACA,SAASD,CAACA,CAAA,EAAG;IACX,OAAOxD,CAAC,CAACoB,CAAC,CAAC,GAAGD,CAAC,GAAGO,CAAC,CAACpB,CAAC,CAACc,CAAC,CAAC,EAAED,CAAC,CAAC,CAACsD,KAAK;EACpC;EACA,SAASF,CAACA,CAACb,CAAC,EAAEG,CAAC,EAAE;IACfA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAGzC,CAAC,CAAC;IACvB,MAAM0C,CAAC,GAAGpC,CAAC,CAACmC,CAAC,CAAC;IACd,OAAON,CAAC,CAACM,CAAC,CAAC,IAAI,CAACC,CAAC,CAACR,QAAQ,GAAGI,CAAC,GAAGxD,CAAC,CAAC2D,CAAC,CAAC,GAAGc,CAAC,CAACd,CAAC,CAAC,GAAGA,CAAC;EAClD;EACA,SAASa,CAACA,CAAChB,CAAC,EAAEG,CAAC,EAAE;IACf,IAAIA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAGzC,CAAC,CAAC,EAAEpB,CAAC,CAAC6D,CAAC,CAAC,EAC/B,OAAO/C,CAAC,CAAC4C,CAAC,EAAEG,CAAC,EAAE1C,CAAC,CAACwC,MAAM,CAAC;IAC1B,MAAMG,CAAC,GAAGxD,CAAC,CAACuD,CAAC,CAAC;MAAEe,CAAC,GAAGpE,CAAC,CAACqD,CAAC,CAAC;MAAEG,CAAC,GAAGtC,CAAC,CAACoC,CAAC,CAAC,CAACW,KAAK,CAACd,MAAM;IAC/C,OAAO3C,CAAC,CAACF,CAAC,CAAC4C,CAAC,EAAEkB,CAAC,EAAEZ,CAAC,CAAC,EAAEF,CAAC,CAAC;EACzB;AACF;AACA,SACE5C,CAAC,IAAI2D,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}