{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\PortalControls\\\\index.tsx\";\nimport React from 'react';\nimport { Card } from '@progress/kendo-react-layout';\nimport { FaUser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';\nimport styles from './index.module.less';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortalControls = () => {\n  const controlItems = [{\n    id: 'user-management',\n    title: 'User Management',\n    description: 'Manage portal users and assign portal roles',\n    icon: FaUser,\n    onClick: () => {\n      // TODO: Navigate to user management\n      console.log('Navigate to User Management');\n    }\n  }, {\n    id: 'security-groups',\n    title: 'Security Groups',\n    description: 'Create and manage permission-based user groups for file access',\n    icon: FaLock,\n    onClick: () => {\n      // TODO: Navigate to security groups\n      console.log('Navigate to Security Groups');\n    }\n  }, {\n    id: 'permissions',\n    title: 'Permissions',\n    description: 'Set security options for security groups',\n    icon: FaCog,\n    onClick: () => {\n      // TODO: Navigate to permissions\n      console.log('Navigate to Permissions');\n    }\n  }];\n  const renderControlCard = item => {\n    const IconComponent = item.icon;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      className: styles.portalControlCard,\n      onClick: item.onClick,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.cardContent,\n        children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n          className: styles.cardIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.textSection,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: styles.cardTitle,\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: styles.cardDescription,\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.arrowSection,\n          children: /*#__PURE__*/_jsxDEV(FaChevronRight, {\n            className: styles.arrowIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, item.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.portalControlsContainer,\n    children: controlItems.map(renderControlCard)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_c = PortalControls;\nexport default PortalControls;\nvar _c;\n$RefreshReg$(_c, \"PortalControls\");", "map": {"version": 3, "names": ["React", "Card", "FaUser", "FaLock", "FaCog", "FaChevronRight", "styles", "jsxDEV", "_jsxDEV", "PortalControls", "controlItems", "id", "title", "description", "icon", "onClick", "console", "log", "renderControlCard", "item", "IconComponent", "className", "portalControlCard", "children", "cardContent", "cardIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textSection", "cardTitle", "cardDescription", "arrowSection", "arrowIcon", "portalControlsContainer", "map", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/pages/PortalControls/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card } from '@progress/kendo-react-layout';\r\nimport { <PERSON>a<PERSON>ser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';\r\nimport styles from './index.module.less';\r\n\r\ninterface PortalControlItem {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  onClick?: () => void;\r\n}\r\n\r\nconst PortalControls = () => {\r\n  const controlItems: PortalControlItem[] = [\r\n    {\r\n      id: 'user-management',\r\n      title: 'User Management',\r\n      description: 'Manage portal users and assign portal roles',\r\n      icon: FaUser,\r\n      onClick: () => {\r\n        // TODO: Navigate to user management\r\n        console.log('Navigate to User Management');\r\n      }\r\n    },\r\n    {\r\n      id: 'security-groups',\r\n      title: 'Security Groups',\r\n      description: 'Create and manage permission-based user groups for file access',\r\n      icon: FaLock,\r\n      onClick: () => {\r\n        // TODO: Navigate to security groups\r\n        console.log('Navigate to Security Groups');\r\n      }\r\n    },\r\n    {\r\n      id: 'permissions',\r\n      title: 'Permissions',\r\n      description: 'Set security options for security groups',\r\n      icon: FaCog,\r\n      onClick: () => {\r\n        // TODO: Navigate to permissions\r\n        console.log('Navigate to Permissions');\r\n      }\r\n    }\r\n  ];\r\n\r\n  const renderControlCard = (item: PortalControlItem) => {\r\n    const IconComponent = item.icon;\r\n\r\n    return (\r\n      <Card\r\n        key={item.id}\r\n        className={styles.portalControlCard}\r\n        onClick={item.onClick}\r\n      >\r\n        <div className={styles.cardContent}>\r\n          <IconComponent className={styles.cardIcon} />\r\n          <div className={styles.textSection}>\r\n            <h3 className={styles.cardTitle}>{item.title}</h3>\r\n            <p className={styles.cardDescription}>{item.description}</p>\r\n          </div>\r\n          <div className={styles.arrowSection}>\r\n            <FaChevronRight className={styles.arrowIcon} />\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={styles.portalControlsContainer}>\r\n      {controlItems.map(renderControlCard)}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortalControls;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,8BAA8B;AACnD,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,cAAc,QAAQ,gBAAgB;AACtE,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUzC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,YAAiC,GAAG,CACxC;IACEC,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6CAA6C;IAC1DC,IAAI,EAAEZ,MAAM;IACZa,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC,EACD;IACEN,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,gEAAgE;IAC7EC,IAAI,EAAEX,MAAM;IACZY,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC,EACD;IACEN,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,EAAEV,KAAK;IACXW,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAIC,IAAuB,IAAK;IACrD,MAAMC,aAAa,GAAGD,IAAI,CAACL,IAAI;IAE/B,oBACEN,OAAA,CAACP,IAAI;MAEHoB,SAAS,EAAEf,MAAM,CAACgB,iBAAkB;MACpCP,OAAO,EAAEI,IAAI,CAACJ,OAAQ;MAAAQ,QAAA,eAEtBf,OAAA;QAAKa,SAAS,EAAEf,MAAM,CAACkB,WAAY;QAAAD,QAAA,gBACjCf,OAAA,CAACY,aAAa;UAACC,SAAS,EAAEf,MAAM,CAACmB;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CrB,OAAA;UAAKa,SAAS,EAAEf,MAAM,CAACwB,WAAY;UAAAP,QAAA,gBACjCf,OAAA;YAAIa,SAAS,EAAEf,MAAM,CAACyB,SAAU;YAAAR,QAAA,EAAEJ,IAAI,CAACP;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClDrB,OAAA;YAAGa,SAAS,EAAEf,MAAM,CAAC0B,eAAgB;YAAAT,QAAA,EAAEJ,IAAI,CAACN;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNrB,OAAA;UAAKa,SAAS,EAAEf,MAAM,CAAC2B,YAAa;UAAAV,QAAA,eAClCf,OAAA,CAACH,cAAc;YAACgB,SAAS,EAAEf,MAAM,CAAC4B;UAAU;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAbDV,IAAI,CAACR,EAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcR,CAAC;EAEX,CAAC;EAED,oBACErB,OAAA;IAAKa,SAAS,EAAEf,MAAM,CAAC6B,uBAAwB;IAAAZ,QAAA,EAC5Cb,YAAY,CAAC0B,GAAG,CAAClB,iBAAiB;EAAC;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC;AAACQ,EAAA,GA9DI5B,cAAc;AAgEpB,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}