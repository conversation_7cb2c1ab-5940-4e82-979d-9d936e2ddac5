import { DeleteOutlined, DownOutlined, FileExclamationOutlined, SyncOutlined, UpOutlined } from '@ant-design/icons';
import { FULL_PERCENTAGE } from '@app/components/FileUploader';
import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { FileList, UploadProgressProps } from '../types';
import styles from './index.module.less';
import ProgressingFile from './ProgressingFile';

const UploadProgress: React.FC<UploadProgressProps> = ({ files, onRetry, onRetryAll, onDelete, onDeleteAll, onComplete }) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const total = Object.keys(files).length;
  const completed = Object.entries(files).filter(
    ([_, info]) => Math.round(info.percent || 0) === FULL_PERCENTAGE && info.chunkCounter === info.chunkCount && info.completed
  ).length;
  const hasErrored = !!Object.entries(files).filter(([_, info]) => info.error).length;

  useEffect(() => {
    if (completed === total && !hasErrored) {
      onComplete?.();
    }
  }, [completed, onComplete, total]);

  return (
    <div className={styles.yjUploadProgressContainer}>
      <div className={styles.yjProgressWrapperHeader}>
        <h6>
          Uploading Files{' '}
          <span className={styles.uploadingCount}>
            {' '}
            ({completed}/{total} Files Completed)
          </span>
        </h6>

        {hasErrored && (
          <div className={styles.yjProgressErrorMessageWrapper}>
            <span className={styles.attRequired}>
              <FileExclamationOutlined /> Some files require your attention to complete upload
            </span>
          </div>
        )}

        <div className={styles.yjProgressHeaderButtonWrapper}>
          <div className={styles.yjProgressHeaderButtonLeft}>
            <Button icon={<SyncOutlined />} onClick={onRetryAll} disabled={!hasErrored} style={{ cursor: hasErrored ? 'pointer' : 'not-allowed' }} />
            <Button icon={<DeleteOutlined />} onClick={onDeleteAll} />
          </div>
          {total > 1 && (
            <div className={styles.yjProgressHeaderButtonRight}>
              <Button icon={isCollapsed ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsCollapsed(!isCollapsed)} />
            </div>
          )}
        </div>
      </div>

      <div className={styles.yjFileProgressWrapper}>
        <div className={isCollapsed ? `${styles.yjProgressWrapper} ${styles.yjProgressWrapperCollapsed}` : styles.yjProgressWrapper}>
          {renderFilesList(files, onRetry, onDelete)}
        </div>
      </div>
    </div>
  );
};

function renderFilesList(files: FileList, onRetry: (uid: string) => void, onDelete: (uid: string) => void) {
  return Object.entries(files).map(([uid, info]) => {
    return <ProgressingFile key={uid} uid={uid} info={info} onRetry={onRetry} onDelete={onDelete} />;
  });
}

export default UploadProgress;
