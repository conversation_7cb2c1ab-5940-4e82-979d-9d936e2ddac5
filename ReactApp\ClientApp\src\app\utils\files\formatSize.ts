const DEFAULT_DECIMAL_PLACES = 2;
export default (bytes: number, decimals = DEFAULT_DECIMAL_PLACES) => {
  if (!bytes || bytes === 0 || bytes < 0) {
    return '0 Bytes';
  }
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

const CAPACITY_FOR_A_GB = 1073741824;
const NUMBER_THREE = 3;
export const DEFAULT_CHUNK_SIZE = CAPACITY_FOR_A_GB * NUMBER_THREE;
