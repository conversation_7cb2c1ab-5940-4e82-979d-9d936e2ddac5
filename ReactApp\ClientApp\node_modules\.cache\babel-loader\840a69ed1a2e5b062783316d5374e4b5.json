{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport r from \"prop-types\";\nimport { classNames as T } from \"@progress/kendo-react-common\";\nimport { cardType as c, cardOrientation as l } from \"./interfaces/Enums.mjs\";\nconst d = e.forwardRef((t, p) => {\n  const {\n      children: m,\n      dir: f,\n      style: u,\n      className: y,\n      type: a = s.type,\n      orientation: o = s.orientation,\n      ...R\n    } = t,\n    n = e.useRef(null),\n    i = e.useRef(null);\n  return e.useImperativeHandle(n, () => ({\n    element: i.current,\n    props: t\n  })), e.useImperativeHandle(p, () => n.current), /* @__PURE__ */e.createElement(\"div\", {\n    dir: f,\n    style: u,\n    ref: i,\n    className: T(\"k-card\", y, {\n      [`k-card-${a}`]: a !== c.DEFAULT\n    }, o === null ? void 0 : o !== l.HORIZONTAL ? \"k-card-vertical\" : \"k-card-horizontal\"),\n    ...R\n  }, m);\n});\nd.displayName = \"KendoReactCard\";\nd.propTypes = {\n  children: r.node,\n  className: r.string,\n  dir: r.string,\n  orientation: r.oneOf([\"horizontal\", \"vertical\"]),\n  style: r.object,\n  type: r.oneOf([\"default\", \"primary\", \"info\", \"success\", \"warning\", \"error\"])\n};\nconst s = {\n  type: c.DEFAULT,\n  orientation: l.VERTICAL\n};\nexport { d as Card };", "map": {"version": 3, "names": ["e", "r", "classNames", "T", "cardType", "c", "cardOrientation", "l", "d", "forwardRef", "t", "p", "children", "m", "dir", "f", "style", "u", "className", "y", "type", "a", "s", "orientation", "o", "R", "n", "useRef", "i", "useImperativeHandle", "element", "current", "props", "createElement", "ref", "DEFAULT", "HORIZONTAL", "displayName", "propTypes", "node", "string", "oneOf", "object", "VERTICAL", "Card"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/Card.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport r from \"prop-types\";\nimport { classNames as T } from \"@progress/kendo-react-common\";\nimport { cardType as c, cardOrientation as l } from \"./interfaces/Enums.mjs\";\nconst d = e.forwardRef((t, p) => {\n  const {\n    children: m,\n    dir: f,\n    style: u,\n    className: y,\n    type: a = s.type,\n    orientation: o = s.orientation,\n    ...R\n  } = t, n = e.useRef(null), i = e.useRef(null);\n  return e.useImperativeHandle(n, () => ({\n    element: i.current,\n    props: t\n  })), e.useImperativeHandle(p, () => n.current), /* @__PURE__ */ e.createElement(\n    \"div\",\n    {\n      dir: f,\n      style: u,\n      ref: i,\n      className: T(\n        \"k-card\",\n        y,\n        { [`k-card-${a}`]: a !== c.DEFAULT },\n        o === null ? void 0 : o !== l.HORIZONTAL ? \"k-card-vertical\" : \"k-card-horizontal\"\n      ),\n      ...R\n    },\n    m\n  );\n});\nd.displayName = \"KendoReactCard\";\nd.propTypes = {\n  children: r.node,\n  className: r.string,\n  dir: r.string,\n  orientation: r.oneOf([\"horizontal\", \"vertical\"]),\n  style: r.object,\n  type: r.oneOf([\"default\", \"primary\", \"info\", \"success\", \"warning\", \"error\"])\n};\nconst s = {\n  type: c.DEFAULT,\n  orientation: l.VERTICAL\n};\nexport {\n  d as Card\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,SAASC,QAAQ,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,wBAAwB;AAC5E,MAAMC,CAAC,GAAGR,CAAC,CAACS,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAM;MACJC,QAAQ,EAAEC,CAAC;MACXC,GAAG,EAAEC,CAAC;MACNC,KAAK,EAAEC,CAAC;MACRC,SAAS,EAAEC,CAAC;MACZC,IAAI,EAAEC,CAAC,GAAGC,CAAC,CAACF,IAAI;MAChBG,WAAW,EAAEC,CAAC,GAAGF,CAAC,CAACC,WAAW;MAC9B,GAAGE;IACL,CAAC,GAAGf,CAAC;IAAEgB,CAAC,GAAG1B,CAAC,CAAC2B,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAG5B,CAAC,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC7C,OAAO3B,CAAC,CAAC6B,mBAAmB,CAACH,CAAC,EAAE,OAAO;IACrCI,OAAO,EAAEF,CAAC,CAACG,OAAO;IAClBC,KAAK,EAAEtB;EACT,CAAC,CAAC,CAAC,EAAEV,CAAC,CAAC6B,mBAAmB,CAAClB,CAAC,EAAE,MAAMe,CAAC,CAACK,OAAO,CAAC,EAAE,eAAgB/B,CAAC,CAACiC,aAAa,CAC7E,KAAK,EACL;IACEnB,GAAG,EAAEC,CAAC;IACNC,KAAK,EAAEC,CAAC;IACRiB,GAAG,EAAEN,CAAC;IACNV,SAAS,EAAEf,CAAC,CACV,QAAQ,EACRgB,CAAC,EACD;MAAE,CAAC,UAAUE,CAAC,EAAE,GAAGA,CAAC,KAAKhB,CAAC,CAAC8B;IAAQ,CAAC,EACpCX,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,KAAKjB,CAAC,CAAC6B,UAAU,GAAG,iBAAiB,GAAG,mBACjE,CAAC;IACD,GAAGX;EACL,CAAC,EACDZ,CACF,CAAC;AACH,CAAC,CAAC;AACFL,CAAC,CAAC6B,WAAW,GAAG,gBAAgB;AAChC7B,CAAC,CAAC8B,SAAS,GAAG;EACZ1B,QAAQ,EAAEX,CAAC,CAACsC,IAAI;EAChBrB,SAAS,EAAEjB,CAAC,CAACuC,MAAM;EACnB1B,GAAG,EAAEb,CAAC,CAACuC,MAAM;EACbjB,WAAW,EAAEtB,CAAC,CAACwC,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EAChDzB,KAAK,EAAEf,CAAC,CAACyC,MAAM;EACftB,IAAI,EAAEnB,CAAC,CAACwC,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;AAC7E,CAAC;AACD,MAAMnB,CAAC,GAAG;EACRF,IAAI,EAAEf,CAAC,CAAC8B,OAAO;EACfZ,WAAW,EAAEhB,CAAC,CAACoC;AACjB,CAAC;AACD,SACEnC,CAAC,IAAIoC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}