import { CloseOutlined } from '@ant-design/icons';
import { Button, Input, Table } from 'antd';
import Tooltip from 'antd/es/tooltip';
import { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { FileRecord, UploaderSubmitFileListProps } from './types';

const FileList: React.FC<UploaderSubmitFileListProps> = ({
  fileList,
  fileEvents = {
    onFileRemove: () => null,
    onFileSelect: () => null,
    onFileSelectAll: () => null,
    onSaveSuccess: () => null,
    onTitleUpdate: () => null,
  },
  forManageFiles = false,
  onDataChange = () => null,
}) => {
  const { onFileSelectAll, onFileSelect, onTitleUpdate, onFileRemove } = fileEvents;
  const checkedLength = fileList.filter((v) => v.checked).length;
  const baseColumns: ColumnsType<FileRecord> = [
    {
      key: 'remove',
      render: (record: FileRecord) => (
        <Tooltip title="Remove Files">
          <Button key={record.referenceNumber} icon={<CloseOutlined />} className={styles.yjDeteleFile} onClick={() => onFileRemove(record.referenceNumber)} />
        </Tooltip>
      ),
      width: 50,
    },

    {
      key: 'title',
      title: 'Title',
      dataIndex: 'title',
      render: (text: string, record: FileRecord, index: number) => {
        return (
          <>
            <Input disabled={!record.checked} key={record.referenceNumber} value={text} onChange={(e) => onTitleUpdate(e.target.value, index)} />
            {record.checked && record.error && <span style={{ color: 'red', display: 'block' }}>{record.error}</span>}
          </>
        );
      },
    },
  ];

  const [columns, setColumns] = useState<ColumnsType<FileRecord>>(baseColumns);
  useEffect(() => {
    if (forManageFiles) {
      const customColumns: ColumnsType<FileRecord> = [
        {
          key: 'title',
          title: 'Title',
          dataIndex: 'title',
          render: (text: string, record: FileRecord) => {
            return (
              <>
                <Input
                  defaultValue={text}
                  onChange={(e) => {
                    onDataChange(e.target.value !== '');
                    onTitleUpdate(e.target.value, 0);
                  }}
                />
                {record.checked && record.error && <span style={{ color: 'red' }}>{record.error}</span>}
              </>
            );
          },
        },
      ];
      setColumns(customColumns);
    }
  }, [forManageFiles]);

  const rowSelection = {
    selectedRowKeys: fileList.filter((e) => e.checked).map((e) => e.referenceNumber),
    onSelect: (record: any, selected: any) => {
      const index = fileList.findIndex((fileRecord: FileRecord) => fileRecord.referenceNumber === record.referenceNumber);
      onFileSelect(selected, index);
    },
    onSelectAll: (selected: any) => {
      onFileSelectAll(selected);
    },
  };

  return (
    <>
      {!checkedLength && <span style={{ color: 'red' }}>Select files to set properties</span>}
      <Table
        rowSelection={rowSelection}
        tableLayout="fixed"
        className={styles.yjTblFileList}
        columns={columns}
        dataSource={fileList}
        scroll={{
          y: '31vh',
        }}
        pagination={false}
        rowKey={'referenceNumber'}
      />
    </>
  );
};

export default FileList;
