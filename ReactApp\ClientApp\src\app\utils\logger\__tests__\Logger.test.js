import logger from "..";

describe("Logger Library", () => {
  it("should contain valid Instance", () => {
    expect(typeof logger).toBe("object");
  });

  it("should contain debug method", () => {
    expect(typeof logger.debug).toBe("function");
  });

  it("should contain verbose method", () => {
    expect(typeof logger.verbose).toBe("function");
  });

  it("should contain error method", () => {
    expect(typeof logger.error).toBe("function");
  });

  it("should contain info method", () => {
    expect(typeof logger.info).toBe("function");
  });

  it("should contain warn method", () => {
    expect(typeof logger.warn).toBe("function");
  });

  it("should contain validateLogHierachy method", () => {
    expect(typeof logger.validateLogHierachy).toBe("function");
  });

  it("should contain message method", () => {
    expect(typeof logger.message).toBe("function");
  });

  it("sould update log level", () => {
    logger.setLogLevel("warn");
    expect(logger.logLevel).toBe("warn");
    logger.setLogLevel("debug");
    expect(logger.logLevel).toBe("debug");
  });

  it("getLogLevel sould return the current log level", () => {
    logger.setLogLevel("warn");
    expect(logger.getLogLevel()).toBe("warn");
    logger.setLogLevel("debug");
    expect(logger.getLogLevel()).toBe("debug");
  });

  it("should validate log hierachy", () => {
    logger.setLogLevel("verbose");
    expect(logger.validateLogHierachy("verbose")).toBe(true);
    expect(logger.validateLogHierachy("debug")).toBe(false);
    expect(logger.validateLogHierachy("info")).toBe(true);
    expect(logger.validateLogHierachy("error")).toBe(true);
    expect(logger.validateLogHierachy("warn")).toBe(true);

    logger.setLogLevel("debug");
    expect(logger.validateLogHierachy("verbose")).toBe(true);
    expect(logger.validateLogHierachy("debug")).toBe(true);
    expect(logger.validateLogHierachy("info")).toBe(true);
    expect(logger.validateLogHierachy("error")).toBe(true);
    expect(logger.validateLogHierachy("warn")).toBe(true);

    logger.setLogLevel("info");
    expect(logger.validateLogHierachy("verbose")).toBe(false);
    expect(logger.validateLogHierachy("debug")).toBe(false);
    expect(logger.validateLogHierachy("info")).toBe(true);
    expect(logger.validateLogHierachy("error")).toBe(true);
    expect(logger.validateLogHierachy("warn")).toBe(true);

    logger.setLogLevel("error");
    expect(logger.validateLogHierachy("verbose")).toBe(false);
    expect(logger.validateLogHierachy("debug")).toBe(false);
    expect(logger.validateLogHierachy("info")).toBe(false);
    expect(logger.validateLogHierachy("error")).toBe(true);
    expect(logger.validateLogHierachy("warn")).toBe(false);

    logger.setLogLevel("warn");
    expect(logger.validateLogHierachy("verbose")).toBe(false);
    expect(logger.validateLogHierachy("debug")).toBe(false);
    expect(logger.validateLogHierachy("info")).toBe(false);
    expect(logger.validateLogHierachy("error")).toBe(true);
    expect(logger.validateLogHierachy("warn")).toBe(true);
  });

  it("should execute message method on log error", () => {
    const _jestMessageTester = jest.fn();
    logger.message = _jestMessageTester;

    logger.setLogLevel("debug");

    logger.debug("param1", "param2", "param3");
    logger.verbose("param1", "param2", "param3");
    logger.info("param1", "param2", "param3");
    logger.warn("param1", "param2", "param3");
    logger.error("param1", "param2", "param3");

    expect(_jestMessageTester).toBeCalledTimes(5);
    expect(_jestMessageTester).toBeCalledWith("param1", "param2", "debug", "param3");
    expect(_jestMessageTester).toBeCalledWith("param1", "param2", "verbose", "param3");
    expect(_jestMessageTester).toBeCalledWith("param1", "param2", "info", "param3");
    expect(_jestMessageTester).toBeCalledWith("param1", "param2", "warn", "param3");
    expect(_jestMessageTester).toBeCalledWith("param1", "param2", "error", "param3");
  });
});
