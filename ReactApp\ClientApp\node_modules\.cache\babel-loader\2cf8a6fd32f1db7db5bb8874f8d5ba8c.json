{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar r = /* @__PURE__ */(e => (e[e.Toggle = 0] = \"Toggle\", e[e.Next = 1] = \"Next\", e[e.Previous = 2] = \"Previous\", e[e.First = 3] = \"First\", e[e.Last = 4] = \"Last\", e[e.Left = 5] = \"Left\", e[e.Right = 6] = \"Right\", e))(r || {});\nexport { r as NavigationAction };", "map": {"version": 3, "names": ["r", "e", "Toggle", "Next", "Previous", "First", "Last", "Left", "Right", "NavigationAction"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/panelbar/interfaces/NavigationAction.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nvar r = /* @__PURE__ */ ((e) => (e[e.Toggle = 0] = \"Toggle\", e[e.Next = 1] = \"Next\", e[e.Previous = 2] = \"Previous\", e[e.First = 3] = \"First\", e[e.Last = 4] = \"Last\", e[e.Left = 5] = \"Left\", e[e.Right = 6] = \"Right\", e))(r || {});\nexport {\n  r as NavigationAction\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,CAAC,GAAG,eAAgB,CAAEC,CAAC,KAAMA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAED,CAAC,CAACA,CAAC,CAACE,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAEF,CAAC,CAACA,CAAC,CAACG,QAAQ,GAAG,CAAC,CAAC,GAAG,UAAU,EAAEH,CAAC,CAACA,CAAC,CAACI,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,EAAEJ,CAAC,CAACA,CAAC,CAACK,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAEL,CAAC,CAACA,CAAC,CAACM,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAA<PERSON>,CAAC,CAACA,CAAC,CAACO,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,EAAEP,CAAC,CAAC,EAAED,CAAC,IAAI,CAAC,CAAC,CAAC;AACrO,SACEA,CAAC,IAAIS,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}