{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\PortalControls\\\\index.tsx\";\nimport React from 'react';\nimport { FaUser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';\nimport styles from './index.module.less';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortalControls = props => {\n  const portalControlItems = [{\n    id: 'user-management',\n    title: 'User Management',\n    description: 'Manage portal users and assign portal roles',\n    icon: FaUser,\n    onClick: () => {\n      // TODO: Navigate to user management\n      console.log('Navigate to User Management');\n    }\n  }, {\n    id: 'security-groups',\n    title: 'Security Groups',\n    description: 'Create and manage permission-based user groups for file access',\n    icon: FaLock,\n    onClick: () => {\n      // TODO: Navigate to security groups\n      console.log('Navigate to Security Groups');\n    }\n  }, {\n    id: 'permissions',\n    title: 'Permissions',\n    description: 'Set security options for security groups',\n    icon: FaCog,\n    onClick: () => {\n      // TODO: Navigate to permissions\n      console.log('Navigate to Permissions');\n    }\n  }];\n  const renderPortalControlCard = item => {\n    const IconComponent = item.icon;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.portalControlCard,\n      onClick: item.onClick,\n      role: \"button\",\n      tabIndex: 0,\n      onKeyDown: e => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          item.onClick();\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.cardContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.iconContainer,\n          children: /*#__PURE__*/_jsxDEV(IconComponent, {\n            className: styles.cardIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.textContent,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: styles.cardTitle,\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: styles.cardDescription,\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.arrowContainer,\n          children: /*#__PURE__*/_jsxDEV(FaChevronRight, {\n            className: styles.arrowIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, item.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.portalControlsContainer,\n    children: portalControlItems.map(renderPortalControlCard)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_c = PortalControls;\nexport default PortalControls;\nvar _c;\n$RefreshReg$(_c, \"PortalControls\");", "map": {"version": 3, "names": ["React", "FaUser", "FaLock", "FaCog", "FaChevronRight", "styles", "jsxDEV", "_jsxDEV", "PortalControls", "props", "portalControlItems", "id", "title", "description", "icon", "onClick", "console", "log", "renderPortalControlCard", "item", "IconComponent", "className", "portalControlCard", "role", "tabIndex", "onKeyDown", "e", "key", "children", "cardContent", "iconContainer", "cardIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textContent", "cardTitle", "cardDescription", "arrowContainer", "arrowIcon", "portalControlsContainer", "map", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/pages/PortalControls/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaChevronRight } from 'react-icons/fa';\r\nimport styles from './index.module.less';\r\n\r\ninterface PortalControlItem {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  icon: React.ComponentType<any>;\r\n  onClick: () => void;\r\n}\r\n\r\nconst PortalControls = (props: any) => {\r\n  const portalControlItems: PortalControlItem[] = [\r\n    {\r\n      id: 'user-management',\r\n      title: 'User Management',\r\n      description: 'Manage portal users and assign portal roles',\r\n      icon: FaUser,\r\n      onClick: () => {\r\n        // TODO: Navigate to user management\r\n        console.log('Navigate to User Management');\r\n      }\r\n    },\r\n    {\r\n      id: 'security-groups',\r\n      title: 'Security Groups',\r\n      description: 'Create and manage permission-based user groups for file access',\r\n      icon: FaLock,\r\n      onClick: () => {\r\n        // TODO: Navigate to security groups\r\n        console.log('Navigate to Security Groups');\r\n      }\r\n    },\r\n    {\r\n      id: 'permissions',\r\n      title: 'Permissions',\r\n      description: 'Set security options for security groups',\r\n      icon: FaCog,\r\n      onClick: () => {\r\n        // TODO: Navigate to permissions\r\n        console.log('Navigate to Permissions');\r\n      }\r\n    }\r\n  ];\r\n\r\n  const renderPortalControlCard = (item: PortalControlItem) => {\r\n    const IconComponent = item.icon;\r\n\r\n    return (\r\n      <div\r\n        key={item.id}\r\n        className={styles.portalControlCard}\r\n        onClick={item.onClick}\r\n        role=\"button\"\r\n        tabIndex={0}\r\n        onKeyDown={(e) => {\r\n          if (e.key === 'Enter' || e.key === ' ') {\r\n            item.onClick();\r\n          }\r\n        }}\r\n      >\r\n        <div className={styles.cardContent}>\r\n          <div className={styles.iconContainer}>\r\n            <IconComponent className={styles.cardIcon} />\r\n          </div>\r\n          <div className={styles.textContent}>\r\n            <h3 className={styles.cardTitle}>{item.title}</h3>\r\n            <p className={styles.cardDescription}>{item.description}</p>\r\n          </div>\r\n          <div className={styles.arrowContainer}>\r\n            <FaChevronRight className={styles.arrowIcon} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={styles.portalControlsContainer}>\r\n      {portalControlItems.map(renderPortalControlCard)}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortalControls;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,cAAc,QAAQ,gBAAgB;AACtE,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUzC,MAAMC,cAAc,GAAIC,KAAU,IAAK;EACrC,MAAMC,kBAAuC,GAAG,CAC9C;IACEC,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6CAA6C;IAC1DC,IAAI,EAAEb,MAAM;IACZc,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC,EACD;IACEN,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,gEAAgE;IAC7EC,IAAI,EAAEZ,MAAM;IACZa,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC,EACD;IACEN,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,EAAEX,KAAK;IACXY,OAAO,EAAEA,CAAA,KAAM;MACb;MACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC,CACF;EAED,MAAMC,uBAAuB,GAAIC,IAAuB,IAAK;IAC3D,MAAMC,aAAa,GAAGD,IAAI,CAACL,IAAI;IAE/B,oBACEP,OAAA;MAEEc,SAAS,EAAEhB,MAAM,CAACiB,iBAAkB;MACpCP,OAAO,EAAEI,IAAI,CAACJ,OAAQ;MACtBQ,IAAI,EAAC,QAAQ;MACbC,QAAQ,EAAE,CAAE;MACZC,SAAS,EAAGC,CAAC,IAAK;QAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;UACtCR,IAAI,CAACJ,OAAO,CAAC,CAAC;QAChB;MACF,CAAE;MAAAa,QAAA,eAEFrB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAACwB,WAAY;QAAAD,QAAA,gBACjCrB,OAAA;UAAKc,SAAS,EAAEhB,MAAM,CAACyB,aAAc;UAAAF,QAAA,eACnCrB,OAAA,CAACa,aAAa;YAACC,SAAS,EAAEhB,MAAM,CAAC0B;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN5B,OAAA;UAAKc,SAAS,EAAEhB,MAAM,CAAC+B,WAAY;UAAAR,QAAA,gBACjCrB,OAAA;YAAIc,SAAS,EAAEhB,MAAM,CAACgC,SAAU;YAAAT,QAAA,EAAET,IAAI,CAACP;UAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClD5B,OAAA;YAAGc,SAAS,EAAEhB,MAAM,CAACiC,eAAgB;YAAAV,QAAA,EAAET,IAAI,CAACN;UAAW;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACN5B,OAAA;UAAKc,SAAS,EAAEhB,MAAM,CAACkC,cAAe;UAAAX,QAAA,eACpCrB,OAAA,CAACH,cAAc;YAACiB,SAAS,EAAEhB,MAAM,CAACmC;UAAU;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAtBDhB,IAAI,CAACR,EAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuBT,CAAC;EAEV,CAAC;EAED,oBACE5B,OAAA;IAAKc,SAAS,EAAEhB,MAAM,CAACoC,uBAAwB;IAAAb,QAAA,EAC5ClB,kBAAkB,CAACgC,GAAG,CAACxB,uBAAuB;EAAC;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEV,CAAC;AAACQ,EAAA,GAvEInC,cAAc;AAyEpB,eAAeA,cAAc;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}