import InfinitySelect from "..";
import { shallow, mount } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";
import initTestSuite from "@app/utils/config/TestSuite";
import { Select, Button } from "antd";

/**
 * [x] Component render without props
 * [x] Component render with props
 * [x] Snapshot of the component
 * [x] Check Select component exist
 * [x] Check Button exist with props isDelete => ture
 * [x] Check Button exist with props isDelete => false
 */
describe("<InfinitySelect/>", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render component", () => {
    const component = shallow(<InfinitySelect />);
    expect(component.html()).not.toBe(null);
  });

  it("should render component with props", () => {
    const component = shallow(<InfinitySelect endpoint="licenses" notFoundContent="No Data" mode="multiple" search="companyName" />);
    expect(component.html()).not.toBe(null);
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<InfinitySelect />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should contain a Select component", () => {
    const component = mount(<InfinitySelect />);
    expect(component.find(Select).length).toBe(1);
  });

  it("should contain a Button component", () => {
    const component = mount(<InfinitySelect isDelete={true} />);
    expect(component.find(Button).length).toBe(1);
  });

  it("should not contain a Button component", () => {
    const component = mount(<InfinitySelect />);
    expect(component.find(Button).length).toBe(0);
  });
});
