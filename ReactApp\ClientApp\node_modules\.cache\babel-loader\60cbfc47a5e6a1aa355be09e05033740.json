{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport h from \"prop-types\";\nimport { validatePackage as y, classNames as E, WatermarkOverlay as T } from \"@progress/kendo-react-common\";\nimport { TimelineHorizontal as g } from \"./TimelineHorizontal.mjs\";\nimport { TimelineVertical as M } from \"./TimelineVertical.mjs\";\nimport { packageMetadata as W } from \"../package-metadata.mjs\";\nconst b = c => {\n  const p = !y(W, {\n      component: \"Timeline\"\n    }),\n    f = {\n      alterMode: !1,\n      collapsibleEvents: !1,\n      dateFormat: \"MMM dd, yyyy\",\n      ...c\n    },\n    {\n      collapsibleEvents: i,\n      transitionDuration: r,\n      className: d,\n      alterMode: n,\n      navigatable: l,\n      horizontal: t,\n      events: o,\n      dateFormat: s,\n      onChange: k,\n      onActionClick: m\n    } = f,\n    [v, u] = e.useState(),\n    a = e.useRef(null);\n  return e.useEffect(() => {\n    a.current && u(a.current.offsetWidth);\n  }, []), /* @__PURE__ */e.createElement(\"div\", {\n    ref: a,\n    className: E(\"k-timeline\", {\n      \"k-timeline-collapsible\": i,\n      \"k-timeline-vertical\": !t,\n      \"k-timeline-alternating\": n,\n      \"k-timeline-horizontal\": t\n    }, d),\n    style: {\n      width: `${v}px`\n    }\n  }, t ? /* @__PURE__ */e.createElement(g, {\n    navigatable: l,\n    eventsData: o,\n    dateFormat: s,\n    transitionDuration: r,\n    onActionClick: m\n  }) : /* @__PURE__ */e.createElement(M, {\n    navigatable: l,\n    eventsData: o,\n    dateFormat: s,\n    alterMode: n,\n    collapsibleEvents: i,\n    transitionDuration: r,\n    onChange: k,\n    onActionClick: m\n  }), p && /* @__PURE__ */e.createElement(T, null));\n};\nb.propTypes = {\n  className: h.string\n};\nexport { b as Timeline };", "map": {"version": 3, "names": ["e", "h", "validatePackage", "y", "classNames", "E", "WatermarkOverlay", "T", "TimelineHorizontal", "g", "TimelineVertical", "M", "packageMetadata", "W", "b", "c", "p", "component", "f", "alterMode", "collapsibleEvents", "dateFormat", "i", "transitionDuration", "r", "className", "d", "n", "navigatable", "l", "horizontal", "t", "events", "o", "s", "onChange", "k", "onActionClick", "m", "v", "u", "useState", "a", "useRef", "useEffect", "current", "offsetWidth", "createElement", "ref", "style", "width", "eventsData", "propTypes", "string", "Timeline"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/timeline/Timeline.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport h from \"prop-types\";\nimport { validatePackage as y, classNames as E, WatermarkOverlay as T } from \"@progress/kendo-react-common\";\nimport { TimelineHorizontal as g } from \"./TimelineHorizontal.mjs\";\nimport { TimelineVertical as M } from \"./TimelineVertical.mjs\";\nimport { packageMetadata as W } from \"../package-metadata.mjs\";\nconst b = (c) => {\n  const p = !y(W, { component: \"Timeline\" }), f = {\n    alterMode: !1,\n    collapsibleEvents: !1,\n    dateFormat: \"MMM dd, yyyy\",\n    ...c\n  }, {\n    collapsibleEvents: i,\n    transitionDuration: r,\n    className: d,\n    alterMode: n,\n    navigatable: l,\n    horizontal: t,\n    events: o,\n    dateFormat: s,\n    onChange: k,\n    onActionClick: m\n  } = f, [v, u] = e.useState(), a = e.useRef(null);\n  return e.useEffect(() => {\n    a.current && u(a.current.offsetWidth);\n  }, []), /* @__PURE__ */ e.createElement(\n    \"div\",\n    {\n      ref: a,\n      className: E(\n        \"k-timeline\",\n        {\n          \"k-timeline-collapsible\": i,\n          \"k-timeline-vertical\": !t,\n          \"k-timeline-alternating\": n,\n          \"k-timeline-horizontal\": t\n        },\n        d\n      ),\n      style: { width: `${v}px` }\n    },\n    t ? /* @__PURE__ */ e.createElement(\n      g,\n      {\n        navigatable: l,\n        eventsData: o,\n        dateFormat: s,\n        transitionDuration: r,\n        onActionClick: m\n      }\n    ) : /* @__PURE__ */ e.createElement(\n      M,\n      {\n        navigatable: l,\n        eventsData: o,\n        dateFormat: s,\n        alterMode: n,\n        collapsibleEvents: i,\n        transitionDuration: r,\n        onChange: k,\n        onActionClick: m\n      }\n    ),\n    p && /* @__PURE__ */ e.createElement(T, null)\n  );\n};\nb.propTypes = {\n  className: h.string\n};\nexport {\n  b as Timeline\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,eAAe,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,8BAA8B;AAC3G,SAASC,kBAAkB,IAAIC,CAAC,QAAQ,0BAA0B;AAClE,SAASC,gBAAgB,IAAIC,CAAC,QAAQ,wBAAwB;AAC9D,SAASC,eAAe,IAAIC,CAAC,QAAQ,yBAAyB;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG,CAACb,CAAC,CAACU,CAAC,EAAE;MAAEI,SAAS,EAAE;IAAW,CAAC,CAAC;IAAEC,CAAC,GAAG;MAC9CC,SAAS,EAAE,CAAC,CAAC;MACbC,iBAAiB,EAAE,CAAC,CAAC;MACrBC,UAAU,EAAE,cAAc;MAC1B,GAAGN;IACL,CAAC;IAAE;MACDK,iBAAiB,EAAEE,CAAC;MACpBC,kBAAkB,EAAEC,CAAC;MACrBC,SAAS,EAAEC,CAAC;MACZP,SAAS,EAAEQ,CAAC;MACZC,WAAW,EAAEC,CAAC;MACdC,UAAU,EAAEC,CAAC;MACbC,MAAM,EAAEC,CAAC;MACTZ,UAAU,EAAEa,CAAC;MACbC,QAAQ,EAAEC,CAAC;MACXC,aAAa,EAAEC;IACjB,CAAC,GAAGpB,CAAC;IAAE,CAACqB,CAAC,EAAEC,CAAC,CAAC,GAAGxC,CAAC,CAACyC,QAAQ,CAAC,CAAC;IAAEC,CAAC,GAAG1C,CAAC,CAAC2C,MAAM,CAAC,IAAI,CAAC;EAChD,OAAO3C,CAAC,CAAC4C,SAAS,CAAC,MAAM;IACvBF,CAAC,CAACG,OAAO,IAAIL,CAAC,CAACE,CAAC,CAACG,OAAO,CAACC,WAAW,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC,EAAE,eAAgB9C,CAAC,CAAC+C,aAAa,CACrC,KAAK,EACL;IACEC,GAAG,EAAEN,CAAC;IACNjB,SAAS,EAAEpB,CAAC,CACV,YAAY,EACZ;MACE,wBAAwB,EAAEiB,CAAC;MAC3B,qBAAqB,EAAE,CAACS,CAAC;MACzB,wBAAwB,EAAEJ,CAAC;MAC3B,uBAAuB,EAAEI;IAC3B,CAAC,EACDL,CACF,CAAC;IACDuB,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAGX,CAAC;IAAK;EAC3B,CAAC,EACDR,CAAC,GAAG,eAAgB/B,CAAC,CAAC+C,aAAa,CACjCtC,CAAC,EACD;IACEmB,WAAW,EAAEC,CAAC;IACdsB,UAAU,EAAElB,CAAC;IACbZ,UAAU,EAAEa,CAAC;IACbX,kBAAkB,EAAEC,CAAC;IACrBa,aAAa,EAAEC;EACjB,CACF,CAAC,GAAG,eAAgBtC,CAAC,CAAC+C,aAAa,CACjCpC,CAAC,EACD;IACEiB,WAAW,EAAEC,CAAC;IACdsB,UAAU,EAAElB,CAAC;IACbZ,UAAU,EAAEa,CAAC;IACbf,SAAS,EAAEQ,CAAC;IACZP,iBAAiB,EAAEE,CAAC;IACpBC,kBAAkB,EAAEC,CAAC;IACrBW,QAAQ,EAAEC,CAAC;IACXC,aAAa,EAAEC;EACjB,CACF,CAAC,EACDtB,CAAC,IAAI,eAAgBhB,CAAC,CAAC+C,aAAa,CAACxC,CAAC,EAAE,IAAI,CAC9C,CAAC;AACH,CAAC;AACDO,CAAC,CAACsC,SAAS,GAAG;EACZ3B,SAAS,EAAExB,CAAC,CAACoD;AACf,CAAC;AACD,SACEvC,CAAC,IAAIwC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}