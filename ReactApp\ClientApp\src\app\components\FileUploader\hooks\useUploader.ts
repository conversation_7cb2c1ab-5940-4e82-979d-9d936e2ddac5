import { DEFAULT_CHUNK_SIZE } from '@app/utils/files/formatSize';
import { UploadFile } from 'antd/lib/upload/interface';
import Axios from 'axios';
import { useState } from 'react';
import { FILE_IS_TOO_LARGE } from '../constants/errors';
import { FileList } from '../types';

const useUploader = () => {
  const [files, setFiles] = useState<FileList>({});

  const add = (file: UploadFile, uploadOptions: any, error?: Error) => {
    if (!files[file.uid]) {
      const cancelToken = Axios.CancelToken;
      const source = cancelToken.source();
      const _chunkCount = file.size ? Math.floor(file.size / DEFAULT_CHUNK_SIZE): 0;
      setFiles((current: FileList) => {
        return {
          ...current,
          [file.uid]: {
            name: file.name,
            size: file.size ? file.size : 0,
            uploadOptions: uploadOptions,
            chunkCount: _chunkCount + 1,
            chunkCounter: 1,
            chunkStartSize: 0,
            chunkEndSize: DEFAULT_CHUNK_SIZE,
            toBeProceed: false,
            completed: false,
            referenceNumber: null,
            retryCount: 0,
            percent: 0,
            currentChunkPercent: 0,
            cancelTokenSource: source,
            cancel: source.cancel,
            error,
          },
        };
      });
    }
  };

  const updatePercent = (uid: string, percent: number, currentChunkPercent: number) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], percent, currentChunkPercent },
      };
    });
  };

  const updateError = (uid: string, error: Error) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], error },
      };
    });
  };

  const increaseChunkCounter = (uid: string) => {
    setFiles((current: FileList) => {
      return current[uid].chunkCounter !== current[uid].chunkCount
        ? {
            ...current,
            [uid]: {
              ...current[uid],
              chunkCounter: current[uid].chunkCounter + 1,
              chunkStartSize: current[uid].chunkEndSize,
              chunkEndSize: current[uid].chunkEndSize + DEFAULT_CHUNK_SIZE,
              toBeProceed: false,
              retryCount: 0,
            },
          }
        : { ...current };
    });
  };

  const setReferenceNumber = (uid: string, referenceNumber: string) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], referenceNumber: referenceNumber },
      };
    });
  };

  const changeToBeProceedStatus = (uid: string, status: boolean) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], toBeProceed: status },
      };
    });
  };

  const isCompleted = (uid: string, status: boolean) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], completed: status },
      };
    });
  };

  const incrementRetryCount = (uid: string) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: {
          ...current[uid],
          retryCount: current[uid]?.retryCount + 1,
          percent: (current[uid]?.percent as number) - (current[uid]?.currentChunkPercent as number),
          currentChunkPercent: 0,
        },
      };
    });
  };

  const resetPercent = (uid: string) => {
    setFiles((current: FileList) => {
      return {
        ...current,
        [uid]: {
          ...current[uid],
          percent: (current[uid]?.percent as number) - (current[uid]?.currentChunkPercent as number),
          currentChunkPercent: 0,
        },
      };
    });
  };

  const remove = (uid: string) => {
    files[uid].cancel?.();

    setFiles(({ [uid]: _value, ...rest }: FileList) => {
      return { ...rest };
    });
  };

  const removeAll = () => {
    Object.entries(files).forEach(([_, info]) => {
      if (info?.cancel) {
        info?.cancel();
      }
    });
    setFiles({});
  };

  //Remove The Error
  const retry = (uid: string) => {
    setFiles(({ [uid]: value, ...rest }: FileList) => {
      const { error, ...restInfo } = value;
      return { [uid]: restInfo, ...rest };
    });
  };

  const retryAll = () => {
    Object.entries(files)
      .filter(([_, info]) => info.error)
      .forEach(([uid]) => {
        //Check The Size Before Retry
        if (files[uid].error?.message === FILE_IS_TOO_LARGE) {
          updateError(uid, {
            name: FILE_IS_TOO_LARGE,
            message: FILE_IS_TOO_LARGE,
          });
        } else {
          retry(uid);
          changeToBeProceedStatus(uid, false);
        }
      });
  };

  return {
    files,
    action: {
      add,
      updatePercent,
      updateError,
      increaseChunkCounter,
      isCompleted,
      changeToBeProceedStatus,
      incrementRetryCount,
      resetPercent,
      remove,
      removeAll,
      retry,
      retryAll,
      setReferenceNumber,
    },
  };
};

export default useUploader;
