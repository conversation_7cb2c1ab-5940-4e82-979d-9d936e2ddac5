{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nconst t = {\n    linear: !1,\n    mode: \"steps\",\n    value: 0\n  },\n  o = e.createContext(t);\nexport { o as StepperContext };", "map": {"version": 3, "names": ["e", "t", "linear", "mode", "value", "o", "createContext", "StepperContext"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/stepper/context/StepperContext.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nconst t = {\n  linear: !1,\n  mode: \"steps\",\n  value: 0\n}, o = e.createContext(t);\nexport {\n  o as StepperContext\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAG;IACRC,MAAM,EAAE,CAAC,CAAC;IACVC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;EAAEC,CAAC,GAAGL,CAAC,CAACM,aAAa,CAACL,CAAC,CAAC;AACzB,SACEI,CAAC,IAAIE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}