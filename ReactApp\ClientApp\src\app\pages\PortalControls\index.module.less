@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@file-path: '../../../styles/';

// Portal Controls Container
.portalControlsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

// Portal Control Card
.portalControlCard {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;

  &:hover {
    border-color: #d0d0d0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

// Card Content Layout
.cardContent {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 20px;
}

// Icon Section
.iconSection {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cardIcon {
  font-size: 20px;
  color: #333;
}

// Text Section
.textSection {
  flex: 1;
  min-width: 0;
}

.cardTitle {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.cardDescription {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

// Arrow Section
.arrowSection {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrowIcon {
  font-size: 16px;
  color: #999;
  transition: color 0.2s ease-in-out;
}

.portalControlCard:hover .arrowIcon {
  color: #666;
}