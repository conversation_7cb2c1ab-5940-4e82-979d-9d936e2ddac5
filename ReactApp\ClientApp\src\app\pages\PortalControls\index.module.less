@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@file-path: '../../../styles/';

.yj_cp_download_btn {

    background: #1B69B9;
    border: 1px solid #1B69B9;
    border-radius: 5px;
    color: white;
}

.yj_cp_submitted_fileCard_fileStatus_dot_1 {

    background-color: #E9914E;
    border-radius: 50%;
    height: 12px;
    float: left;
    width: 12px;
    margin-top: 5px;
    margin-right: 10px;
}

.yj_cp_submitted_fileCard_fileStatus_dot_2 {

    background-color: #48B22E;
    border-radius: 50%;
    height: 12px;
    float: left;
    width: 12px;
    margin-top: 5px;
    margin-right: 10px;
}

.yj_cp_submitted_fileCard_fileStatus_dot_3 {

    background-color: #CE1017;
    border-radius: 50%;
    height: 12px;
    float: left;
    width: 12px;
    margin-top: 5px;
    margin-right: 10px;
}

.yj_cp_submitted_fileCard_fileStatus_1 {
    color: #E9914E;
    font-weight: 500;
    text-transform: uppercase;
}

.yj_cp_submitted_fileCard_fileStatus_2 {
    color: #48B22E;
    font-weight: 500;
    text-transform: uppercase;
}

.yj_cp_submitted_fileCard_fileStatus_3 {
    color: #CE1017;
    font-weight: 500;
    text-transform: uppercase;
}

.yj_cp_submitted_fileCard_1 {

    border: 1px solid #E9914E !important;
}

.yj_cp_submitted_fileCard_2 {

    border: 1px solid #48B22E !important;
}

.yj_cp_submitted_fileCard_3 {

    border: 1px solid #CE1017 !important;
}