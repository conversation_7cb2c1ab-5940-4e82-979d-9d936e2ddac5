.portalControlsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 20px;
}

.portalControlCard {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;

  &:hover {
    border-color: #d0d0d0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

.cardContent {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 20px;
}

.cardIcon {
  font-size: 20px;
  color: #333;
}

.textSection {
  flex: 1;
}

.cardTitle {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.cardDescription {
  margin: 0;
  font-size: 13px;
  color: #666;
}

.arrowIcon {
  font-size: 16px;
  color: #999;
}

.portalControlCard:hover .arrowIcon {
  color: #666;
}