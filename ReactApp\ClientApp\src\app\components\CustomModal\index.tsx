import { Modal } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import Tooltip from 'antd/lib/tooltip';
import React, { ReactNode } from 'react';

type PropTypes = {
  size?: 'small' | 'medium' | 'auto';
  children: ReactNode;
};

const CustomModal = ({ size, children, ...props }: ModalProps & PropTypes) => {
  let style = 'yjCommonModalLarge';
  if (size === 'small') {
    style = 'yjCommonModalSmall';
  } else if (size === 'medium') {
    style = 'yjCommonModalMedium';
  } else if (size === 'auto') {
    style = '';
  }

  return (
    <Modal
      maskClosable={false}
      destroyOnClose={true}
      className={style}
      {...props}
      title={
        <Tooltip placement="top" title={props.title}>
          {props.title}
        </Tooltip>
      }
    >
      {children}
    </Modal>
  );
};

export default CustomModal;
