{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { useId as g } from \"@progress/kendo-react-common\";\nconst s = e.forwardRef((n, l) => {\n  const a = e.useRef(null),\n    i = e.useCallback(() => ({\n      element: a.current\n    }), []);\n  e.useImperativeHandle(l, i);\n  const {\n      className: c,\n      style: m,\n      id: d,\n      children: p,\n      row: u,\n      col: y,\n      rowSpan: r,\n      colSpan: o\n    } = n,\n    f = g(),\n    I = {\n      gridArea: `${u || \"auto\"} / ${y || \"auto\"} / ${r ? \"span \" + r : \"auto\"} / ${o ? \"span \" + o : \"auto\"}`,\n      ...m\n    };\n  return /* @__PURE__ */e.createElement(\"div\", {\n    ref: a,\n    className: c,\n    style: I,\n    id: d || f\n  }, p);\n});\ns.propTypes = {\n  className: t.string,\n  style: t.object,\n  children: t.any,\n  id: t.string\n};\ns.displayName = \"KendoReactGridLayoutItem\";\nexport { s as GridLayoutItem };", "map": {"version": 3, "names": ["e", "t", "useId", "g", "s", "forwardRef", "n", "l", "a", "useRef", "i", "useCallback", "element", "current", "useImperativeHandle", "className", "c", "style", "m", "id", "d", "children", "p", "row", "u", "col", "y", "rowSpan", "r", "colSpan", "o", "f", "I", "gridArea", "createElement", "ref", "propTypes", "string", "object", "any", "displayName", "GridLayoutItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/gridlayout/GridLayoutItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { useId as g } from \"@progress/kendo-react-common\";\nconst s = e.forwardRef((n, l) => {\n  const a = e.useRef(null), i = e.useCallback(\n    () => ({\n      element: a.current\n    }),\n    []\n  );\n  e.useImperativeHandle(l, i);\n  const { className: c, style: m, id: d, children: p, row: u, col: y, rowSpan: r, colSpan: o } = n, f = g(), I = {\n    gridArea: `${u || \"auto\"} / ${y || \"auto\"} / ${r ? \"span \" + r : \"auto\"} / ${o ? \"span \" + o : \"auto\"}`,\n    ...m\n  };\n  return /* @__PURE__ */ e.createElement(\"div\", { ref: a, className: c, style: I, id: d || f }, p);\n});\ns.propTypes = {\n  className: t.string,\n  style: t.object,\n  children: t.any,\n  id: t.string\n};\ns.displayName = \"KendoReactGridLayoutItem\";\nexport {\n  s as GridLayoutItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,KAAK,IAAIC,CAAC,QAAQ,8BAA8B;AACzD,MAAMC,CAAC,GAAGJ,CAAC,CAACK,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC/B,MAAMC,CAAC,GAAGR,CAAC,CAACS,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGV,CAAC,CAACW,WAAW,CACzC,OAAO;MACLC,OAAO,EAAEJ,CAAC,CAACK;IACb,CAAC,CAAC,EACF,EACF,CAAC;EACDb,CAAC,CAACc,mBAAmB,CAACP,CAAC,EAAEG,CAAC,CAAC;EAC3B,MAAM;MAAEK,SAAS,EAAEC,CAAC;MAAEC,KAAK,EAAEC,CAAC;MAAEC,EAAE,EAAEC,CAAC;MAAEC,QAAQ,EAAEC,CAAC;MAAEC,GAAG,EAAEC,CAAC;MAAEC,GAAG,EAAEC,CAAC;MAAEC,OAAO,EAAEC,CAAC;MAAEC,OAAO,EAAEC;IAAE,CAAC,GAAGxB,CAAC;IAAEyB,CAAC,GAAG5B,CAAC,CAAC,CAAC;IAAE6B,CAAC,GAAG;MAC7GC,QAAQ,EAAE,GAAGT,CAAC,IAAI,MAAM,MAAME,CAAC,IAAI,MAAM,MAAME,CAAC,GAAG,OAAO,GAAGA,CAAC,GAAG,MAAM,MAAME,CAAC,GAAG,OAAO,GAAGA,CAAC,GAAG,MAAM,EAAE;MACvG,GAAGZ;IACL,CAAC;EACD,OAAO,eAAgBlB,CAAC,CAACkC,aAAa,CAAC,KAAK,EAAE;IAAEC,GAAG,EAAE3B,CAAC;IAAEO,SAAS,EAAEC,CAAC;IAAEC,KAAK,EAAEe,CAAC;IAAEb,EAAE,EAAEC,CAAC,IAAIW;EAAE,CAAC,EAAET,CAAC,CAAC;AAClG,CAAC,CAAC;AACFlB,CAAC,CAACgC,SAAS,GAAG;EACZrB,SAAS,EAAEd,CAAC,CAACoC,MAAM;EACnBpB,KAAK,EAAEhB,CAAC,CAACqC,MAAM;EACfjB,QAAQ,EAAEpB,CAAC,CAACsC,GAAG;EACfpB,EAAE,EAAElB,CAAC,CAACoC;AACR,CAAC;AACDjC,CAAC,CAACoC,WAAW,GAAG,0BAA0B;AAC1C,SACEpC,CAAC,IAAIqC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}