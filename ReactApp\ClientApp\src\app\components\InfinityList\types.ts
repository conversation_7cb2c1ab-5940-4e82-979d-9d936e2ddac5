import { ListGridType } from 'antd/lib/list';

const PAGINATED_TEN = 10;
const PAGINATED_TWENTY = 20;
const PAGINATED_THIRTY = 30;
export type paginatedLimit = typeof PAGINATED_TEN | typeof PAGINATED_TWENTY | typeof PAGINATED_THIRTY;

export type InfinityListProps = {
  data: any;
  setPaginations: (page: number, searchValue?: string) => any;
  idKeyValue: string;
  hasCheckbox?: boolean;
  hasSearch?: boolean;
  heightInPx?: number;
  paginatedLimit?: paginatedLimit;
  formatValue: (value: any, onRemove?: any) => any;
  onSelect?: (value: any, selectedList: any[], totalRecordCountValue: any, clearVisible?: boolean) => void;
  notFoundContent: string;
  selectedList?: any[];
  listClassName?: string;
  onChange?: (changed: boolean) => void;
  onChangeCheckBox?: (event: any, itemId: any) => void;
  showRecordCounter?: boolean;
  selectedCount?: number;
  checkedList?: any[];
  deletedList?: any[];
  onTotalCount?: (value: number) => void;
  onItemRemove?: (value: string) => any;
  removedItem?: any[];
  listItemStyle?: any;
  grid?: ListGridType;
  onRecordsChange?: (value: any) => void;
  isLoading:boolean;
};
