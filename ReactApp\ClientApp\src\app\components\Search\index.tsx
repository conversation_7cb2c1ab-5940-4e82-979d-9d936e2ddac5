import { Input } from 'antd';
import React from 'react';
import { SearchProps } from './types';

const Search = (props: SearchProps) => {
  return (
    <Input
      placeholder={props.placeholder}
      suffix={props.suffix}
      value={props.value}
      disabled={props.disabled}
      className={props.className}
      style={props.style}
      onChange={(e) => {
        if (props.onChange) {
          props.onChange(e.target.value);
        }
      }}
      onPressEnter={(e) => {
        if (props.onSearch) {
          props.onSearch(e.currentTarget.value);
        }
      }}
    />
  );
};

export default Search;
