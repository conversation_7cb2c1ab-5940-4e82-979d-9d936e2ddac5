{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { classNames as r } from \"@progress/kendo-react-common\";\nconst a = e => /* @__PURE__ */t.createElement(\"div\", {\n  style: e.style,\n  className: r(\"k-card-subtitle\", e.className)\n}, e.children);\na.propTypes = {\n  className: s.string\n};\nexport { a as CardSubtitle };", "map": {"version": 3, "names": ["t", "s", "classNames", "r", "a", "e", "createElement", "style", "className", "children", "propTypes", "string", "CardSubtitle"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/CardSubtitle.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport s from \"prop-types\";\nimport { classNames as r } from \"@progress/kendo-react-common\";\nconst a = (e) => /* @__PURE__ */ t.createElement(\"div\", { style: e.style, className: r(\"k-card-subtitle\", e.className) }, e.children);\na.propTypes = {\n  className: s.string\n};\nexport {\n  a as CardSubtitle\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK,eAAgBL,CAAC,CAACM,aAAa,CAAC,KAAK,EAAE;EAAEC,KAAK,EAAEF,CAAC,CAACE,KAAK;EAAEC,SAAS,EAAEL,CAAC,CAAC,iBAAiB,EAAEE,CAAC,CAACG,SAAS;AAAE,CAAC,EAAEH,CAAC,CAACI,QAAQ,CAAC;AACrIL,CAAC,CAACM,SAAS,GAAG;EACZF,SAAS,EAAEP,CAAC,CAACU;AACf,CAAC;AACD,SACEP,CAAC,IAAIQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}