{"ast": null, "code": "import React from 'react';\nconst LoginPage = /*#__PURE__*/React.lazy(_c = () => import('../pages/Login'));\n_c2 = LoginPage;\nconst PublishedFiles = /*#__PURE__*/React.lazy(_c3 = () => import('../pages/PublishedFiles'));\n_c4 = PublishedFiles;\nconst SubmittedFiles = /*#__PURE__*/React.lazy(_c5 = () => import('../pages/SubmittedFiles'));\n_c6 = SubmittedFiles;\nconst PortalControls = /*#__PURE__*/React.lazy(_c7 = () => import('../pages/PortalControls'));\n_c8 = PortalControls;\nconst AppRoutes = [{\n  path: '/',\n  title: 'Login',\n  component: LoginPage,\n  exact: true,\n  guard: []\n}, {\n  path: '/publishedFiles',\n  title: 'Published Files',\n  component: PublishedFiles,\n  exact: true,\n  guard: [],\n  useInBreadcrumbs: true\n}, {\n  path: '/submittedFiles',\n  title: 'Submitted Files',\n  component: SubmittedFiles,\n  exact: true,\n  guard: [],\n  useInBreadcrumbs: true\n}, {\n  path: '/portalControls',\n  title: 'Portal Controls',\n  component: PortalControls,\n  // To be Updated\n  exact: true,\n  guard: [],\n  useInBreadcrumbs: true\n}];\nexport default AppRoutes;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"LoginPage$React.lazy\");\n$RefreshReg$(_c2, \"LoginPage\");\n$RefreshReg$(_c3, \"PublishedFiles$React.lazy\");\n$RefreshReg$(_c4, \"PublishedFiles\");\n$RefreshReg$(_c5, \"SubmittedFiles$React.lazy\");\n$RefreshReg$(_c6, \"SubmittedFiles\");\n$RefreshReg$(_c7, \"PortalControls$React.lazy\");\n$RefreshReg$(_c8, \"PortalControls\");", "map": {"version": 3, "names": ["React", "LoginPage", "lazy", "_c", "_c2", "PublishedFiles", "_c3", "_c4", "SubmittedFiles", "_c5", "_c6", "PortalControls", "_c7", "_c8", "AppRoutes", "path", "title", "component", "exact", "guard", "useInBreadcrumbs", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/routes/index.ts"], "sourcesContent": ["import React from 'react';\r\nimport RouteConfigType from '../types/RouteConfigType';\r\nconst LoginPage = React.lazy(() => import('../pages/Login'));\r\nconst PublishedFiles = React.lazy(() => import('../pages/PublishedFiles'));\r\nconst SubmittedFiles = React.lazy(() => import('../pages/SubmittedFiles'));\r\nconst PortalControls = React.lazy(() => import('../pages/PortalControls'));\r\n\r\nconst AppRoutes: RouteConfigType[] = [\r\n  {\r\n    path: '/',\r\n    title: 'Login',\r\n    component: LoginPage,\r\n    exact: true,\r\n    guard: [],\r\n  },\r\n  {\r\n    path: '/publishedFiles',\r\n    title: 'Published Files',\r\n    component: PublishedFiles,\r\n    exact: true,\r\n    guard: [],\r\n    useInBreadcrumbs: true,\r\n  },\r\n  {\r\n    path: '/submittedFiles',\r\n    title: 'Submitted Files',\r\n    component: SubmittedFiles,\r\n    exact: true,\r\n    guard: [],\r\n    useInBreadcrumbs: true,\r\n  },\r\n    {\r\n    path: '/portalControls',\r\n    title: 'Portal Controls',\r\n    component: PortalControls, // To be Updated\r\n    exact: true,\r\n    guard: [],\r\n    useInBreadcrumbs: true,\r\n  },\r\n];\r\n\r\nexport default AppRoutes;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,SAAS,gBAAGD,KAAK,CAACE,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAACC,GAAA,GAAvDH,SAAS;AACf,MAAMI,cAAc,gBAAGL,KAAK,CAACE,IAAI,CAAAI,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,GAAA,GAArEF,cAAc;AACpB,MAAMG,cAAc,gBAAGR,KAAK,CAACE,IAAI,CAAAO,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,GAAA,GAArEF,cAAc;AACpB,MAAMG,cAAc,gBAAGX,KAAK,CAACE,IAAI,CAAAU,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,GAAA,GAArEF,cAAc;AAEpB,MAAMG,SAA4B,GAAG,CACnC;EACEC,IAAI,EAAE,GAAG;EACTC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAEhB,SAAS;EACpBiB,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAEZ,cAAc;EACzBa,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,EAAE;EACTC,gBAAgB,EAAE;AACpB,CAAC,EACD;EACEL,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAET,cAAc;EACzBU,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,EAAE;EACTC,gBAAgB,EAAE;AACpB,CAAC,EACC;EACAL,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAEN,cAAc;EAAE;EAC3BO,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,EAAE;EACTC,gBAAgB,EAAE;AACpB,CAAC,CACF;AAED,eAAeN,SAAS;AAAC,IAAAX,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA;AAAAQ,YAAA,CAAAlB,EAAA;AAAAkB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAR,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}