import { ControlOutlined, DeleteOutlined, DownloadOutlined, DownOutlined, SyncOutlined } from '@ant-design/icons';
import {
  useDownloadFileMutation,
  useDownloadPortalZipFileURIMutation,
  useDownloadZipFileMutation,
  useGetSubmittedFilesQuery,
  useLazyDownloadPortalFileURIQuery,
  useRemoveFilesMutation,
  useSaveFileDataMutation,
  useUploadFilesMutation,
  useSoftDeleteFilesMutation
} from '@app/api/fileApiSlice';
import CustomModal from '@app/components/CustomModal';
import DownloadModal from '@app/components/DownloadModal';
import { DownloadModalDownloadTypes } from '@app/components/DownloadModal/types';
import FileCard from '@app/components/FileCard/FileCard';
import FileUploader from '@app/components/FileUploader';
import { setPendingSave, setSucceededFiles } from '@app/components/FileUploader/uploaderSlice';
import InfinityList from '@app/components/InfinityList';
import SiteSelection from '@app/components/SiteSelection';
import { useAppDispatch, useAppSelector } from '@app/hooks/useAppSelector';
import useWindowDimensions from '@app/hooks/useWindowDimensions';
import { PageContent, PageTitle } from '@app/layouts/MasterLayout';
import { selectAppState } from '@app/appSlice';
import { FileType, SubmittedFilesType } from '@app/types/fileTypes';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import { errorNotification, infoNotification, successNotification } from '@app/utils/antNotifications';
import logger from '@app/utils/logger';
import { Button, Checkbox, Col, Divider, Dropdown, Menu, Row, Tooltip } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import FilterArea from './FilterArea';
import styles from './index.module.less';
import FileDetailsModal from '@app/components/FileUploader/FileDetailsModal';
import { FileRecord } from '@app/components/forms/UploaderSubmit/types';
import { validateTitle } from '@app/components/FileUploader/validators';
import { FileList } from '@app/components/FileUploader/types';
import confirmDiscard from '@app/components/FileUploader/utils/confirmDiscard';

const MIN_SCREEN_RESOLUTION = 1366;
const MIN_INFINITY_HEIGHT = 315;
const MAX_INFINITY_HEIGHT = 700;

const SubmittedFiles = (props: any) => {
  const [sortDir, setSortDir] = React.useState('desc');
  const [items, setItems] = React.useState<SubmittedFilesType[]>([]);
  const [selectedFiles, setSelectedFiles] = React.useState<Array<string>>([]);
  const [indeterminate, setIndeterminate] = React.useState(false);
  const [selectAll, setSelectAll] = React.useState(false);
  const [selectTotalFiles, setSelectTotalFiles] = React.useState(false);
  const [totalFileCount, setTotalFileCount] = React.useState(0);
  const { selectedSite: siteId } = useAppSelector(selectAppState);
  const [showDownloadModal, setShowDownloadModal] = React.useState(false);
  const [downloadType, setDownloadType] = React.useState<DownloadModalDownloadTypes | undefined>(DownloadModalDownloadTypes.individual);
  const { width } = useWindowDimensions();
  const [triggerDownloadPortalFileURI] = useLazyDownloadPortalFileURIQuery();
  const [triggerDownloadPortalZipFileURI] = useDownloadPortalZipFileURIMutation();
  const [triggerDownloadFile] = useDownloadFileMutation();
  const [triggerDownloadZipFile] = useDownloadZipFileMutation();
  const [triggerUploadFiles] = useUploadFilesMutation();
  const [triggerRemoveFiles] = useRemoveFilesMutation();
  const [triggerSoftDeleteFiles] = useSoftDeleteFilesMutation(); 
  const [triggerSaveFileData] = useSaveFileDataMutation();
  const [uploadedFiles, setUploadedFiles] = useState<FileList>({});
  const [pageOptions, setPageOptions] = useState<any>({
    limit: 10,
    offset: 0,
  });
  const { data: submittedFilesRecords, isFetching } = useGetSubmittedFilesQuery({
    siteId,
    options: pageOptions,
  });
  let navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    setSelectedFiles([]);
  }, []);

  
 
  useEffect(() => {
    if (items.length != 0) {
      setIndeterminate(selectedFiles.length > 0 && selectedFiles.length < items.length);
      setSelectAll(selectedFiles.length === items.length);
      if (selectedFiles.length !== items.length) setSelectTotalFiles(false);
    }
  }, [selectedFiles, totalFileCount, items]);

  const toggleSortDir = () => {
    setSortDir(sortDir === 'desc' ? 'asc' : 'desc');
  };

  const onSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setSelectedFiles(checked ? items.map((item: SubmittedFilesType) => item.id) : []);
  };

  const handleOnDownloadModalCancel = () => {
    setShowDownloadModal(false);
  };

  const displaydownloadModal = (downloadTypeInput: DownloadModalDownloadTypes, display: boolean) => {
    setDownloadType(downloadTypeInput);
    setShowDownloadModal(display);
  };

  const handleMenuClick = (e: any) => {
    if (e.key === '1') {
      if (selectedFiles.length === 1) {
        downloadAndSaveFile(selectedFiles[0]);
      } else {
        displaydownloadModal(DownloadModalDownloadTypes.individual, true);
      }
    } else {
      displaydownloadModal(DownloadModalDownloadTypes.zip, true);
    }
  };

  const menu: any = [
    {
      label: 'Download files',
      key: '1',
    },
  ];

  if (selectedFiles.length > 1) {
    menu.push({
      label: 'Download as a zip files',
      key: '2',
    });
  }

  const downloadMenu = <Menu onClick={handleMenuClick} items={menu} />;

  const downloadAndSaveFile = async (fileId: string, isMultiple?: boolean, isZip?: boolean, fileList?: FileType[]) => {
    if (isZip) {
      infoNotification([''], 'Zip is being downloaded.');
      const fileIdList = [] as any;
      fileList?.forEach((file) => {
        fileIdList.push(file.fileId);
      });

      if (fileIdList.length > 0) {
        try {
          let { payload, uri } = await triggerDownloadPortalZipFileURI({
            fileIds: fileIdList,
          }).unwrap();
          triggerDownloadZipFile({ files: payload, uri });
        } catch (error: any) {
          const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));

          if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
            navigate('/forbidden');
          } else {
            errorNotification([''], 'Download Failed');
          }

          logger.error('File Management Module', 'Download Component', errorObject);
        }
      }
      return;
    }
    if (!isMultiple) infoNotification([''], 'File is being downloaded.');
    try {
      let { data: URIData } = await triggerDownloadPortalFileURI({ fileId });
      if (URIData) triggerDownloadFile(URIData);
    } catch (error: any) {
      const errorObject = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(error) as any));

      if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
        navigate('/forbidden');
      } else {
        errorNotification([''], 'Download Failed');
      }

      logger.error('File Management Module', 'Download Component', errorObject);
    }
  };

  const cardContent = (file: SubmittedFilesType) => {
    return (
      <div>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Size:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.size}</div>
          </Col>
        </Row>
        <Row>
          <Col span={4}>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Uploaded at:</div>
          </Col>
          <Col>
            <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.uploadedAt).format('YYYY-MM-DD hh:mmA')}</div>
          </Col>
        </Row>
        {file.status.value === 2 && (
          <>
            <Row>
              <Col span={4}>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Accepted at:</div>
              </Col>
              <Col>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.acceptedAt).format('YYYY-MM-DD hh:mmA')}</div>
              </Col>
            </Row>
            <Row>
              <Col span={4}>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Accepted by:</div>
              </Col>
              <Col>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.acceptedBy?.name}</div>
              </Col>
            </Row>
          </>
        )}
        {file.status.value === 3 && (
          <>
            <Row>
              <Col span={4}>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deleted at:</div>
              </Col>
              <Col>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{moment(file.deletedAt).format('YYYY-MM-DD hh:mmA')}</div>
              </Col>
            </Row>
            <Row>
              <Col span={4}>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deleted by:</div>
              </Col>
              <Col>
                <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.deletedBy?.name}</div>
              </Col>
            </Row>
            {file.deletionNote && (
              <Row>
                <Col span={4}>
                  <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_TITLE}>Deletion Note:</div>
                </Col>
                <Col>
                  <div className={styles.YJ_CP_PUBLISHEDFILES_CARDCONTENT_VALUE}>{file.deletionNote}</div>
                </Col>
              </Row>
            )}
          </>
        )}
      </div>
    );
  };

  const generateCards = (file: SubmittedFilesType) => {
    return (
      <FileCard
        fileId={file.id}
        fileType={file.type}
        title={file.title}
        isSelected={selectedFiles.includes(file.id)}
        className={'yj_cp_submitted_fileCard_' + file.status.value}
        content={cardContent(file)}
        note={file.status.value === 3 ? file.deletionNote && 'Deletion Note: ' + file.deletionNote : undefined}
        trailingIcons={
          <Row gutter={10} justify="space-between">
            <Col span={12}>
              <div className={styles['yj_cp_submitted_fileCard_fileStatus_' + file.status.value]}>
                <p className={styles['yj_cp_submitted_fileCard_fileStatus_dot_' + file.status.value]}></p>
                {file.status.name}
              </div>
            </Col>
            <Col span={12}>
              {file.status.value === 1 && (
                <Row gutter={10} justify="end">
                  <Col>
                    <DownloadOutlined
                      className={styles.YJ_CP_FileCard_DownloadIcon}
                      onClick={(event) => {
                        downloadAndSaveFile(file.id);
                        event.stopPropagation();
                      }}
                    />
                  </Col>
                  {/* <Col>
                    <SyncOutlined
                      className={styles.YJ_CP_FileCard_RefreshIcon}
                      onClick={(event) => {
                        event.stopPropagation();
                      }}
                    />
                  </Col> */}
                  <Col>
                    <DeleteOutlined
                      className={styles.YJ_CP_FileCard_DeleteIcon}
                      onClick={(event) => {
                        softDeleteFile(file.id)
                        event.stopPropagation();
                      }}
                    />
                  </Col>
                </Row>
              )}
            </Col>
          </Row>
        }
        onSelectChange={(isSelected: boolean) => {
          
          if (isSelected) {
            setSelectedFiles([...selectedFiles, file.id]);
          } else {
            setSelectedFiles(selectedFiles.filter((id) => id !== file.id));
          }
          
          
        }}
      />
    );
  };

  const mapToFileList = (fileList: FileList): FileRecord[] => {
    return Object.entries(fileList).map(([_uid, info]) => {
      return {
        referenceNumber: info.referenceNumber || '',
        title: info.name.slice(0, info.name.lastIndexOf('.')),
        checked: true,
        error: validateTitle(info.name),
      };
    });
  };

  const removeFiles = () => {
    
    const chunckedReferencesreferences = Object.entries(uploadedFiles).map(([_, info]) => info.referenceNumber);
    triggerRemoveFiles({ siteId, referenceNumber: chunckedReferencesreferences, removeChunks: true });
    setUploadedFiles({});
  };

  const handleSoftDeleteFiles = () => {
    bulkSoftDeleteFiles(selectedFiles)
  }

  const bulkSoftDeleteFiles = async (fileIds: any[]) => {
    
    triggerSoftDeleteFiles({ siteId, fileId: fileIds });
  };

  const softDeleteFile = async (fileId?: string) => {
   
    const fileIdList = [] as any;
    fileIdList.push(fileId);
    triggerSoftDeleteFiles({ siteId, fileId: fileIdList });
  };

  const removeFile = (uid: string) => {
    
    triggerRemoveFiles({
      siteId,
      referenceNumber: [uid],
      removeChunks: true,
    });
    setUploadedFiles((files) => {
      const id = Object.entries(files).filter(([_, info]) => info.referenceNumber === uid)[0];
      delete files[id[0]];
      return files;
    });
  };

  return (
    <>
      <PageTitle title={props.title}>
        <SiteSelection />
      </PageTitle>
      <PageContent>
        <FileUploader
          siteId={siteId}
          onUpload={(fileData) => {
            const formData = new FormData();
            formData.append('file', fileData.file, fileData.fileName);
            formData.append('siteid', fileData.siteId);
            fileData.referenceNo && formData.append('ReferenceNumber', fileData.referenceNo);
            formData.append('chunkId', fileData.chunkId.toString());
            formData.append('totalChunkCount', fileData.totalChunkCount.toString());
            return triggerUploadFiles({ formData, onUploadProgress: fileData.onUploadProgress, cancelToken: fileData.cancelToken });
          }}
          onRemove={(fileData) => triggerRemoveFiles(fileData)}
          onUploadComplete={(files) => {
            setUploadedFiles(files);
          }}
        />
        {Object.keys(uploadedFiles).length > 0 && (
          <FileDetailsModal
            siteId={siteId}
            files={mapToFileList(uploadedFiles)}
            onClose={() => confirmDiscard(() => removeFiles())}
            onRemove={removeFile}
            onSaveComplete={(successedFiles) => {
              successedFiles.forEach((file) => {
                const [uid] = Object.entries(uploadedFiles).find(([_fileUid, info]) => {
                  return info.referenceNumber === file.referenceNumber;
                })!;
                setUploadedFiles(({ [uid]: _value, ...rest }: FileList) => {
                  return { ...rest };
                });
              });
            }}
            onFinished={(data: any) => {
              try {
                dispatch(setPendingSave(true));
                setPageOptions({ limit: 10, offset: 0 });
                triggerSaveFileData(data).then(() => {
                  successNotification([''], 'File(s) uploaded successfully.');
                  setTimeout(() => {
                    dispatch(setSucceededFiles(data.files || []));
                    dispatch(setPendingSave(false));
                  }, 500);
                });
              } catch (error: any) {
                if (error.statusCode === FORBIDDEN_ERROR_CODE) {
                  errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');
                } else {
                  errorNotification([''], 'Upload Failed.');
                }
                dispatch(setPendingSave(false));
              }
            }}
          />
        )}
        <FilterArea
          sortBy={sortDir}
          onSeachByChange={(value) => console.log(value)}
          onSortChange={toggleSortDir}
          onSearch={(value) => console.log(value)}
          onSortByChange={(value) => console.log(value)}
          onStatusChange={(value) => console.log(value)}
          onCategoryChange={(value) => console.log(value)}
        />
        <Divider />
        <Row style={{ marginTop: '-15px', padding: '0 0 10px 18px' }} justify="space-between">
          <Col>
            <Checkbox checked={selectAll} indeterminate={indeterminate} onChange={(e) => onSelectAll(e.target.checked)} />
            {(() => {
              if (selectedFiles.length > 0)
                return (
                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_SELECTED_COUNT}>
                    {(() => (selectTotalFiles ? totalFileCount : selectedFiles.length))()}
                    {' out of ' + totalFileCount + ' files selected'}
                  </div>
                );
              else
                return (
                  <div style={{ display: 'inline', paddingLeft: '10px' }} className={styles.YJ_CP_NONE_SELECTED}>
                    None selected
                  </div>
                );
            })()}{' '}
            {selectedFiles.length > 0 && items.length < totalFileCount && (
              <a
                className={styles.YJ_CP_SELECT_TOTAL_FILES}
                type="link"
                onClick={() => {
                  onSelectAll(true);
                  setSelectTotalFiles(true);
                }}
              >
                {'Select all ' + totalFileCount + ' files'}
              </a>
            )}
          </Col>
          <Col>
            <Row gutter={5}>
              <Col>
                <Dropdown
                  trigger={['click']}
                  overlay={downloadMenu}
                  disabled={(!selectAll && !selectTotalFiles && selectedFiles.length === 0) || !selectedFiles.every((e) => items.find((item) => item.id === e)?.status.value === 1)}
                  className={styles.yj_cp_download_btn}
                >
                  <Button icon={<DownloadOutlined />} >
                    DOWNLOAD <DownOutlined />
                  </Button>
                </Dropdown>
              </Col>
              <Col>
                <Button type="default" onClick={handleSoftDeleteFiles}
                disabled={(!selectAll && !selectTotalFiles && selectedFiles.length === 0) || !selectedFiles.every((e) => items.find((item) => item.id === e)?.status.value === 1)}
                className={styles.yj_cp_download_btn} icon={<DeleteOutlined />}>
                  Delete
                </Button>
              </Col>
              {/* <Col>
                <Button type="default" disabled={true} icon={<ControlOutlined />}>
                  Manage Categories
                </Button>
              </Col> */}
            </Row>
          </Col>
        </Row>
        <div style={{ width: '100%' }}>
          {siteId && (
            <InfinityList
              setPaginations={(page: number, searchValue?: string | undefined) => {
                setPageOptions({
                  limit: 10,
                  offset: page - 1,
                  search: searchValue,
                });
              }}
              heightInPx={(() => {
                if (width <= MIN_SCREEN_RESOLUTION) return MIN_INFINITY_HEIGHT;
                else return MAX_INFINITY_HEIGHT;
              })()}
              onTotalCount={(count) => setTotalFileCount(count)}
              key="YJ-CP-SubmittedFiles"
              paginatedLimit={20}
              idKeyValue="fileId"
              formatValue={(value: any) => generateCards(value)}
              notFoundContent={'No records available.'}
              grid={{ column: 1, gutter: 0 }}
              onRecordsChange={(records) => setItems(records)}
              data={submittedFilesRecords}
              isLoading={isFetching}
            />
          )}
        </div>
      </PageContent>
      {/*  Download Option Menu Modal */}
      <CustomModal
        visible={showDownloadModal}
        title={'Download File(s)'}
        size={'small'}
        onCancel={handleOnDownloadModalCancel}
        footer={[
          <Button onClick={handleOnDownloadModalCancel} key="submit" type="primary">
            Done
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <DownloadModal
            downloadAndSaveFile={downloadAndSaveFile}
            hasDownloaded={(hasDownloaded: boolean) => {
              if (hasDownloaded) {
                setShowDownloadModal(false);
              }
            }}
            selectedFiles={items
              .filter((item) => selectedFiles.includes(item.id))
              .map(({ id: fileId, title, type }) => ({
                fileId,
                title,
                type,
              }))}
            downloadType={downloadType}
          />
        </div>
      </CustomModal>
    </>
  );
};

export default SubmittedFiles;
