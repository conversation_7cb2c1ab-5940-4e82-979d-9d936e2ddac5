import React from 'react';
import { Card } from '@progress/kendo-react-layout';
import { <PERSON>a<PERSON>ser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';
import styles from './index.module.less';

interface PortalControlItem {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
}

const PortalControls = () => {
  const controlItems: PortalControlItem[] = [
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage portal users and assign portal roles',
      icon: FaUser,
      onClick: () => {
        // TODO: Navigate to user management
        console.log('Navigate to User Management');
      }
    },
    {
      id: 'security-groups',
      title: 'Security Groups',
      description: 'Create and manage permission-based user groups for file access',
      icon: FaLock,
      onClick: () => {
        // TODO: Navigate to security groups
        console.log('Navigate to Security Groups');
      }
    },
    {
      id: 'permissions',
      title: 'Permissions',
      description: 'Set security options for security groups',
      icon: FaCog,
      onClick: () => {
        // TODO: Navigate to permissions
        console.log('Navigate to Permissions');
      }
    }
  ];

  const renderControlCard = (item: PortalControlItem) => {
    const IconComponent = item.icon;

    return (
      <Card
        key={item.id}
        className={styles.portalControlCard}
        onClick={item.onClick}
      >
        <div className={styles.cardContent}>
          <div className={styles.iconSection}>
            <IconComponent className={styles.cardIcon} />
          </div>
          <div className={styles.textSection}>
            <h3 className={styles.cardTitle}>{item.title}</h3>
            <p className={styles.cardDescription}>{item.description}</p>
          </div>
          <div className={styles.arrowSection}>
            <FaChevronRight className={styles.arrowIcon} />
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={styles.portalControlsContainer}>
      {controlItems.map(renderControlCard)}
    </div>
  );
};

export default PortalControls;
