import React, { Fragment, useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { useNavigate } from 'react-router-dom';
import constants from '@app/utils/Constants';

const Login = () => {
  let navigate = useNavigate();
  const { oktaAuth, authState } = useOktaAuth();

  useEffect(() => {
    if (authState?.isAuthenticated) navigate(constants.postSigninRedirect);
    else oktaAuth.signInWithRedirect();
  }, [authState, oktaAuth]);

  return <Fragment></Fragment>;
};

export default Login;
