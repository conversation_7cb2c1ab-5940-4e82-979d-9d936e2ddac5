import Modal from '@app/components/CustomModal';
import useFileList from '@app/components/FileUploader/hooks/useFileList';
import { FileEvents, FileRecord } from '@app/components/forms/UploaderSubmit/types';
import UploaderSubmitContainer from '@app/components/forms/UploaderSubmit/UploaderSubmitContainer';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { FileDetailsModalProps } from '../types';
import confirmDiscard from '../utils/confirmDiscard';

const FileDetailsModal: React.FC<FileDetailsModalProps> = ({ siteId, files, onClose, onRemove, onSaveComplete, onFinished, onLastFile = () => null }) => {
  const [form] = Form.useForm();
  const { fileList, actions } = useFileList({ files });
  const { changeTitle, changeSelection, changeSelectAll, removeFile } = actions;
  const [isFileTableValid, setIsFileTableValid] = useState(false);

  const onTitleUpdate = (value: string, index: number) => {
    changeTitle(value, index);
  };

  const onFileSelect = (value: boolean, index: number) => {
    changeSelection(value, index);
  };

  const onFileSelectAll = (value: boolean) => {
    changeSelectAll(value);
  };

  const onFileRemove = (referenceNumber: string) => {
    confirmDiscard(() => {
      deleteFile(referenceNumber);
    });
  };

  const onSaveSuccess = (inputFileList: FileRecord[]) => {
    onSaveComplete?.(inputFileList);
  };

  const deleteFile = (referenceNumber: string) => {
    onRemove?.(referenceNumber);
    removeFile(referenceNumber);
  };

  const fileEvents: FileEvents = {
    onTitleUpdate,
    onFileSelect,
    onFileSelectAll,
    onFileRemove,
    onSaveSuccess,
  };

  useEffect(() => {
    if (fileList?.length === 0) {
      onLastFile?.();
    }
    const checkedLength = fileList?.filter((v) => v.checked)?.length;
    const validFileTable = !!checkedLength && !fileList?.filter((v) => v.checked && (!v.title.trim() || !!v.error)).length;
    setIsFileTableValid(validFileTable);
  }, [fileList]);

  return (
    <>
      <Modal
        size={'medium'}
        style={{ top: 20 }}
        title={'Upload File(s)'}
        open={!!fileList?.length}
        destroyOnClose={true}
        okText="UPLOAD"
        onOk={() => form.submit()}
        onCancel={onClose}
        okButtonProps={{ disabled: !isFileTableValid }}
        
      >
        <UploaderSubmitContainer siteId={siteId} fileList={fileList} form={form} fileEvents={fileEvents} onFinished={onFinished} />
      </Modal>
    </>
  );
};

export default FileDetailsModal;
