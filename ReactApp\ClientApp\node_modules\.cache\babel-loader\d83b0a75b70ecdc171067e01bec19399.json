{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport d from \"prop-types\";\nimport { useDir as l, getTabIndex as i, classNames as n } from \"@progress/kendo-react-common\";\nconst m = t.forwardRef((e, s) => {\n    const r = t.useRef(null),\n      a = t.useRef(null);\n    return t.useImperativeHandle(r, () => ({\n      element: a.current,\n      props: e\n    })), t.useImperativeHandle(s, () => r.current), /* @__PURE__ */t.createElement(t.Fragment, null, e.rootItem ? /* @__PURE__ */t.createElement(\"ol\", {\n      id: e.id,\n      ref: a,\n      style: e.style,\n      dir: l(a, e.dir),\n      tabIndex: i(e.tabIndex, e.disabled),\n      className: n(\"k-breadcrumb-root-item-container\", {\n        \"k-disabled\": e.disabled\n      }, e.className)\n    }, e.children) : /* @__PURE__ */t.createElement(\"ol\", {\n      id: e.id,\n      ref: a,\n      style: e.style,\n      dir: l(a, e.dir),\n      tabIndex: i(e.tabIndex, e.disabled),\n      className: n(\"k-breadcrumb-container !k-flex-wrap\", {\n        \"k-disabled\": e.disabled\n      }, e.className)\n    }, e.children));\n  }),\n  c = {\n    id: d.string,\n    className: d.string,\n    children: d.element,\n    tabIndex: d.number,\n    style: d.object,\n    dir: d.string,\n    disabled: d.bool\n  };\nm.displayName = \"KendoReactBreadcrumbOrderedList\";\nm.propTypes = c;\nexport { m as BreadcrumbOrderedList };", "map": {"version": 3, "names": ["t", "d", "useDir", "l", "getTabIndex", "i", "classNames", "n", "m", "forwardRef", "e", "s", "r", "useRef", "a", "useImperativeHandle", "element", "current", "props", "createElement", "Fragment", "rootItem", "id", "ref", "style", "dir", "tabIndex", "disabled", "className", "children", "c", "string", "number", "object", "bool", "displayName", "propTypes", "BreadcrumbOrderedList"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/breadcrumb/BreadcrumbOrderedList.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport d from \"prop-types\";\nimport { useDir as l, getTabIndex as i, classNames as n } from \"@progress/kendo-react-common\";\nconst m = t.forwardRef(\n  (e, s) => {\n    const r = t.useRef(null), a = t.useRef(null);\n    return t.useImperativeHandle(r, () => ({\n      element: a.current,\n      props: e\n    })), t.useImperativeHandle(\n      s,\n      () => r.current\n    ), /* @__PURE__ */ t.createElement(t.Fragment, null, e.rootItem ? /* @__PURE__ */ t.createElement(\n      \"ol\",\n      {\n        id: e.id,\n        ref: a,\n        style: e.style,\n        dir: l(a, e.dir),\n        tabIndex: i(e.tabIndex, e.disabled),\n        className: n(\n          \"k-breadcrumb-root-item-container\",\n          {\n            \"k-disabled\": e.disabled\n          },\n          e.className\n        )\n      },\n      e.children\n    ) : /* @__PURE__ */ t.createElement(\n      \"ol\",\n      {\n        id: e.id,\n        ref: a,\n        style: e.style,\n        dir: l(a, e.dir),\n        tabIndex: i(e.tabIndex, e.disabled),\n        className: n(\n          \"k-breadcrumb-container !k-flex-wrap\",\n          {\n            \"k-disabled\": e.disabled\n          },\n          e.className\n        )\n      },\n      e.children\n    ));\n  }\n), c = {\n  id: d.string,\n  className: d.string,\n  children: d.element,\n  tabIndex: d.number,\n  style: d.object,\n  dir: d.string,\n  disabled: d.bool\n};\nm.displayName = \"KendoReactBreadcrumbOrderedList\";\nm.propTypes = c;\nexport {\n  m as BreadcrumbOrderedList\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,MAAM,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC7F,MAAMC,CAAC,GAAGR,CAAC,CAACS,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK;IACR,MAAMC,CAAC,GAAGZ,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGd,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC;IAC5C,OAAOb,CAAC,CAACe,mBAAmB,CAACH,CAAC,EAAE,OAAO;MACrCI,OAAO,EAAEF,CAAC,CAACG,OAAO;MAClBC,KAAK,EAAER;IACT,CAAC,CAAC,CAAC,EAAEV,CAAC,CAACe,mBAAmB,CACxBJ,CAAC,EACD,MAAMC,CAAC,CAACK,OACV,CAAC,EAAE,eAAgBjB,CAAC,CAACmB,aAAa,CAACnB,CAAC,CAACoB,QAAQ,EAAE,IAAI,EAAEV,CAAC,CAACW,QAAQ,GAAG,eAAgBrB,CAAC,CAACmB,aAAa,CAC/F,IAAI,EACJ;MACEG,EAAE,EAAEZ,CAAC,CAACY,EAAE;MACRC,GAAG,EAAET,CAAC;MACNU,KAAK,EAAEd,CAAC,CAACc,KAAK;MACdC,GAAG,EAAEtB,CAAC,CAACW,CAAC,EAAEJ,CAAC,CAACe,GAAG,CAAC;MAChBC,QAAQ,EAAErB,CAAC,CAACK,CAAC,CAACgB,QAAQ,EAAEhB,CAAC,CAACiB,QAAQ,CAAC;MACnCC,SAAS,EAAErB,CAAC,CACV,kCAAkC,EAClC;QACE,YAAY,EAAEG,CAAC,CAACiB;MAClB,CAAC,EACDjB,CAAC,CAACkB,SACJ;IACF,CAAC,EACDlB,CAAC,CAACmB,QACJ,CAAC,GAAG,eAAgB7B,CAAC,CAACmB,aAAa,CACjC,IAAI,EACJ;MACEG,EAAE,EAAEZ,CAAC,CAACY,EAAE;MACRC,GAAG,EAAET,CAAC;MACNU,KAAK,EAAEd,CAAC,CAACc,KAAK;MACdC,GAAG,EAAEtB,CAAC,CAACW,CAAC,EAAEJ,CAAC,CAACe,GAAG,CAAC;MAChBC,QAAQ,EAAErB,CAAC,CAACK,CAAC,CAACgB,QAAQ,EAAEhB,CAAC,CAACiB,QAAQ,CAAC;MACnCC,SAAS,EAAErB,CAAC,CACV,qCAAqC,EACrC;QACE,YAAY,EAAEG,CAAC,CAACiB;MAClB,CAAC,EACDjB,CAAC,CAACkB,SACJ;IACF,CAAC,EACDlB,CAAC,CAACmB,QACJ,CAAC,CAAC;EACJ,CACF,CAAC;EAAEC,CAAC,GAAG;IACLR,EAAE,EAAErB,CAAC,CAAC8B,MAAM;IACZH,SAAS,EAAE3B,CAAC,CAAC8B,MAAM;IACnBF,QAAQ,EAAE5B,CAAC,CAACe,OAAO;IACnBU,QAAQ,EAAEzB,CAAC,CAAC+B,MAAM;IAClBR,KAAK,EAAEvB,CAAC,CAACgC,MAAM;IACfR,GAAG,EAAExB,CAAC,CAAC8B,MAAM;IACbJ,QAAQ,EAAE1B,CAAC,CAACiC;EACd,CAAC;AACD1B,CAAC,CAAC2B,WAAW,GAAG,iCAAiC;AACjD3B,CAAC,CAAC4B,SAAS,GAAGN,CAAC;AACf,SACEtB,CAAC,IAAI6B,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}