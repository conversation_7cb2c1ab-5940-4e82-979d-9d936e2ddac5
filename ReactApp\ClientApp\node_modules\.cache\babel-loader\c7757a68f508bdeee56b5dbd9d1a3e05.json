{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nclass e {\n  constructor() {\n    this.phase = \"Initialized\";\n  }\n  getIsDirectionRightToLeft() {\n    return this.isDirectionRightToLeft;\n  }\n  setIsDirectionRightToLeft(i) {\n    this.phase = this.phase === \"NotInitialized\" ? \"Initialized\" : \"NewValueReceived\", this.previousIsDirectionRightToLeft = this.isDirectionRightToLeft, this.isDirectionRightToLeft = i;\n  }\n  hasDirectionChanged() {\n    return this.phase === \"NewValueReceived\" ? this.previousIsDirectionRightToLeft !== this.isDirectionRightToLeft : !1;\n  }\n}\nexport { e as DirectionHolder };", "map": {"version": 3, "names": ["e", "constructor", "phase", "getIsDirectionRightToLeft", "isDirectionRightToLeft", "setIsDirectionRightToLeft", "i", "previousIsDirectionRightToLeft", "hasDirectionChanged", "DirectionHolder"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/DirectionHolder.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nclass e {\n  constructor() {\n    this.phase = \"Initialized\";\n  }\n  getIsDirectionRightToLeft() {\n    return this.isDirectionRightToLeft;\n  }\n  setIsDirectionRightToLeft(i) {\n    this.phase = this.phase === \"NotInitialized\" ? \"Initialized\" : \"NewValueReceived\", this.previousIsDirectionRightToLeft = this.isDirectionRightToLeft, this.isDirectionRightToLeft = i;\n  }\n  hasDirectionChanged() {\n    return this.phase === \"NewValueReceived\" ? this.previousIsDirectionRightToLeft !== this.isDirectionRightToLeft : !1;\n  }\n}\nexport {\n  e as DirectionHolder\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,CAAC;EACNC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,aAAa;EAC5B;EACAC,yBAAyBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACC,sBAAsB;EACpC;EACAC,yBAAyBA,CAACC,CAAC,EAAE;IAC3B,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACA,KAAK,KAAK,gBAAgB,GAAG,aAAa,GAAG,kBAAkB,EAAE,IAAI,CAACK,8BAA8B,GAAG,IAAI,CAACH,sBAAsB,EAAE,IAAI,CAACA,sBAAsB,GAAGE,CAAC;EACvL;EACAE,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACN,KAAK,KAAK,kBAAkB,GAAG,IAAI,CAACK,8BAA8B,KAAK,IAAI,CAACH,sBAAsB,GAAG,CAAC,CAAC;EACrH;AACF;AACA,SACEJ,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}