import { DownloadOutlined } from '@ant-design/icons/lib';
import { FileType } from '@app/types/fileTypes';
import { infoNotification } from '@app/utils/antNotifications';
import { Button, List } from 'antd';
import React, { Fragment, useCallback, useEffect, useState } from 'react';
import styles from './index.module.less';
import { DownloadModalDownloadTypes, DownloadModalProps } from './types';

const DownloadModal = ({ downloadType = DownloadModalDownloadTypes.individual, selectedFiles, downloadAndSaveFile }: DownloadModalProps) => {
  const DOWNLOAD_COUNTER_TIME = 10;
  const DOWNLOAD_TIMEOUT = 1000;
  const [timeLeft, setTimeLeft] = useState(DOWNLOAD_COUNTER_TIME);

  useEffect(() => {
    if (!timeLeft) {
      return () => {};
    }
    const intervalId = setInterval(() => {
      setTimeLeft(timeLeft - 1);
    }, DOWNLOAD_TIMEOUT);
    return () => clearInterval(intervalId);
  }, [timeLeft]);

  const bulkDownload = (fileList: FileType[] | undefined) => {
    infoNotification([''], 'Files are being downloaded.');
    fileList?.forEach((file) => {
      downloadAndSaveFile(file.fileId, true);
    });
  };

  const triggerFileDownload = useCallback(
    (timeOutdownload: number) => {
      if (selectedFiles && selectedFiles.length > 0) {
        downloadType === DownloadModalDownloadTypes.individual
          ? setTimeout(() => {
              bulkDownload(selectedFiles);
            }, timeOutdownload)
          : setTimeout(() => {
              downloadAndSaveFile('', false, true, selectedFiles);
            }, timeOutdownload);
      }
    },
    [downloadType, selectedFiles]
  );

  useEffect(() => {
    const timeOutdownload = 0;
    triggerFileDownload(timeOutdownload);
  }, []);

  return (
    <div className={styles.yjModalContentWrapper}>
      <Fragment>
        {downloadType === DownloadModalDownloadTypes.individual ? (
          <>
            If your download didn't start in <span className={styles.yjDigit}>{timeLeft}</span> seconds click below
            <List
              itemLayout="horizontal"
              dataSource={selectedFiles}
              renderItem={(item) =>
                item && (
                  <List.Item className={styles.yjDownloadItem}>
                    <List.Item.Meta title={<p className={styles.yjDownloadFileName}>{`${item.title}.${item.type ? item.type.toLowerCase() : ''}`}</p>} />
                    <div className={styles.yj_cp_downloadBtn}>
                      <Button
                        onClick={() => {
                          downloadAndSaveFile(item.fileId);
                        }}
                        type="default"
                      >
                        <DownloadOutlined /> Download
                      </Button>
                    </div>
                  </List.Item>
                )
              }
            />
          </>
        ) : (
          <>
            If your download didn't start in <span className={styles.yjDigit}>{timeLeft}</span> seconds click below
            <List.Item className={styles.yjDownloadItem}>
              <List.Item.Meta title={<p className={styles.yjDownloadFileName}>{selectedFiles ? `${selectedFiles[selectedFiles.length - 1]?.title}.zip` : ''}</p>} />
              <div className={styles.yj_cp_downloadBtn}>
                <Button
                  onClick={() => {
                    downloadAndSaveFile('', false, true, selectedFiles);
                  }}
                  type="default"
                >
                  <DownloadOutlined /> Download
                </Button>
              </div>
            </List.Item>
          </>
        )}
      </Fragment>
    </div>
  );
};
export default DownloadModal;
