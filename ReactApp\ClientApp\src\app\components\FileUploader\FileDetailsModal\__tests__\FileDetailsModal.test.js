import Modal from "@app/components/CustomModal";
import UploaderSubmitContainer from "@app/components/forms/UploaderSubmit/UploaderSubmitContainer";
import { shallow } from "enzyme";
import React from "react";
import FileDetailsModal from "..";

describe("<FileDetailsModal />", () => {
  it("should render component", () => {
    const component = shallow(<FileDetailsModal files={[]} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render with props", () => {
    const component = shallow(<FileDetailsModal uploadType={1} siteId={"S-001"} files={[]} onClose={() => null} onRemove={() => null} onSaveComplete={() => null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render common Modal", () => {
    const component = shallow(<FileDetailsModal files={[]} />);
    expect(component.find(Modal)).not.toBe(null);
  });

  it("should have title: Upload File(s)", () => {
    const component = shallow(<FileDetailsModal files={[]} />);
    expect(component.find(Modal).prop("title")).toEqual("Upload File(s)");
  });

  it("should have OK text: DONE", () => {
    const component = shallow(<FileDetailsModal files={[]} />);
    expect(component.find(Modal).prop("okText")).toEqual("DONE");
  });

  it("should pass the given onCancel function", () => {
    const onCancel = () => null;
    const component = shallow(<FileDetailsModal files={[]} onClose={onCancel} />);
    expect(component.find(Modal).prop("onCancel")).toEqual(onCancel);
  });

  it("should render UploaderSubmitContainer", () => {
    const component = shallow(<FileDetailsModal files={[]} />);
    expect(component.find(Modal).find(UploaderSubmitContainer)).not.toBe(null);
  });
});
