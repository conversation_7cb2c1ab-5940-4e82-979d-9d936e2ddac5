{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport t from \"prop-types\";\nimport { IconWrap as c } from \"@progress/kendo-react-common\";\nimport { chevronLeftIcon as o, chevronRightIcon as d } from \"@progress/kendo-svg-icons\";\nconst m = r.forwardRef((e, l) => {\n    const n = r.useRef(null),\n      i = r.useRef(null);\n    return r.useImperativeHandle(n, () => {\n      var a;\n      return {\n        element: ((a = i.current) == null ? void 0 : a.element) || null,\n        props: e\n      };\n    }), r.useImperativeHandle(l, () => n.current), /* @__PURE__ */r.createElement(c, {\n      ref: i,\n      id: e.id,\n      \"aria-hidden\": !0,\n      tabIndex: e.tabIndex,\n      style: e.style,\n      name: e.dir === \"rtl\" ? \"chevron-left\" : \"chevron-right\",\n      icon: e.dir === \"rtl\" ? o : d,\n      className: \"k-breadcrumb-delimiter-icon\",\n      size: \"xsmall\"\n    });\n  }),\n  s = {\n    id: t.string,\n    className: t.string,\n    style: t.object,\n    tabIndex: t.number,\n    dir: t.string\n  };\nm.displayName = \"KendoReactBreadcrumbDelimiter\";\nm.propTypes = s;\nexport { m as BreadcrumbDelimiter };", "map": {"version": 3, "names": ["r", "t", "IconWrap", "c", "chevronLeftIcon", "o", "chevronRightIcon", "d", "m", "forwardRef", "e", "l", "n", "useRef", "i", "useImperativeHandle", "a", "element", "current", "props", "createElement", "ref", "id", "tabIndex", "style", "name", "dir", "icon", "className", "size", "s", "string", "object", "number", "displayName", "propTypes", "BreadcrumbDelimiter"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/breadcrumb/BreadcrumbDelimiter.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as r from \"react\";\nimport t from \"prop-types\";\nimport { IconWrap as c } from \"@progress/kendo-react-common\";\nimport { chevronLeftIcon as o, chevronRightIcon as d } from \"@progress/kendo-svg-icons\";\nconst m = r.forwardRef(\n  (e, l) => {\n    const n = r.useRef(null), i = r.useRef(null);\n    return r.useImperativeHandle(n, () => {\n      var a;\n      return {\n        element: ((a = i.current) == null ? void 0 : a.element) || null,\n        props: e\n      };\n    }), r.useImperativeHandle(\n      l,\n      () => n.current\n    ), /* @__PURE__ */ r.createElement(\n      c,\n      {\n        ref: i,\n        id: e.id,\n        \"aria-hidden\": !0,\n        tabIndex: e.tabIndex,\n        style: e.style,\n        name: e.dir === \"rtl\" ? \"chevron-left\" : \"chevron-right\",\n        icon: e.dir === \"rtl\" ? o : d,\n        className: \"k-breadcrumb-delimiter-icon\",\n        size: \"xsmall\"\n      }\n    );\n  }\n), s = {\n  id: t.string,\n  className: t.string,\n  style: t.object,\n  tabIndex: t.number,\n  dir: t.string\n};\nm.displayName = \"KendoReactBreadcrumbDelimiter\";\nm.propTypes = s;\nexport {\n  m as BreadcrumbDelimiter\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,QAAQ,IAAIC,CAAC,QAAQ,8BAA8B;AAC5D,SAASC,eAAe,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,2BAA2B;AACvF,MAAMC,CAAC,GAAGR,CAAC,CAACS,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK;IACR,MAAMC,CAAC,GAAGZ,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGd,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC;IAC5C,OAAOb,CAAC,CAACe,mBAAmB,CAACH,CAAC,EAAE,MAAM;MACpC,IAAII,CAAC;MACL,OAAO;QACLC,OAAO,EAAE,CAAC,CAACD,CAAC,GAAGF,CAAC,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,CAAC,CAACC,OAAO,KAAK,IAAI;QAC/DE,KAAK,EAAET;MACT,CAAC;IACH,CAAC,CAAC,EAAEV,CAAC,CAACe,mBAAmB,CACvBJ,CAAC,EACD,MAAMC,CAAC,CAACM,OACV,CAAC,EAAE,eAAgBlB,CAAC,CAACoB,aAAa,CAChCjB,CAAC,EACD;MACEkB,GAAG,EAAEP,CAAC;MACNQ,EAAE,EAAEZ,CAAC,CAACY,EAAE;MACR,aAAa,EAAE,CAAC,CAAC;MACjBC,QAAQ,EAAEb,CAAC,CAACa,QAAQ;MACpBC,KAAK,EAAEd,CAAC,CAACc,KAAK;MACdC,IAAI,EAAEf,CAAC,CAACgB,GAAG,KAAK,KAAK,GAAG,cAAc,GAAG,eAAe;MACxDC,IAAI,EAAEjB,CAAC,CAACgB,GAAG,KAAK,KAAK,GAAGrB,CAAC,GAAGE,CAAC;MAC7BqB,SAAS,EAAE,6BAA6B;MACxCC,IAAI,EAAE;IACR,CACF,CAAC;EACH,CACF,CAAC;EAAEC,CAAC,GAAG;IACLR,EAAE,EAAErB,CAAC,CAAC8B,MAAM;IACZH,SAAS,EAAE3B,CAAC,CAAC8B,MAAM;IACnBP,KAAK,EAAEvB,CAAC,CAAC+B,MAAM;IACfT,QAAQ,EAAEtB,CAAC,CAACgC,MAAM;IAClBP,GAAG,EAAEzB,CAAC,CAAC8B;EACT,CAAC;AACDvB,CAAC,CAAC0B,WAAW,GAAG,+BAA+B;AAC/C1B,CAAC,CAAC2B,SAAS,GAAGL,CAAC;AACf,SACEtB,CAAC,IAAI4B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}