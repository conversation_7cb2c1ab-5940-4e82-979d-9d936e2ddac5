{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst t = {\n    vertical: \"top\",\n    horizontal: \"left\"\n  },\n  n = {\n    vertical: \"top\",\n    horizontal: \"left\"\n  },\n  l = {\n    vertical: \"top\",\n    horizontal: \"right\"\n  },\n  r = {\n    vertical: \"top\",\n    horizontal: \"right\"\n  },\n  o = {\n    vertical: \"flip\",\n    horizontal: \"fit\"\n  },\n  i = {\n    vertical: \"fit\",\n    horizontal: \"flip\"\n  },\n  a = {\n    downward: {\n      anchorAlign: {\n        vertical: \"bottom\",\n        horizontal: \"right\"\n      },\n      popupAlign: l,\n      collision: o,\n      animationDirection: \"down\"\n    },\n    leftward: {\n      anchorAlign: {\n        vertical: \"top\",\n        horizontal: \"left\"\n      },\n      popupAlign: r,\n      collision: i,\n      animationDirection: \"left\"\n    }\n  },\n  c = {\n    downward: {\n      anchorAlign: {\n        vertical: \"bottom\",\n        horizontal: \"left\"\n      },\n      popupAlign: t,\n      collision: o,\n      animationDirection: \"down\"\n    },\n    rightward: {\n      anchorAlign: {\n        vertical: \"top\",\n        horizontal: \"right\"\n      },\n      popupAlign: n,\n      collision: i,\n      animationDirection: \"right\"\n    }\n  };\nexport { c as POPUP_SETTINGS, a as POPUP_SETTINGS_RTL };", "map": {"version": 3, "names": ["t", "vertical", "horizontal", "n", "l", "r", "o", "i", "a", "downward", "anchorAlign", "popupAlign", "collision", "animationDirection", "leftward", "c", "rightward", "POPUP_SETTINGS", "POPUP_SETTINGS_RTL"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/consts.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nconst t = {\n  vertical: \"top\",\n  horizontal: \"left\"\n}, n = {\n  vertical: \"top\",\n  horizontal: \"left\"\n}, l = {\n  vertical: \"top\",\n  horizontal: \"right\"\n}, r = {\n  vertical: \"top\",\n  horizontal: \"right\"\n}, o = {\n  vertical: \"flip\",\n  horizontal: \"fit\"\n}, i = {\n  vertical: \"fit\",\n  horizontal: \"flip\"\n}, a = {\n  downward: {\n    anchorAlign: {\n      vertical: \"bottom\",\n      horizontal: \"right\"\n    },\n    popupAlign: l,\n    collision: o,\n    animationDirection: \"down\"\n  },\n  leftward: {\n    anchorAlign: {\n      vertical: \"top\",\n      horizontal: \"left\"\n    },\n    popupAlign: r,\n    collision: i,\n    animationDirection: \"left\"\n  }\n}, c = {\n  downward: {\n    anchorAlign: {\n      vertical: \"bottom\",\n      horizontal: \"left\"\n    },\n    popupAlign: t,\n    collision: o,\n    animationDirection: \"down\"\n  },\n  rightward: {\n    anchorAlign: {\n      vertical: \"top\",\n      horizontal: \"right\"\n    },\n    popupAlign: n,\n    collision: i,\n    animationDirection: \"right\"\n  }\n};\nexport {\n  c as POPUP_SETTINGS,\n  a as POPUP_SETTINGS_RTL\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,CAAC,GAAG;IACRC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC;EAAEC,CAAC,GAAG;IACLF,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC;EAAEE,CAAC,GAAG;IACLH,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC;EAAEG,CAAC,GAAG;IACLJ,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC;EAAEI,CAAC,GAAG;IACLL,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;EACd,CAAC;EAAEK,CAAC,GAAG;IACLN,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC;EAAEM,CAAC,GAAG;IACLC,QAAQ,EAAE;MACRC,WAAW,EAAE;QACXT,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAC;MACDS,UAAU,EAAEP,CAAC;MACbQ,SAAS,EAAEN,CAAC;MACZO,kBAAkB,EAAE;IACtB,CAAC;IACDC,QAAQ,EAAE;MACRJ,WAAW,EAAE;QACXT,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAC;MACDS,UAAU,EAAEN,CAAC;MACbO,SAAS,EAAEL,CAAC;MACZM,kBAAkB,EAAE;IACtB;EACF,CAAC;EAAEE,CAAC,GAAG;IACLN,QAAQ,EAAE;MACRC,WAAW,EAAE;QACXT,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAC;MACDS,UAAU,EAAEX,CAAC;MACbY,SAAS,EAAEN,CAAC;MACZO,kBAAkB,EAAE;IACtB,CAAC;IACDG,SAAS,EAAE;MACTN,WAAW,EAAE;QACXT,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAC;MACDS,UAAU,EAAER,CAAC;MACbS,SAAS,EAAEL,CAAC;MACZM,kBAAkB,EAAE;IACtB;EACF,CAAC;AACD,SACEE,CAAC,IAAIE,cAAc,EACnBT,CAAC,IAAIU,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}