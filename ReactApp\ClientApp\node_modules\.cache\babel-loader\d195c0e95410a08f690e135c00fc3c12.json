{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport r from \"prop-types\";\nimport { validatePackage as H, useRtl as K, classNames as f, getTabIndex as O, WatermarkOverlay as V } from \"@progress/kendo-react-common\";\nimport { calculateRatio as q } from \"../common/utils.mjs\";\nimport { packageMetadata as D } from \"../package-metadata.mjs\";\nconst v = e.forwardRef((t, y) => {\n  const h = !H(D, {\n      component: \"ChunkProgressBar\"\n    }),\n    {\n      chunkCount: C = a.chunkCount,\n      className: x,\n      disabled: c = a.disabled,\n      orientation: N,\n      min: i = a.min,\n      max: u = a.max,\n      reverse: P = a.reverse,\n      style: E,\n      tabIndex: S,\n      emptyStyle: R,\n      emptyClassName: w,\n      progressStyle: I,\n      progressClassName: B\n    } = t,\n    l = e.useRef(null),\n    L = e.useCallback(() => {\n      l.current && l.current.focus();\n    }, []);\n  e.useImperativeHandle(y, () => ({\n    element: l.current,\n    focus: L\n  }));\n  const d = t.value || a.value,\n    k = t.value === null,\n    T = K(l, t.dir),\n    o = N === \"vertical\",\n    j = {\n      className: f(\"k-progressbar k-chunk-progressbar\", {\n        \"k-progressbar-horizontal\": !o,\n        \"k-progressbar-vertical\": o,\n        \"k-progressbar-reverse\": P,\n        \"k-progressbar-indeterminate\": k,\n        \"k-disabled\": c\n      }, x),\n      ref: l,\n      dir: T,\n      tabIndex: O(S, c),\n      role: \"progressbar\",\n      \"aria-label\": t.ariaLabel,\n      \"aria-valuemin\": i,\n      \"aria-valuemax\": u,\n      \"aria-valuenow\": k ? void 0 : d,\n      \"aria-disabled\": c,\n      style: E\n    },\n    z = _ref => {\n      let {\n        count: n\n      } = _ref;\n      const p = [],\n        b = 100 / n + \"%\",\n        F = q(i, u, d),\n        M = Math.floor(F * n),\n        g = Array(n).fill(!1);\n      for (let s = 0; s < M; s++) g[s] = !0;\n      for (let s = 0; s < n; ++s) {\n        const m = g[s],\n          W = m ? B : w,\n          A = {\n            ...{\n              width: o ? void 0 : b,\n              height: o ? b : void 0\n            },\n            ...(m ? I : R)\n          };\n        p.push( /* @__PURE__ */e.createElement(\"li\", {\n          key: s,\n          className: f(\"k-progressbar-chunk\", {\n            \"k-first\": s === 0,\n            \"k-last\": s === n - 1,\n            \"k-selected\": m\n          }, W),\n          style: A\n        }));\n      }\n      return /* @__PURE__ */e.createElement(e.Fragment, null, p);\n    };\n  return /* @__PURE__ */e.createElement(e.Fragment, null, /* @__PURE__ */e.createElement(\"div\", {\n    ...j\n  }, /* @__PURE__ */e.createElement(\"ul\", {\n    className: \"k-progressbar-chunks k-reset\"\n  }, /* @__PURE__ */e.createElement(z, {\n    count: C\n  }))), h && /* @__PURE__ */e.createElement(V, null));\n});\nv.propTypes = {\n  chunkCount: r.number,\n  ariaLabel: r.string,\n  disabled: r.bool,\n  reverse: r.bool,\n  max: r.number,\n  min: r.number,\n  value: r.number,\n  tabIndex: r.number,\n  emptyStyle: r.object,\n  emptyClassName: r.string,\n  progressStyle: r.object,\n  progressClassName: r.string\n};\nconst a = {\n  chunkCount: 5,\n  min: 0,\n  max: 100,\n  value: 0,\n  disabled: !1,\n  reverse: !1\n};\nv.displayName = \"KendoChunkProgressBar\";\nexport { v as ChunkProgressBar };", "map": {"version": 3, "names": ["e", "r", "validatePackage", "H", "useRtl", "K", "classNames", "f", "getTabIndex", "O", "WatermarkOverlay", "V", "calculateRatio", "q", "packageMetadata", "D", "v", "forwardRef", "t", "y", "h", "component", "chunkCount", "C", "a", "className", "x", "disabled", "c", "orientation", "N", "min", "i", "max", "u", "reverse", "P", "style", "E", "tabIndex", "S", "emptyStyle", "R", "emptyClassName", "w", "progressStyle", "I", "progressClassName", "B", "l", "useRef", "L", "useCallback", "current", "focus", "useImperativeHandle", "element", "d", "value", "k", "T", "dir", "o", "j", "ref", "role", "aria<PERSON><PERSON><PERSON>", "z", "_ref", "count", "n", "p", "b", "F", "M", "Math", "floor", "g", "Array", "fill", "s", "m", "W", "A", "width", "height", "push", "createElement", "key", "Fragment", "propTypes", "number", "string", "bool", "object", "displayName", "ChunkProgressBar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-progressbars/chunkprogressbar/ChunkProgressBar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport r from \"prop-types\";\nimport { validatePackage as H, useRtl as K, classNames as f, getTabIndex as O, WatermarkOverlay as V } from \"@progress/kendo-react-common\";\nimport { calculateRatio as q } from \"../common/utils.mjs\";\nimport { packageMetadata as D } from \"../package-metadata.mjs\";\nconst v = e.forwardRef(\n  (t, y) => {\n    const h = !H(D, { component: \"ChunkProgressBar\" }), {\n      chunkCount: C = a.chunkCount,\n      className: x,\n      disabled: c = a.disabled,\n      orientation: N,\n      min: i = a.min,\n      max: u = a.max,\n      reverse: P = a.reverse,\n      style: E,\n      tabIndex: S,\n      emptyStyle: R,\n      emptyClassName: w,\n      progressStyle: I,\n      progressClassName: B\n    } = t, l = e.useRef(null), L = e.useCallback(() => {\n      l.current && l.current.focus();\n    }, []);\n    e.useImperativeHandle(\n      y,\n      () => ({\n        element: l.current,\n        focus: L\n      })\n    );\n    const d = t.value || a.value, k = t.value === null, T = K(l, t.dir), o = N === \"vertical\", j = {\n      className: f(\n        \"k-progressbar k-chunk-progressbar\",\n        {\n          \"k-progressbar-horizontal\": !o,\n          \"k-progressbar-vertical\": o,\n          \"k-progressbar-reverse\": P,\n          \"k-progressbar-indeterminate\": k,\n          \"k-disabled\": c\n        },\n        x\n      ),\n      ref: l,\n      dir: T,\n      tabIndex: O(S, c),\n      role: \"progressbar\",\n      \"aria-label\": t.ariaLabel,\n      \"aria-valuemin\": i,\n      \"aria-valuemax\": u,\n      \"aria-valuenow\": k ? void 0 : d,\n      \"aria-disabled\": c,\n      style: E\n    }, z = ({ count: n }) => {\n      const p = [], b = 100 / n + \"%\", F = q(i, u, d), M = Math.floor(F * n), g = Array(n).fill(!1);\n      for (let s = 0; s < M; s++)\n        g[s] = !0;\n      for (let s = 0; s < n; ++s) {\n        const m = g[s], W = m ? B : w, A = { ...{\n          width: o ? void 0 : b,\n          height: o ? b : void 0\n        }, ...m ? I : R };\n        p.push(\n          /* @__PURE__ */ e.createElement(\n            \"li\",\n            {\n              key: s,\n              className: f(\n                \"k-progressbar-chunk\",\n                {\n                  \"k-first\": s === 0,\n                  \"k-last\": s === n - 1,\n                  \"k-selected\": m\n                },\n                W\n              ),\n              style: A\n            }\n          )\n        );\n      }\n      return /* @__PURE__ */ e.createElement(e.Fragment, null, p);\n    };\n    return /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"div\", { ...j }, /* @__PURE__ */ e.createElement(\"ul\", { className: \"k-progressbar-chunks k-reset\" }, /* @__PURE__ */ e.createElement(z, { count: C }))), h && /* @__PURE__ */ e.createElement(V, null));\n  }\n);\nv.propTypes = {\n  chunkCount: r.number,\n  ariaLabel: r.string,\n  disabled: r.bool,\n  reverse: r.bool,\n  max: r.number,\n  min: r.number,\n  value: r.number,\n  tabIndex: r.number,\n  emptyStyle: r.object,\n  emptyClassName: r.string,\n  progressStyle: r.object,\n  progressClassName: r.string\n};\nconst a = {\n  chunkCount: 5,\n  min: 0,\n  max: 100,\n  value: 0,\n  disabled: !1,\n  reverse: !1\n};\nv.displayName = \"KendoChunkProgressBar\";\nexport {\n  v as ChunkProgressBar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,eAAe,IAAIC,CAAC,EAAEC,MAAM,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,8BAA8B;AAC1I,SAASC,cAAc,IAAIC,CAAC,QAAQ,qBAAqB;AACzD,SAASC,eAAe,IAAIC,CAAC,QAAQ,yBAAyB;AAC9D,MAAMC,CAAC,GAAGhB,CAAC,CAACiB,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK;EACR,MAAMC,CAAC,GAAG,CAACjB,CAAC,CAACY,CAAC,EAAE;MAAEM,SAAS,EAAE;IAAmB,CAAC,CAAC;IAAE;MAClDC,UAAU,EAAEC,CAAC,GAAGC,CAAC,CAACF,UAAU;MAC5BG,SAAS,EAAEC,CAAC;MACZC,QAAQ,EAAEC,CAAC,GAAGJ,CAAC,CAACG,QAAQ;MACxBE,WAAW,EAAEC,CAAC;MACdC,GAAG,EAAEC,CAAC,GAAGR,CAAC,CAACO,GAAG;MACdE,GAAG,EAAEC,CAAC,GAAGV,CAAC,CAACS,GAAG;MACdE,OAAO,EAAEC,CAAC,GAAGZ,CAAC,CAACW,OAAO;MACtBE,KAAK,EAAEC,CAAC;MACRC,QAAQ,EAAEC,CAAC;MACXC,UAAU,EAAEC,CAAC;MACbC,cAAc,EAAEC,CAAC;MACjBC,aAAa,EAAEC,CAAC;MAChBC,iBAAiB,EAAEC;IACrB,CAAC,GAAG9B,CAAC;IAAE+B,CAAC,GAAGjD,CAAC,CAACkD,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGnD,CAAC,CAACoD,WAAW,CAAC,MAAM;MACjDH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACNtD,CAAC,CAACuD,mBAAmB,CACnBpC,CAAC,EACD,OAAO;IACLqC,OAAO,EAAEP,CAAC,CAACI,OAAO;IAClBC,KAAK,EAAEH;EACT,CAAC,CACH,CAAC;EACD,MAAMM,CAAC,GAAGvC,CAAC,CAACwC,KAAK,IAAIlC,CAAC,CAACkC,KAAK;IAAEC,CAAC,GAAGzC,CAAC,CAACwC,KAAK,KAAK,IAAI;IAAEE,CAAC,GAAGvD,CAAC,CAAC4C,CAAC,EAAE/B,CAAC,CAAC2C,GAAG,CAAC;IAAEC,CAAC,GAAGhC,CAAC,KAAK,UAAU;IAAEiC,CAAC,GAAG;MAC7FtC,SAAS,EAAElB,CAAC,CACV,mCAAmC,EACnC;QACE,0BAA0B,EAAE,CAACuD,CAAC;QAC9B,wBAAwB,EAAEA,CAAC;QAC3B,uBAAuB,EAAE1B,CAAC;QAC1B,6BAA6B,EAAEuB,CAAC;QAChC,YAAY,EAAE/B;MAChB,CAAC,EACDF,CACF,CAAC;MACDsC,GAAG,EAAEf,CAAC;MACNY,GAAG,EAAED,CAAC;MACNrB,QAAQ,EAAE9B,CAAC,CAAC+B,CAAC,EAAEZ,CAAC,CAAC;MACjBqC,IAAI,EAAE,aAAa;MACnB,YAAY,EAAE/C,CAAC,CAACgD,SAAS;MACzB,eAAe,EAAElC,CAAC;MAClB,eAAe,EAAEE,CAAC;MAClB,eAAe,EAAEyB,CAAC,GAAG,KAAK,CAAC,GAAGF,CAAC;MAC/B,eAAe,EAAE7B,CAAC;MAClBS,KAAK,EAAEC;IACT,CAAC;IAAE6B,CAAC,GAAGC,IAAA,IAAkB;MAAA,IAAjB;QAAEC,KAAK,EAAEC;MAAE,CAAC,GAAAF,IAAA;MAClB,MAAMG,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,GAAG,GAAGF,CAAC,GAAG,GAAG;QAAEG,CAAC,GAAG5D,CAAC,CAACmB,CAAC,EAAEE,CAAC,EAAEuB,CAAC,CAAC;QAAEiB,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,GAAGH,CAAC,CAAC;QAAEO,CAAC,GAAGC,KAAK,CAACR,CAAC,CAAC,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7F,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,CAAC,EAAEM,CAAC,EAAE,EACxBH,CAAC,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;MACX,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,CAAC,EAAE,EAAEU,CAAC,EAAE;QAC1B,MAAMC,CAAC,GAAGJ,CAAC,CAACG,CAAC,CAAC;UAAEE,CAAC,GAAGD,CAAC,GAAGjC,CAAC,GAAGJ,CAAC;UAAEuC,CAAC,GAAG;YAAE,GAAG;cACtCC,KAAK,EAAEtB,CAAC,GAAG,KAAK,CAAC,GAAGU,CAAC;cACrBa,MAAM,EAAEvB,CAAC,GAAGU,CAAC,GAAG,KAAK;YACvB,CAAC;YAAE,IAAGS,CAAC,GAAGnC,CAAC,GAAGJ,CAAC;UAAC,CAAC;QACjB6B,CAAC,CAACe,IAAI,EACJ,eAAgBtF,CAAC,CAACuF,aAAa,CAC7B,IAAI,EACJ;UACEC,GAAG,EAAER,CAAC;UACNvD,SAAS,EAAElB,CAAC,CACV,qBAAqB,EACrB;YACE,SAAS,EAAEyE,CAAC,KAAK,CAAC;YAClB,QAAQ,EAAEA,CAAC,KAAKV,CAAC,GAAG,CAAC;YACrB,YAAY,EAAEW;UAChB,CAAC,EACDC,CACF,CAAC;UACD7C,KAAK,EAAE8C;QACT,CACF,CACF,CAAC;MACH;MACA,OAAO,eAAgBnF,CAAC,CAACuF,aAAa,CAACvF,CAAC,CAACyF,QAAQ,EAAE,IAAI,EAAElB,CAAC,CAAC;IAC7D,CAAC;EACD,OAAO,eAAgBvE,CAAC,CAACuF,aAAa,CAACvF,CAAC,CAACyF,QAAQ,EAAE,IAAI,EAAE,eAAgBzF,CAAC,CAACuF,aAAa,CAAC,KAAK,EAAE;IAAE,GAAGxB;EAAE,CAAC,EAAE,eAAgB/D,CAAC,CAACuF,aAAa,CAAC,IAAI,EAAE;IAAE9D,SAAS,EAAE;EAA+B,CAAC,EAAE,eAAgBzB,CAAC,CAACuF,aAAa,CAACpB,CAAC,EAAE;IAAEE,KAAK,EAAE9C;EAAE,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC,IAAI,eAAgBpB,CAAC,CAACuF,aAAa,CAAC5E,CAAC,EAAE,IAAI,CAAC,CAAC;AACnS,CACF,CAAC;AACDK,CAAC,CAAC0E,SAAS,GAAG;EACZpE,UAAU,EAAErB,CAAC,CAAC0F,MAAM;EACpBzB,SAAS,EAAEjE,CAAC,CAAC2F,MAAM;EACnBjE,QAAQ,EAAE1B,CAAC,CAAC4F,IAAI;EAChB1D,OAAO,EAAElC,CAAC,CAAC4F,IAAI;EACf5D,GAAG,EAAEhC,CAAC,CAAC0F,MAAM;EACb5D,GAAG,EAAE9B,CAAC,CAAC0F,MAAM;EACbjC,KAAK,EAAEzD,CAAC,CAAC0F,MAAM;EACfpD,QAAQ,EAAEtC,CAAC,CAAC0F,MAAM;EAClBlD,UAAU,EAAExC,CAAC,CAAC6F,MAAM;EACpBnD,cAAc,EAAE1C,CAAC,CAAC2F,MAAM;EACxB/C,aAAa,EAAE5C,CAAC,CAAC6F,MAAM;EACvB/C,iBAAiB,EAAE9C,CAAC,CAAC2F;AACvB,CAAC;AACD,MAAMpE,CAAC,GAAG;EACRF,UAAU,EAAE,CAAC;EACbS,GAAG,EAAE,CAAC;EACNE,GAAG,EAAE,GAAG;EACRyB,KAAK,EAAE,CAAC;EACR/B,QAAQ,EAAE,CAAC,CAAC;EACZQ,OAAO,EAAE,CAAC;AACZ,CAAC;AACDnB,CAAC,CAAC+E,WAAW,GAAG,uBAAuB;AACvC,SACE/E,CAAC,IAAIgF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}