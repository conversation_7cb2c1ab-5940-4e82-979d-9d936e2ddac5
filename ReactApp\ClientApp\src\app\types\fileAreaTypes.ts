export type FileAreaSettings = {
  urlFileUpload: boolean;
  internalFileStatusUpdate: boolean;
  internalFilesEmail: boolean;
  internalFileSetAssignee: boolean;
  internalFileSetProject: boolean;
  internalFilesDownload: boolean;
  internalFilesCheckinCheckout: boolean;
  internalFilesRecategorize: boolean;
  fileAreaFolderStructure: boolean;
  fileAreaManageEmailSubscription: boolean;
  fileAreaManageTags: boolean;
  internalFilesAssign: boolean;
  internalFilesCopy: boolean;
  internalFilesDelete: boolean;
  internalFilesHistory: boolean;
  internalFilesMove: boolean;
  internalFilesRename: boolean;
  internalFilesUpdate: boolean;
  internalFilesUpload: boolean;
  internalFilesView: boolean;
  internalFilesViewAssignmentHistory: boolean;
  internalFilesViewCheckoutHistory: boolean;
  internalFilesViewProperties: boolean;
  internalFilesViewVersionHistory: boolean;
  portalFilesView: boolean;
  internalFilesPublishUnPublish: boolean;
};

export type PortalFile = {
  requestId: string | null;
  securityKey: string | null;
};
