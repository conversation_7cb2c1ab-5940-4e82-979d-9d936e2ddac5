{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as a from \"react\";\nimport r from \"prop-types\";\nimport { classNames as s, kendoThemeMaps as o } from \"@progress/kendo-react-common\";\nimport { avatarType as t } from \"./interfaces/Enums.mjs\";\nconst i = l => {\n  const e = {\n    type: t.TEXT,\n    size: \"medium\",\n    rounded: \"full\",\n    fillMode: \"solid\",\n    themeColor: \"primary\",\n    ...l\n  };\n  return /* @__PURE__ */a.createElement(\"div\", {\n    style: e.style,\n    className: s(\"k-avatar\", {\n      \"k-avatar-bordered\": e.border,\n      [`k-rounded-${o.roundedMap[e.rounded] || e.rounded}`]: e.rounded,\n      [`k-avatar-${o.sizeMap[e.size] || e.size}`]: e.size,\n      [`k-avatar-${e.fillMode}`]: e.fillMode,\n      [`k-avatar-${e.fillMode}-${e.themeColor}`]: !!(e.fillMode && e.themeColor)\n    }, e.className)\n  }, /* @__PURE__ */a.createElement(\"span\", {\n    className: `k-avatar-${e.type}`\n  }, e.children));\n};\ni.propTypes = {\n  className: r.string,\n  type: r.oneOf([\"text\", \"image\", \"icon\"]),\n  size: r.oneOf([null, \"small\", \"medium\", \"large\"]),\n  rounded: r.oneOf([null, \"small\", \"medium\", \"large\", \"full\"]),\n  fillMode: r.oneOf([null, \"solid\", \"outline\"]),\n  /* eslint-disable max-len */\n  themeColor: r.oneOf([null, \"base\", \"dark\", \"error\", \"info\", \"inverse\", \"inverse\", \"light\", \"primary\", \"secondary\", \"success\", \"tertiary\", \"warning\"])\n};\nexport { i as Avatar };", "map": {"version": 3, "names": ["a", "r", "classNames", "s", "kendoThemeMaps", "o", "avatarType", "t", "i", "l", "e", "type", "TEXT", "size", "rounded", "fillMode", "themeColor", "createElement", "style", "className", "border", "roundedMap", "sizeMap", "children", "propTypes", "string", "oneOf", "Avatar"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/card/Avatar.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as a from \"react\";\nimport r from \"prop-types\";\nimport { classNames as s, kendoThemeMaps as o } from \"@progress/kendo-react-common\";\nimport { avatarType as t } from \"./interfaces/Enums.mjs\";\nconst i = (l) => {\n  const e = {\n    type: t.TEXT,\n    size: \"medium\",\n    rounded: \"full\",\n    fillMode: \"solid\",\n    themeColor: \"primary\",\n    ...l\n  };\n  return /* @__PURE__ */ a.createElement(\n    \"div\",\n    {\n      style: e.style,\n      className: s(\n        \"k-avatar\",\n        {\n          \"k-avatar-bordered\": e.border,\n          [`k-rounded-${o.roundedMap[e.rounded] || e.rounded}`]: e.rounded,\n          [`k-avatar-${o.sizeMap[e.size] || e.size}`]: e.size,\n          [`k-avatar-${e.fillMode}`]: e.fillMode,\n          [`k-avatar-${e.fillMode}-${e.themeColor}`]: !!(e.fillMode && e.themeColor)\n        },\n        e.className\n      )\n    },\n    /* @__PURE__ */ a.createElement(\"span\", { className: `k-avatar-${e.type}` }, e.children)\n  );\n};\ni.propTypes = {\n  className: r.string,\n  type: r.oneOf([\"text\", \"image\", \"icon\"]),\n  size: r.oneOf([null, \"small\", \"medium\", \"large\"]),\n  rounded: r.oneOf([null, \"small\", \"medium\", \"large\", \"full\"]),\n  fillMode: r.oneOf([null, \"solid\", \"outline\"]),\n  /* eslint-disable max-len */\n  themeColor: r.oneOf([\n    null,\n    \"base\",\n    \"dark\",\n    \"error\",\n    \"info\",\n    \"inverse\",\n    \"inverse\",\n    \"light\",\n    \"primary\",\n    \"secondary\",\n    \"success\",\n    \"tertiary\",\n    \"warning\"\n  ])\n};\nexport {\n  i as Avatar\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,QAAQ,8BAA8B;AACnF,SAASC,UAAU,IAAIC,CAAC,QAAQ,wBAAwB;AACxD,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAG;IACRC,IAAI,EAAEJ,CAAC,CAACK,IAAI;IACZC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,OAAO;IACjBC,UAAU,EAAE,SAAS;IACrB,GAAGP;EACL,CAAC;EACD,OAAO,eAAgBT,CAAC,CAACiB,aAAa,CACpC,KAAK,EACL;IACEC,KAAK,EAAER,CAAC,CAACQ,KAAK;IACdC,SAAS,EAAEhB,CAAC,CACV,UAAU,EACV;MACE,mBAAmB,EAAEO,CAAC,CAACU,MAAM;MAC7B,CAAC,aAAaf,CAAC,CAACgB,UAAU,CAACX,CAAC,CAACI,OAAO,CAAC,IAAIJ,CAAC,CAACI,OAAO,EAAE,GAAGJ,CAAC,CAACI,OAAO;MAChE,CAAC,YAAYT,CAAC,CAACiB,OAAO,CAACZ,CAAC,CAACG,IAAI,CAAC,IAAIH,CAAC,CAACG,IAAI,EAAE,GAAGH,CAAC,CAACG,IAAI;MACnD,CAAC,YAAYH,CAAC,CAACK,QAAQ,EAAE,GAAGL,CAAC,CAACK,QAAQ;MACtC,CAAC,YAAYL,CAAC,CAACK,QAAQ,IAAIL,CAAC,CAACM,UAAU,EAAE,GAAG,CAAC,EAAEN,CAAC,CAACK,QAAQ,IAAIL,CAAC,CAACM,UAAU;IAC3E,CAAC,EACDN,CAAC,CAACS,SACJ;EACF,CAAC,EACD,eAAgBnB,CAAC,CAACiB,aAAa,CAAC,MAAM,EAAE;IAAEE,SAAS,EAAE,YAAYT,CAAC,CAACC,IAAI;EAAG,CAAC,EAAED,CAAC,CAACa,QAAQ,CACzF,CAAC;AACH,CAAC;AACDf,CAAC,CAACgB,SAAS,GAAG;EACZL,SAAS,EAAElB,CAAC,CAACwB,MAAM;EACnBd,IAAI,EAAEV,CAAC,CAACyB,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EACxCb,IAAI,EAAEZ,CAAC,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EACjDZ,OAAO,EAAEb,CAAC,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC5DX,QAAQ,EAAEd,CAAC,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EAC7C;EACAV,UAAU,EAAEf,CAAC,CAACyB,KAAK,CAAC,CAClB,IAAI,EACJ,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACV,SAAS,CACV;AACH,CAAC;AACD,SACElB,CAAC,IAAImB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}