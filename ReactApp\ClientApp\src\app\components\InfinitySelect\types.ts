export type InfinitySelectGetOptions = 'fetch' | 'load';

export type InfinitySelectProps = {
  getPaginatedRecords: (page: number, method: InfinitySelectGetOptions, searchValue?: string) => Promise<any>;
  setVisibility: (isVisible: boolean) => void;
  onChange: (value: any, selectedValues?: Array<any>) => void;
  onLoaded?: (isLoaded: boolean) => void;
  mode?: 'multiple' | 'tags';
  notFoundContent?: string;
  notLoadContent?: string;
  value?: any;
  placeholder?: string;
  isDelete?: boolean;
  isDefault?: boolean;
  queryParameters?: {};
  excludeList?: Array<string>;
  preSelected?: Array<any>;
  defaultValues?: any;
  removeById?: any;
  maxTagCount?: any;
  maxTagPlaceHolder?: (value: any) => void;
  disabled?: boolean;
  returnObject?: boolean;
  newlyAddedValue?: any;
  isFetching?: boolean;
};
