{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as D, svgIconPropType as P, IconWrap as T } from \"@progress/kendo-react-common\";\nconst f = e.forwardRef((m, g) => {\n    const i = e.useRef(null);\n    e.useImperativeHandle(g, () => ({\n      element: i.current\n    }));\n    const {\n        className: r,\n        style: x,\n        selected: a,\n        disabled: n,\n        item: k,\n        render: y,\n        dataItem: N,\n        icon: d,\n        svgIcon: b,\n        text: p,\n        id: C,\n        onSelect: s,\n        onKeyDown: l,\n        index: o,\n        tabIndex: E = h.tabIndex\n      } = m,\n      R = e.useMemo(() => D(\"k-bottom-nav-item\", {\n        \"k-selected\": a,\n        \"k-disabled\": n\n      }, r), [a, n, r]),\n      w = e.useCallback(c => {\n        s && o !== void 0 && !n && s(c, o);\n      }, [s, o, n]),\n      K = e.useCallback(c => {\n        l && o !== void 0 && !n && l(c, o);\n      }, [l, o, n]),\n      I = y,\n      v = k,\n      u = /* @__PURE__ */e.createElement(\"span\", {\n        ref: i,\n        className: R,\n        style: x,\n        role: \"link\",\n        id: C,\n        tabIndex: E,\n        onClick: w,\n        onKeyDown: K,\n        \"aria-current\": a,\n        \"aria-disabled\": n\n      }, v ? /* @__PURE__ */e.createElement(v, {\n        itemIndex: o,\n        item: N\n      }) : /* @__PURE__ */e.createElement(e.Fragment, null, (d || b) && /* @__PURE__ */e.createElement(T, {\n        className: \"k-bottom-nav-item-icon\",\n        name: d,\n        icon: b,\n        size: \"xlarge\"\n      }), p && /* @__PURE__ */e.createElement(\"span\", {\n        className: \"k-bottom-nav-item-text\",\n        style: {\n          userSelect: \"none\"\n        }\n      }, p)));\n    return I !== void 0 ? I.call(void 0, u, m) : u;\n  }),\n  h = {\n    tabIndex: 0\n  };\nf.propTypes = {\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  disabled: t.bool,\n  selected: t.bool,\n  icon: t.string,\n  svgIcon: P,\n  text: t.string,\n  tabIndex: t.number\n};\nf.displayName = \"KendoReactBottomNavigationItem\";\nexport { f as BottomNavigationItem };", "map": {"version": 3, "names": ["e", "t", "classNames", "D", "svgIconPropType", "P", "IconWrap", "T", "f", "forwardRef", "m", "g", "i", "useRef", "useImperativeHandle", "element", "current", "className", "r", "style", "x", "selected", "a", "disabled", "n", "item", "k", "render", "y", "dataItem", "N", "icon", "d", "svgIcon", "b", "text", "p", "id", "C", "onSelect", "s", "onKeyDown", "l", "index", "o", "tabIndex", "E", "h", "R", "useMemo", "w", "useCallback", "c", "K", "I", "v", "u", "createElement", "ref", "role", "onClick", "itemIndex", "Fragment", "name", "size", "userSelect", "call", "propTypes", "string", "object", "bool", "number", "displayName", "BottomNavigationItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/bottomnavigation/BottomNavigationItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as e from \"react\";\nimport t from \"prop-types\";\nimport { classNames as D, svgIconPropType as P, IconWrap as T } from \"@progress/kendo-react-common\";\nconst f = e.forwardRef(\n  (m, g) => {\n    const i = e.useRef(null);\n    e.useImperativeHandle(\n      g,\n      () => ({\n        element: i.current\n      })\n    );\n    const {\n      className: r,\n      style: x,\n      selected: a,\n      disabled: n,\n      item: k,\n      render: y,\n      dataItem: N,\n      icon: d,\n      svgIcon: b,\n      text: p,\n      id: C,\n      onSelect: s,\n      onKeyDown: l,\n      index: o,\n      tabIndex: E = h.tabIndex\n    } = m, R = e.useMemo(\n      () => D(\n        \"k-bottom-nav-item\",\n        {\n          \"k-selected\": a,\n          \"k-disabled\": n\n        },\n        r\n      ),\n      [a, n, r]\n    ), w = e.useCallback(\n      (c) => {\n        s && o !== void 0 && !n && s(c, o);\n      },\n      [s, o, n]\n    ), K = e.useCallback(\n      (c) => {\n        l && o !== void 0 && !n && l(c, o);\n      },\n      [l, o, n]\n    ), I = y, v = k, u = /* @__PURE__ */ e.createElement(\n      \"span\",\n      {\n        ref: i,\n        className: R,\n        style: x,\n        role: \"link\",\n        id: C,\n        tabIndex: E,\n        onClick: w,\n        onKeyDown: K,\n        \"aria-current\": a,\n        \"aria-disabled\": n\n      },\n      v ? /* @__PURE__ */ e.createElement(v, { itemIndex: o, item: N }) : /* @__PURE__ */ e.createElement(e.Fragment, null, (d || b) && /* @__PURE__ */ e.createElement(T, { className: \"k-bottom-nav-item-icon\", name: d, icon: b, size: \"xlarge\" }), p && /* @__PURE__ */ e.createElement(\"span\", { className: \"k-bottom-nav-item-text\", style: { userSelect: \"none\" } }, p))\n    );\n    return I !== void 0 ? I.call(void 0, u, m) : u;\n  }\n), h = {\n  tabIndex: 0\n};\nf.propTypes = {\n  className: t.string,\n  style: t.object,\n  id: t.string,\n  disabled: t.bool,\n  selected: t.bool,\n  icon: t.string,\n  svgIcon: P,\n  text: t.string,\n  tabIndex: t.number\n};\nf.displayName = \"KendoReactBottomNavigationItem\";\nexport {\n  f as BottomNavigationItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,8BAA8B;AACnG,MAAMC,CAAC,GAAGR,CAAC,CAACS,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK;IACR,MAAMC,CAAC,GAAGZ,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC;IACxBb,CAAC,CAACc,mBAAmB,CACnBH,CAAC,EACD,OAAO;MACLI,OAAO,EAAEH,CAAC,CAACI;IACb,CAAC,CACH,CAAC;IACD,MAAM;QACJC,SAAS,EAAEC,CAAC;QACZC,KAAK,EAAEC,CAAC;QACRC,QAAQ,EAAEC,CAAC;QACXC,QAAQ,EAAEC,CAAC;QACXC,IAAI,EAAEC,CAAC;QACPC,MAAM,EAAEC,CAAC;QACTC,QAAQ,EAAEC,CAAC;QACXC,IAAI,EAAEC,CAAC;QACPC,OAAO,EAAEC,CAAC;QACVC,IAAI,EAAEC,CAAC;QACPC,EAAE,EAAEC,CAAC;QACLC,QAAQ,EAAEC,CAAC;QACXC,SAAS,EAAEC,CAAC;QACZC,KAAK,EAAEC,CAAC;QACRC,QAAQ,EAAEC,CAAC,GAAGC,CAAC,CAACF;MAClB,CAAC,GAAGnC,CAAC;MAAEsC,CAAC,GAAGhD,CAAC,CAACiD,OAAO,CAClB,MAAM9C,CAAC,CACL,mBAAmB,EACnB;QACE,YAAY,EAAEmB,CAAC;QACf,YAAY,EAAEE;MAChB,CAAC,EACDN,CACF,CAAC,EACD,CAACI,CAAC,EAAEE,CAAC,EAAEN,CAAC,CACV,CAAC;MAAEgC,CAAC,GAAGlD,CAAC,CAACmD,WAAW,CACjBC,CAAC,IAAK;QACLZ,CAAC,IAAII,CAAC,KAAK,KAAK,CAAC,IAAI,CAACpB,CAAC,IAAIgB,CAAC,CAACY,CAAC,EAAER,CAAC,CAAC;MACpC,CAAC,EACD,CAACJ,CAAC,EAAEI,CAAC,EAAEpB,CAAC,CACV,CAAC;MAAE6B,CAAC,GAAGrD,CAAC,CAACmD,WAAW,CACjBC,CAAC,IAAK;QACLV,CAAC,IAAIE,CAAC,KAAK,KAAK,CAAC,IAAI,CAACpB,CAAC,IAAIkB,CAAC,CAACU,CAAC,EAAER,CAAC,CAAC;MACpC,CAAC,EACD,CAACF,CAAC,EAAEE,CAAC,EAAEpB,CAAC,CACV,CAAC;MAAE8B,CAAC,GAAG1B,CAAC;MAAE2B,CAAC,GAAG7B,CAAC;MAAE8B,CAAC,GAAG,eAAgBxD,CAAC,CAACyD,aAAa,CAClD,MAAM,EACN;QACEC,GAAG,EAAE9C,CAAC;QACNK,SAAS,EAAE+B,CAAC;QACZ7B,KAAK,EAAEC,CAAC;QACRuC,IAAI,EAAE,MAAM;QACZtB,EAAE,EAAEC,CAAC;QACLO,QAAQ,EAAEC,CAAC;QACXc,OAAO,EAAEV,CAAC;QACVT,SAAS,EAAEY,CAAC;QACZ,cAAc,EAAE/B,CAAC;QACjB,eAAe,EAAEE;MACnB,CAAC,EACD+B,CAAC,GAAG,eAAgBvD,CAAC,CAACyD,aAAa,CAACF,CAAC,EAAE;QAAEM,SAAS,EAAEjB,CAAC;QAAEnB,IAAI,EAAEK;MAAE,CAAC,CAAC,GAAG,eAAgB9B,CAAC,CAACyD,aAAa,CAACzD,CAAC,CAAC8D,QAAQ,EAAE,IAAI,EAAE,CAAC9B,CAAC,IAAIE,CAAC,KAAK,eAAgBlC,CAAC,CAACyD,aAAa,CAAClD,CAAC,EAAE;QAAEU,SAAS,EAAE,wBAAwB;QAAE8C,IAAI,EAAE/B,CAAC;QAAED,IAAI,EAAEG,CAAC;QAAE8B,IAAI,EAAE;MAAS,CAAC,CAAC,EAAE5B,CAAC,IAAI,eAAgBpC,CAAC,CAACyD,aAAa,CAAC,MAAM,EAAE;QAAExC,SAAS,EAAE,wBAAwB;QAAEE,KAAK,EAAE;UAAE8C,UAAU,EAAE;QAAO;MAAE,CAAC,EAAE7B,CAAC,CAAC,CAC1W,CAAC;IACD,OAAOkB,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,CAACY,IAAI,CAAC,KAAK,CAAC,EAAEV,CAAC,EAAE9C,CAAC,CAAC,GAAG8C,CAAC;EAChD,CACF,CAAC;EAAET,CAAC,GAAG;IACLF,QAAQ,EAAE;EACZ,CAAC;AACDrC,CAAC,CAAC2D,SAAS,GAAG;EACZlD,SAAS,EAAEhB,CAAC,CAACmE,MAAM;EACnBjD,KAAK,EAAElB,CAAC,CAACoE,MAAM;EACfhC,EAAE,EAAEpC,CAAC,CAACmE,MAAM;EACZ7C,QAAQ,EAAEtB,CAAC,CAACqE,IAAI;EAChBjD,QAAQ,EAAEpB,CAAC,CAACqE,IAAI;EAChBvC,IAAI,EAAE9B,CAAC,CAACmE,MAAM;EACdnC,OAAO,EAAE5B,CAAC;EACV8B,IAAI,EAAElC,CAAC,CAACmE,MAAM;EACdvB,QAAQ,EAAE5C,CAAC,CAACsE;AACd,CAAC;AACD/D,CAAC,CAACgE,WAAW,GAAG,gCAAgC;AAChD,SACEhE,CAAC,IAAIiE,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}