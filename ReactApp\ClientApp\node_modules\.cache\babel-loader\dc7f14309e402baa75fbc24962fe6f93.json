{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport t from \"prop-types\";\nimport { Animation as f } from \"./Animation.mjs\";\nconst l = r => {\n    const {\n        appear: e = n.appear,\n        enter: i = n.enter,\n        exit: a = n.exit,\n        transitionEnterDuration: o = n.transitionEnterDuration,\n        transitionExitDuration: s = n.transitionExitDuration,\n        children: p,\n        ...c\n      } = r,\n      m = {\n        transitionName: \"fade\"\n      };\n    return /* @__PURE__ */d.createElement(f, {\n      ...m,\n      appear: e,\n      enter: i,\n      exit: a,\n      transitionEnterDuration: o,\n      transitionExitDuration: s,\n      ...c\n    }, p);\n  },\n  n = {\n    appear: !1,\n    enter: !0,\n    exit: !1,\n    transitionEnterDuration: 500,\n    transitionExitDuration: 500\n  };\nl.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  component: t.node,\n  id: t.string,\n  style: t.any\n};\nexport { l as Fade };", "map": {"version": 3, "names": ["d", "t", "Animation", "f", "l", "r", "appear", "e", "n", "enter", "i", "exit", "a", "transitionEnterDuration", "o", "transitionExitDuration", "s", "children", "p", "c", "m", "transitionName", "createElement", "propTypes", "oneOfType", "arrayOf", "node", "childFactory", "any", "className", "string", "component", "id", "style", "Fade"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-animation/Fade.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as d from \"react\";\nimport t from \"prop-types\";\nimport { Animation as f } from \"./Animation.mjs\";\nconst l = (r) => {\n  const {\n    appear: e = n.appear,\n    enter: i = n.enter,\n    exit: a = n.exit,\n    transitionEnterDuration: o = n.transitionEnterDuration,\n    transitionExitDuration: s = n.transitionExitDuration,\n    children: p,\n    ...c\n  } = r, m = {\n    transitionName: \"fade\"\n  };\n  return /* @__PURE__ */ d.createElement(\n    f,\n    {\n      ...m,\n      appear: e,\n      enter: i,\n      exit: a,\n      transitionEnterDuration: o,\n      transitionExitDuration: s,\n      ...c\n    },\n    p\n  );\n}, n = {\n  appear: !1,\n  enter: !0,\n  exit: !1,\n  transitionEnterDuration: 500,\n  transitionExitDuration: 500\n};\nl.propTypes = {\n  children: t.oneOfType([t.arrayOf(t.node), t.node]),\n  childFactory: t.any,\n  className: t.string,\n  component: t.node,\n  id: t.string,\n  style: t.any\n};\nexport {\n  l as Fade\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,SAAS,IAAIC,CAAC,QAAQ,iBAAiB;AAChD,MAAMC,CAAC,GAAIC,CAAC,IAAK;IACf,MAAM;QACJC,MAAM,EAAEC,CAAC,GAAGC,CAAC,CAACF,MAAM;QACpBG,KAAK,EAAEC,CAAC,GAAGF,CAAC,CAACC,KAAK;QAClBE,IAAI,EAAEC,CAAC,GAAGJ,CAAC,CAACG,IAAI;QAChBE,uBAAuB,EAAEC,CAAC,GAAGN,CAAC,CAACK,uBAAuB;QACtDE,sBAAsB,EAAEC,CAAC,GAAGR,CAAC,CAACO,sBAAsB;QACpDE,QAAQ,EAAEC,CAAC;QACX,GAAGC;MACL,CAAC,GAAGd,CAAC;MAAEe,CAAC,GAAG;QACTC,cAAc,EAAE;MAClB,CAAC;IACD,OAAO,eAAgBrB,CAAC,CAACsB,aAAa,CACpCnB,CAAC,EACD;MACE,GAAGiB,CAAC;MACJd,MAAM,EAAEC,CAAC;MACTE,KAAK,EAAEC,CAAC;MACRC,IAAI,EAAEC,CAAC;MACPC,uBAAuB,EAAEC,CAAC;MAC1BC,sBAAsB,EAAEC,CAAC;MACzB,GAAGG;IACL,CAAC,EACDD,CACF,CAAC;EACH,CAAC;EAAEV,CAAC,GAAG;IACLF,MAAM,EAAE,CAAC,CAAC;IACVG,KAAK,EAAE,CAAC,CAAC;IACTE,IAAI,EAAE,CAAC,CAAC;IACRE,uBAAuB,EAAE,GAAG;IAC5BE,sBAAsB,EAAE;EAC1B,CAAC;AACDX,CAAC,CAACmB,SAAS,GAAG;EACZN,QAAQ,EAAEhB,CAAC,CAACuB,SAAS,CAAC,CAACvB,CAAC,CAACwB,OAAO,CAACxB,CAAC,CAACyB,IAAI,CAAC,EAAEzB,CAAC,CAACyB,IAAI,CAAC,CAAC;EAClDC,YAAY,EAAE1B,CAAC,CAAC2B,GAAG;EACnBC,SAAS,EAAE5B,CAAC,CAAC6B,MAAM;EACnBC,SAAS,EAAE9B,CAAC,CAACyB,IAAI;EACjBM,EAAE,EAAE/B,CAAC,CAAC6B,MAAM;EACZG,KAAK,EAAEhC,CAAC,CAAC2B;AACX,CAAC;AACD,SACExB,CAAC,IAAI8B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}