import { FileRecord } from '@app/components/forms/UploaderSubmit/types';
import { Canceler, CancelTokenSource } from 'axios';
import { ReactNode } from 'react';

export type ProgressingFileInfo = {
  name: string;
  size: number;
  uploadOptions: any;
  chunkCount: number;
  chunkCounter: number;
  chunkStartSize: number;
  chunkEndSize: number;
  toBeProceed: boolean;
  retryCount: number;
  percent?: number;
  currentChunkPercent?: number;
  cancelTokenSource?: CancelTokenSource;
  error?: Error;
  cancel?: Canceler;
  referenceNumber: string | null;
  completed: boolean;
};

export type ProgressingSimpleFileInfo = {
  id: any;
  referenceNumber: string | null;
  uploadOptions: any;
  chunkCount: number;
  chunkCounter: number;
  chunkStartSize: number;
  chunkEndSize: number;
  toBeProceed: boolean;
  uploadResult?: (uid: string) => void;
  percent?: number;
  currentChunkPercent?: number;
  cancelTokenSource?: CancelTokenSource;
  error?: Error;
  cancel?: Canceler;
  progress?: (percent: number) => void;
};

export interface FileList {
  [uid: string]: ProgressingFileInfo;
}

export interface SimpleFileList {
  [uid: string]: ProgressingSimpleFileInfo;
}

export type DragAndDropProps = {
  hasUrlUploadPermission: boolean;
};

export type FileDetailsModalProps = {
  siteId: string;
  files: FileRecord[];
  onClose?: () => void;
  onRemove?: (referenceNumber: string) => void;
  onSaveComplete?: (succeedFiles: FileRecord[]) => void;
  onLastFile?: () => void;
  onFinished: (data: any) => void;
};

export type UploadProgressProps = {
  files: FileList;
  onRetry: (uid: string) => void;
  onRetryAll: () => void;
  onDelete: (uid: string) => void;
  onDeleteAll: () => void;
  onComplete?: () => void;
};

export type ProcessingFileProps = {
  uid: string;
  info: ProgressingFileInfo;
  onRetry: (uid: string) => void;
  onDelete: (uid: string) => void;
};

export type FileUploaderProps = {
  siteId: string;
  portalFiles?: any[];
  onCloseModal?: () => void;
  onUpload: (file: any) => any;
  onRemove: (file: any) => any;
  onUploadComplete: (files: FileList) => any;
};
