import { useEffect, useState } from 'react';
import { FileRecord } from '../../forms/UploaderSubmit/types';
import { validateTitle } from '../validators';

const useFileList = ({ files }: { files: FileRecord[] }) => {
  const [fileList, setFileList] = useState(files);

  useEffect(() => {
    if (files.length < fileList.length) {
      setFileList(files);
    }
  }, [files, fileList.length]);

  const changeTitle = (value: string, index: number) => {
    setFileList((current) => {
      const updated = [
        {
          ...current[index],
          title: value,
          error: validateTitle(value),
        },
      ];
      return [...current.slice(0, index), ...updated, ...current.slice(index + 1)];
    });
  };

  const changeSelection = (value: boolean, index: number) => {
    setFileList((current) => {
      const updated = [
        {
          ...current[index],
          checked: value,
          error: value ? validateTitle(current[index].title) : '',
        },
      ];
      return [...current.slice(0, index), ...updated, ...current.slice(index + 1)];
    });
  };

  const changeSelectAll = (value: boolean) => {
    setFileList((current) => {
      return [
        ...current.map((fileRecord) => {
          return {
            ...fileRecord,
            checked: value,
            error: value ? validateTitle(fileRecord.title) : '',
          };
        }),
      ];
    });
  };

  const removeFile = (referenceNumber: string) => {
    setFileList((current) => {
      return current.filter((_) => _.referenceNumber !== referenceNumber);
    });
  };

  const removeFiles = (inputFileList: FileRecord[]) => {
    setFileList((current) => {
      return current.filter((fileRecord) => {
        return !inputFileList.find((val) => val.referenceNumber === fileRecord.referenceNumber);
      });
    });
  };

  return {
    fileList,
    actions: {
      changeTitle,
      changeSelection,
      changeSelectAll,
      removeFile,
      removeFiles,
    },
  };
};

export default useFileList;
