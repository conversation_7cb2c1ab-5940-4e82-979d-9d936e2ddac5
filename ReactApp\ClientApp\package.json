{"name": "yj-fe-cp", "version": "1.2.0-rc.1", "private": true, "dependencies": {"@iris/discovery.fe.client": "^2.0.1", "@okta/okta-auth-js": "^6.7.7", "@okta/okta-react": "^6.6.0", "@progress/kendo-react-buttons": "^11.0.0", "@progress/kendo-react-layout": "^11.0.0", "@progress/kendo-theme-default": "^11.0.2", "@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.1", "@types/node": "^16.11.36", "@types/react": "^18.0.9", "@types/react-dom": "^18.0.5", "@types/react-router-dom": "^5.1.5", "antd": "^4.24.7", "axios": "^0.27.2", "copy-webpack-plugin": "^11.0.0", "customize-cra": "^1.0.0", "customize-cra-less-loader": "^2.0.0", "downloadjs": "^1.4.7", "jwt-decode": "^3.1.2", "less": "^4.1.3", "less-loader": "^11.0.0", "lodash": "^4.17.15", "postcss-loader": "^7.0.0", "react": "^18.2.0", "react-app-rewired": "^2.2.1", "react-dom": "^18.1.0", "react-icons": "^4.4.0", "react-redux": "^8.0.2", "react-router-dom": "^6.8.0", "react-scripts": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-persist": "^6.0.0", "typescript": "^4.6.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env REACT_APP_VERSION=$npm_package_version react-app-rewired start", "build": "cross-env REACT_APP_VERSION=$npm_package_version react-app-rewired --max_old_space_size=4096 build", "test": "react-app-rewired test", "test-coverage": "react-app-rewired test --coverage", "eject": "react-scripts eject", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "prettier": "npx prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@redux-devtools/core": "^3.13.1", "@types/downloadjs": "^1.4.3", "@types/lodash": "^4.14.182", "babel-plugin-import": "^1.13.5", "cross-env": "^7.0.3", "eslint-config-prettier": "^8.5.0", "jest-canvas-mock": "^2.4.0", "prettier": "^2.6.2", "react-test-renderer": "^18.1.0", "redux-mock-store": "^1.5.4", "stylelint": "^14.8.5", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.2.0"}, "jest": {"moduleNameMapper": {"^@$": "<rootDir>/src$1", "^@app(.*)$": "<rootDir>/src/app$1", "^@pages(.*)$": "<rootDir>/src/app/pages$1"}, "verbose": false, "collectCoverage": true, "transformIgnorePatterns ": ["/node_modules/(?!babel)"], "collectCoverageFrom": ["**/*.{ts,tsx}", "!coverage/**", "!node_modules/**", "!src/index.js", "!src/setupTests.js", "!public/**", "!build/**", "!src/serviceWorker.js", "!src/Routes.js", "!src/test/**", "!src/App.test.js", "!**/_tests_/**", "!**/menus/**", "!**/routes/**", "!**/*.test.js"], "coverageReporters": ["text", "lcov", "json", "text", "clover", "cobertura"], "setupFiles": ["jest-canvas-mock"]}}