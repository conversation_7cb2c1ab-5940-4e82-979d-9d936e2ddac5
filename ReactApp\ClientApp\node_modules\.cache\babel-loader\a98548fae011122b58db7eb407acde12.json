{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\n\nimport e from \"react\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst o = \"ActionSheetView\",\n  s = e.forwardRef((t, a) => {\n    const i = e.useRef(null);\n    return e.useImperativeHandle(a, () => i, []), /* @__PURE__ */e.createElement(\"div\", {\n      ref: i,\n      style: t.style,\n      className: c(\"k-actionsheet-view\", t.className)\n    }, t.children);\n  });\ns.displayName = \"ActionSheetView\";\nexport { s as ActionSheetView, o as ActionSheetViewDisplayName };", "map": {"version": 3, "names": ["e", "classNames", "c", "o", "s", "forwardRef", "t", "a", "i", "useRef", "useImperativeHandle", "createElement", "ref", "style", "className", "children", "displayName", "ActionSheetView", "ActionSheetViewDisplayName"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/actionsheet/ActionSheetView.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\n\"use client\";\nimport e from \"react\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst o = \"ActionSheetView\", s = e.forwardRef((t, a) => {\n  const i = e.useRef(null);\n  return e.useImperativeHandle(a, () => i, []), /* @__PURE__ */ e.createElement(\"div\", { ref: i, style: t.style, className: c(\"k-actionsheet-view\", t.className) }, t.children);\n});\ns.displayName = \"ActionSheetView\";\nexport {\n  s as ActionSheetView,\n  o as ActionSheetViewDisplayName\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AACZ,OAAOA,CAAC,MAAM,OAAO;AACrB,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAG,iBAAiB;EAAEC,CAAC,GAAGJ,CAAC,CAACK,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACtD,MAAMC,CAAC,GAAGR,CAAC,CAACS,MAAM,CAAC,IAAI,CAAC;IACxB,OAAOT,CAAC,CAACU,mBAAmB,CAACH,CAAC,EAAE,MAAMC,CAAC,EAAE,EAAE,CAAC,EAAE,eAAgBR,CAAC,CAACW,aAAa,CAAC,KAAK,EAAE;MAAEC,GAAG,EAAEJ,CAAC;MAAEK,KAAK,EAAEP,CAAC,CAACO,KAAK;MAAEC,SAAS,EAAEZ,CAAC,CAAC,oBAAoB,EAAEI,CAAC,CAACQ,SAAS;IAAE,CAAC,EAAER,CAAC,CAACS,QAAQ,CAAC;EAC/K,CAAC,CAAC;AACFX,CAAC,CAACY,WAAW,GAAG,iBAAiB;AACjC,SACEZ,CAAC,IAAIa,eAAe,EACpBd,CAAC,IAAIe,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}