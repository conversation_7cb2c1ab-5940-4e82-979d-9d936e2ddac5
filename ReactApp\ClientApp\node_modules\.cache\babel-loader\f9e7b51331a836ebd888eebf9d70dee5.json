{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport n from \"prop-types\";\nimport { dispatchEvent as l, useDir as o, getTabIndex as k, classNames as d } from \"@progress/kendo-react-common\";\nconst r = t.forwardRef((e, s) => {\n    const c = t.useRef(null),\n      i = t.useRef(null),\n      m = t.useCallback(() => {\n        i.current && i.current.focus();\n      }, [i]);\n    t.useImperativeHandle(c, () => ({\n      element: i.current,\n      focus: m,\n      props: e\n    })), t.useImperativeHandle(s, () => c.current);\n    const u = t.useCallback(a => {\n        e.id && l(e.onItemSelect, a, a.target, {\n          id: e.id\n        });\n      }, [e.onItemSelect]),\n      b = t.useCallback(a => {\n        e.id && l(e.onKeyDown, a, a.target, {\n          id: e.id\n        });\n      }, [e.onKeyDown]);\n    return /* @__PURE__ */t.createElement(\"a\", {\n      href: \"#\",\n      \"aria-current\": e.ariaCurrent ? e.ariaCurrent : e.isLast,\n      role: \"link\",\n      id: e.id,\n      ref: i,\n      style: e.style,\n      dir: o(i, e.dir),\n      tabIndex: k(e.tabIndex, e.disabled),\n      className: d(e.className, {\n        \"k-breadcrumb-root-link\": e.isFirst,\n        \"k-breadcrumb-link\": !e.isFirst,\n        \"k-breadcrumb-icontext-link\": (e.icon !== void 0 || e.iconClass !== void 0) && e.text,\n        \"k-breadcrumb-icon-link\": (e.icon !== void 0 || e.iconClass !== void 0) && !e.text,\n        \"k-disabled\": e.disabled\n      }),\n      onClick: a => {\n        a.preventDefault(), u(a);\n      },\n      onKeyDown: b\n    }, e.iconClass ? /* @__PURE__ */t.createElement(\"span\", {\n      className: d(\"k-icon\", e.iconClass)\n    }) : e.icon ? e.icon : \"\", e.text && /* @__PURE__ */t.createElement(\"span\", {\n      className: \"k-breadcrumb-item-text\"\n    }, e.text));\n  }),\n  f = {\n    id: n.string,\n    className: n.string,\n    tabIndex: n.number,\n    style: n.object,\n    dir: n.string,\n    disabled: n.bool,\n    text: n.string,\n    icon: n.node,\n    iconClass: n.string,\n    onClick: n.func,\n    ariaCurrent: n.bool\n  };\nr.displayName = \"KendoReactBreadcrumbLink\";\nr.propTypes = f;\nexport { r as BreadcrumbLink };", "map": {"version": 3, "names": ["t", "n", "dispatchEvent", "l", "useDir", "o", "getTabIndex", "k", "classNames", "d", "r", "forwardRef", "e", "s", "c", "useRef", "i", "m", "useCallback", "current", "focus", "useImperativeHandle", "element", "props", "u", "a", "id", "onItemSelect", "target", "b", "onKeyDown", "createElement", "href", "aria<PERSON>urrent", "isLast", "role", "ref", "style", "dir", "tabIndex", "disabled", "className", "<PERSON><PERSON><PERSON><PERSON>", "icon", "iconClass", "text", "onClick", "preventDefault", "f", "string", "number", "object", "bool", "node", "func", "displayName", "propTypes", "BreadcrumbLink"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/breadcrumb/BreadcrumbLink.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport n from \"prop-types\";\nimport { dispatchEvent as l, useDir as o, getTabIndex as k, classNames as d } from \"@progress/kendo-react-common\";\nconst r = t.forwardRef((e, s) => {\n  const c = t.useRef(null), i = t.useRef(null), m = t.useCallback(() => {\n    i.current && i.current.focus();\n  }, [i]);\n  t.useImperativeHandle(c, () => ({\n    element: i.current,\n    focus: m,\n    props: e\n  })), t.useImperativeHandle(s, () => c.current);\n  const u = t.useCallback(\n    (a) => {\n      e.id && l(e.onItemSelect, a, a.target, {\n        id: e.id\n      });\n    },\n    [e.onItemSelect]\n  ), b = t.useCallback(\n    (a) => {\n      e.id && l(e.onKeyDown, a, a.target, {\n        id: e.id\n      });\n    },\n    [e.onKeyDown]\n  );\n  return /* @__PURE__ */ t.createElement(\n    \"a\",\n    {\n      href: \"#\",\n      \"aria-current\": e.ariaCurrent ? e.ariaCurrent : e.isLast,\n      role: \"link\",\n      id: e.id,\n      ref: i,\n      style: e.style,\n      dir: o(i, e.dir),\n      tabIndex: k(e.tabIndex, e.disabled),\n      className: d(e.className, {\n        \"k-breadcrumb-root-link\": e.isFirst,\n        \"k-breadcrumb-link\": !e.isFirst,\n        \"k-breadcrumb-icontext-link\": (e.icon !== void 0 || e.iconClass !== void 0) && e.text,\n        \"k-breadcrumb-icon-link\": (e.icon !== void 0 || e.iconClass !== void 0) && !e.text,\n        \"k-disabled\": e.disabled\n      }),\n      onClick: (a) => {\n        a.preventDefault(), u(a);\n      },\n      onKeyDown: b\n    },\n    e.iconClass ? /* @__PURE__ */ t.createElement(\"span\", { className: d(\"k-icon\", e.iconClass) }) : e.icon ? e.icon : \"\",\n    e.text && /* @__PURE__ */ t.createElement(\"span\", { className: \"k-breadcrumb-item-text\" }, e.text)\n  );\n}), f = {\n  id: n.string,\n  className: n.string,\n  tabIndex: n.number,\n  style: n.object,\n  dir: n.string,\n  disabled: n.bool,\n  text: n.string,\n  icon: n.node,\n  iconClass: n.string,\n  onClick: n.func,\n  ariaCurrent: n.bool\n};\nr.displayName = \"KendoReactBreadcrumbLink\";\nr.propTypes = f;\nexport {\n  r as BreadcrumbLink\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,aAAa,IAAIC,CAAC,EAAEC,MAAM,IAAIC,CAAC,EAAEC,WAAW,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AACjH,MAAMC,CAAC,GAAGV,CAAC,CAACW,UAAU,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC/B,MAAMC,CAAC,GAAGd,CAAC,CAACe,MAAM,CAAC,IAAI,CAAC;MAAEC,CAAC,GAAGhB,CAAC,CAACe,MAAM,CAAC,IAAI,CAAC;MAAEE,CAAC,GAAGjB,CAAC,CAACkB,WAAW,CAAC,MAAM;QACpEF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC;MAChC,CAAC,EAAE,CAACJ,CAAC,CAAC,CAAC;IACPhB,CAAC,CAACqB,mBAAmB,CAACP,CAAC,EAAE,OAAO;MAC9BQ,OAAO,EAAEN,CAAC,CAACG,OAAO;MAClBC,KAAK,EAAEH,CAAC;MACRM,KAAK,EAAEX;IACT,CAAC,CAAC,CAAC,EAAEZ,CAAC,CAACqB,mBAAmB,CAACR,CAAC,EAAE,MAAMC,CAAC,CAACK,OAAO,CAAC;IAC9C,MAAMK,CAAC,GAAGxB,CAAC,CAACkB,WAAW,CACpBO,CAAC,IAAK;QACLb,CAAC,CAACc,EAAE,IAAIvB,CAAC,CAACS,CAAC,CAACe,YAAY,EAAEF,CAAC,EAAEA,CAAC,CAACG,MAAM,EAAE;UACrCF,EAAE,EAAEd,CAAC,CAACc;QACR,CAAC,CAAC;MACJ,CAAC,EACD,CAACd,CAAC,CAACe,YAAY,CACjB,CAAC;MAAEE,CAAC,GAAG7B,CAAC,CAACkB,WAAW,CACjBO,CAAC,IAAK;QACLb,CAAC,CAACc,EAAE,IAAIvB,CAAC,CAACS,CAAC,CAACkB,SAAS,EAAEL,CAAC,EAAEA,CAAC,CAACG,MAAM,EAAE;UAClCF,EAAE,EAAEd,CAAC,CAACc;QACR,CAAC,CAAC;MACJ,CAAC,EACD,CAACd,CAAC,CAACkB,SAAS,CACd,CAAC;IACD,OAAO,eAAgB9B,CAAC,CAAC+B,aAAa,CACpC,GAAG,EACH;MACEC,IAAI,EAAE,GAAG;MACT,cAAc,EAAEpB,CAAC,CAACqB,WAAW,GAAGrB,CAAC,CAACqB,WAAW,GAAGrB,CAAC,CAACsB,MAAM;MACxDC,IAAI,EAAE,MAAM;MACZT,EAAE,EAAEd,CAAC,CAACc,EAAE;MACRU,GAAG,EAAEpB,CAAC;MACNqB,KAAK,EAAEzB,CAAC,CAACyB,KAAK;MACdC,GAAG,EAAEjC,CAAC,CAACW,CAAC,EAAEJ,CAAC,CAAC0B,GAAG,CAAC;MAChBC,QAAQ,EAAEhC,CAAC,CAACK,CAAC,CAAC2B,QAAQ,EAAE3B,CAAC,CAAC4B,QAAQ,CAAC;MACnCC,SAAS,EAAEhC,CAAC,CAACG,CAAC,CAAC6B,SAAS,EAAE;QACxB,wBAAwB,EAAE7B,CAAC,CAAC8B,OAAO;QACnC,mBAAmB,EAAE,CAAC9B,CAAC,CAAC8B,OAAO;QAC/B,4BAA4B,EAAE,CAAC9B,CAAC,CAAC+B,IAAI,KAAK,KAAK,CAAC,IAAI/B,CAAC,CAACgC,SAAS,KAAK,KAAK,CAAC,KAAKhC,CAAC,CAACiC,IAAI;QACrF,wBAAwB,EAAE,CAACjC,CAAC,CAAC+B,IAAI,KAAK,KAAK,CAAC,IAAI/B,CAAC,CAACgC,SAAS,KAAK,KAAK,CAAC,KAAK,CAAChC,CAAC,CAACiC,IAAI;QAClF,YAAY,EAAEjC,CAAC,CAAC4B;MAClB,CAAC,CAAC;MACFM,OAAO,EAAGrB,CAAC,IAAK;QACdA,CAAC,CAACsB,cAAc,CAAC,CAAC,EAAEvB,CAAC,CAACC,CAAC,CAAC;MAC1B,CAAC;MACDK,SAAS,EAAED;IACb,CAAC,EACDjB,CAAC,CAACgC,SAAS,GAAG,eAAgB5C,CAAC,CAAC+B,aAAa,CAAC,MAAM,EAAE;MAAEU,SAAS,EAAEhC,CAAC,CAAC,QAAQ,EAAEG,CAAC,CAACgC,SAAS;IAAE,CAAC,CAAC,GAAGhC,CAAC,CAAC+B,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,GAAG,EAAE,EACrH/B,CAAC,CAACiC,IAAI,IAAI,eAAgB7C,CAAC,CAAC+B,aAAa,CAAC,MAAM,EAAE;MAAEU,SAAS,EAAE;IAAyB,CAAC,EAAE7B,CAAC,CAACiC,IAAI,CACnG,CAAC;EACH,CAAC,CAAC;EAAEG,CAAC,GAAG;IACNtB,EAAE,EAAEzB,CAAC,CAACgD,MAAM;IACZR,SAAS,EAAExC,CAAC,CAACgD,MAAM;IACnBV,QAAQ,EAAEtC,CAAC,CAACiD,MAAM;IAClBb,KAAK,EAAEpC,CAAC,CAACkD,MAAM;IACfb,GAAG,EAAErC,CAAC,CAACgD,MAAM;IACbT,QAAQ,EAAEvC,CAAC,CAACmD,IAAI;IAChBP,IAAI,EAAE5C,CAAC,CAACgD,MAAM;IACdN,IAAI,EAAE1C,CAAC,CAACoD,IAAI;IACZT,SAAS,EAAE3C,CAAC,CAACgD,MAAM;IACnBH,OAAO,EAAE7C,CAAC,CAACqD,IAAI;IACfrB,WAAW,EAAEhC,CAAC,CAACmD;EACjB,CAAC;AACD1C,CAAC,CAAC6C,WAAW,GAAG,0BAA0B;AAC1C7C,CAAC,CAAC8C,SAAS,GAAGR,CAAC;AACf,SACEtC,CAAC,IAAI+C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}