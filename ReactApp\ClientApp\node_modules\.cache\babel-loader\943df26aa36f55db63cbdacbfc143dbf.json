{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\FrontEnd-Portal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\PortalControls\\\\index.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortalControls = props => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Portal Controls\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = PortalControls;\nexport default PortalControls;\nvar _c;\n$RefreshReg$(_c, \"PortalControls\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PortalControls", "props", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/pages/PortalControls/index.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst PortalControls = (props: any) => {\r\n\r\n  return (\r\n    <div>\r\n      <h3>Portal Controls</h3>\r\n    </div>\r\n  )\r\n};\r\n\r\nexport default PortalControls;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAIC,KAAU,IAAK;EAErC,oBACEF,OAAA;IAAAG,QAAA,eACEH,OAAA;MAAAG,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrB,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIP,cAAc;AASpB,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}