{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nfunction n(e) {\n  return e.hoverOpenDelay !== void 0 ? e.hoverOpenDelay : e.openOnClick ? 0 : 100;\n}\nfunction o(e) {\n  return e.hoverCloseDelay !== void 0 ? e.hoverCloseDelay : 100;\n}\nexport { o as getHoverCloseDelay, n as getHoverOpenDelay };", "map": {"version": 3, "names": ["n", "e", "hoverOpenDelay", "openOnClick", "o", "hoverCloseDelay", "getHoverCloseDelay", "getHoverOpenDelay"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/utils/hoverDelay.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nfunction n(e) {\n  return e.hoverOpenDelay !== void 0 ? e.hoverOpenDelay : e.openOnClick ? 0 : 100;\n}\nfunction o(e) {\n  return e.hoverCloseDelay !== void 0 ? e.hoverCloseDelay : 100;\n}\nexport {\n  o as getHoverCloseDelay,\n  n as getHoverOpenDelay\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACC,cAAc,KAAK,KAAK,CAAC,GAAGD,CAAC,CAACC,cAAc,GAAGD,CAAC,CAACE,WAAW,GAAG,CAAC,GAAG,GAAG;AACjF;AACA,SAASC,CAACA,CAACH,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACI,eAAe,KAAK,KAAK,CAAC,GAAGJ,CAAC,CAACI,eAAe,GAAG,GAAG;AAC/D;AACA,SACED,CAAC,IAAIE,kBAAkB,EACvBN,CAAC,IAAIO,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}