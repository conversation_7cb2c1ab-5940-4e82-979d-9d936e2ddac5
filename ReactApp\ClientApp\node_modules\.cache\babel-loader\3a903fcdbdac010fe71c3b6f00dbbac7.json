{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport { getActiveElement as u, IconWrap as h, classNames as p } from \"@progress/kendo-react-common\";\nimport { Popup as c } from \"@progress/kendo-react-popup\";\nimport { shouldOpenItem as I, isFirstItemFromSiblings as M } from \"../utils/itemsIdsUtils.mjs\";\nimport { getDOMElementId as d, convertBoolDirectionToString as m, getPopupSettings as g } from \"../utils/misc.mjs\";\nimport { MenuItemLink as f } from \"./MenuItemLink.mjs\";\nimport { MenuItemArrow as v } from \"./MenuItemArrow.mjs\";\nclass k extends s.Component {\n  constructor() {\n    super(...arguments), this.onMouseOver = e => {\n      this.props.onMouseOver(this.props.parentItemId), e.stopPropagation();\n    }, this.onMouseLeave = e => {\n      this.props.onMouseLeave(this.props.parentItemId), e.stopPropagation();\n    };\n  }\n  render() {\n    const e = this.props.parentItemId;\n    return /* @__PURE__ */s.createElement(\"ul\", {\n      className: this.props.className,\n      role: this.props.role ? this.props.role : e !== void 0 ? \"menu\" : \"menubar\",\n      id: e !== void 0 ? d(this.props.menuGuid, e) : void 0,\n      onMouseOver: e !== void 0 ? this.onMouseOver : void 0,\n      onMouseLeave: e !== void 0 ? this.onMouseLeave : void 0,\n      \"aria-orientation\": this.props[\"aria-orientation\"]\n    }, this.renderChildItems());\n  }\n  renderChildItems() {\n    return this.props.items.length > 0 ? this.props.items.map((e, t) => /* @__PURE__ */s.createElement(O, {\n      item: e,\n      animate: this.props.animate,\n      isMenuVertical: this.props.isMenuVertical,\n      isDirectionRightToLeft: this.props.isDirectionRightToLeft,\n      focusedItemId: this.props.focusedItemId,\n      lastItemIdToBeOpened: this.props.lastItemIdToBeOpened,\n      tabbableItemId: this.props.tabbableItemId,\n      itemRender: this.props.itemRender,\n      linkRender: this.props.linkRender,\n      menuGuid: this.props.menuGuid,\n      onMouseOver: this.props.onMouseOver,\n      onMouseLeave: this.props.onMouseLeave,\n      onMouseDown: this.props.onMouseDown,\n      onBlur: this.props.onBlur,\n      onFocus: this.props.onFocus,\n      onClick: this.props.onClick,\n      onOriginalItemNeeded: this.props.onOriginalItemNeeded,\n      key: t\n    })) : null;\n  }\n}\nclass O extends s.Component {\n  constructor(e) {\n    super(e), this.isFirstRender = !0, this.onMouseOver = t => {\n      this.props.onMouseOver(this.props.item.id), t.stopPropagation();\n    }, this.onMouseLeave = t => {\n      this.props.onMouseLeave(this.props.item.id), t.stopPropagation();\n    }, this.state = {\n      opened: !1\n    };\n  }\n  componentDidMount() {\n    const e = this.props.focusedItemId,\n      t = this.props.item.id;\n    e && e === t && this.itemElement.focus({\n      preventScroll: !0\n    }), this.isFirstRender = !1;\n  }\n  componentDidUpdate(e) {\n    const t = this.props.focusedItemId,\n      o = this.props.item.id;\n    if (t) {\n      const n = u(document);\n      e.focusedItemId !== t && t === o &&\n      // https://github.com/telerik/kendo-react/issues/216 :\n      // No need to focus the wrapping menu item DOM element\n      // when a child DOM element was clicked.\n      !this.itemElement.contains(n) && this.itemElement.focus({\n        preventScroll: !0\n      });\n    }\n  }\n  render() {\n    const e = this.props.item,\n      t = e.id,\n      o = d(this.props.menuGuid, t),\n      n = e.separator;\n    return /* @__PURE__ */s.createElement(s.Fragment, null, n ? /* @__PURE__ */s.createElement(\"li\", {\n      className: \"k-separator k-item\",\n      \"aria-hidden\": !0,\n      key: o,\n      id: o,\n      ref: i => {\n        this.itemElement = i;\n      }\n    }) : /* @__PURE__ */s.createElement(\"li\", {\n      id: o,\n      className: this.getMenuItemClassName(e),\n      style: e.cssStyle,\n      tabIndex: t === this.props.tabbableItemId ? 0 : -1,\n      onMouseOver: this.onMouseOver,\n      onMouseLeave: this.onMouseLeave,\n      onMouseDown: i => this.props.onMouseDown(i),\n      onBlur: i => this.props.onBlur(t, i),\n      onFocus: () => this.props.onFocus(t),\n      onClick: i => this.props.onClick(i, t),\n      role: \"menuitem\",\n      \"aria-disabled\": e.disabled ? !0 : void 0,\n      \"aria-haspopup\": e.items.length > 0 ? !0 : void 0,\n      \"aria-expanded\": e.items.length > 0 ? this.Opened : void 0,\n      \"aria-label\": e.text,\n      \"aria-owns\": this.Opened ? o : void 0,\n      ref: i => {\n        this.itemElement = i;\n      },\n      key: o\n    }, this.contentRender ? this.renderContent() : this.renderMenuItemLink()), this.renderPopupIfOpened());\n  }\n  renderContent() {\n    const e = this.props.item.contentParentItemId;\n    return /* @__PURE__ */s.createElement(\"div\", {\n      className: \"k-content\",\n      role: \"presentation\"\n    }, /* @__PURE__ */s.createElement(this.contentRender, {\n      item: this.props.onOriginalItemNeeded(e),\n      itemId: e\n    }));\n  }\n  renderMenuItemLink() {\n    const e = this.props.item;\n    if (this.linkRender) return /* @__PURE__ */s.createElement(this.linkRender, {\n      item: this.props.onOriginalItemNeeded(e.id),\n      itemId: e.id,\n      opened: this.Opened,\n      dir: m(this.props.isDirectionRightToLeft)\n    });\n    const t = this.itemRender ? /* @__PURE__ */s.createElement(this.itemRender, {\n      item: this.props.onOriginalItemNeeded(e.id),\n      itemId: e.id,\n      key: \"1\"\n    }) : /* @__PURE__ */s.createElement(\"span\", {\n      className: \"k-menu-link-text\"\n    }, e.text);\n    return /* @__PURE__ */s.createElement(f, {\n      url: e.url,\n      opened: this.Opened\n    }, this.renderMenuIconIfApplicable(), t, this.renderArrowIfApplicable());\n  }\n  renderPopupIfOpened() {\n    const e = this.props.item.id,\n      t = this.props.animate,\n      {\n        anchorAlign: o,\n        popupAlign: n,\n        collision: i,\n        animationDirection: r\n      } = g(e, this.props.isMenuVertical, this.props.isDirectionRightToLeft),\n      l = t === !0 ? {\n        openDuration: 300,\n        closeDuration: 300,\n        direction: r\n      } : t === !1 ? !1 : {\n        openDuration: (t == null ? void 0 : t.openDuration) || 300,\n        closeDuration: (t == null ? void 0 : t.closeDuration) || 300,\n        direction: (t == null ? void 0 : t.direction) || r\n      };\n    return /* @__PURE__ */s.createElement(c, {\n      anchor: this.itemElement,\n      show: this.Opened,\n      popupClass: this.getPopupClassName(),\n      anchorAlign: o,\n      popupAlign: n,\n      collision: i,\n      animate: l,\n      key: \"1\"\n    }, /* @__PURE__ */s.createElement(k, {\n      parentItemId: e,\n      animate: this.props.animate,\n      items: this.props.item.items,\n      menuGuid: this.props.menuGuid,\n      focusedItemId: this.props.focusedItemId,\n      lastItemIdToBeOpened: this.props.lastItemIdToBeOpened,\n      tabbableItemId: this.props.tabbableItemId,\n      itemRender: this.props.itemRender,\n      linkRender: this.props.linkRender,\n      isMenuVertical: this.props.isMenuVertical,\n      isDirectionRightToLeft: this.props.isDirectionRightToLeft,\n      className: \"k-group k-menu-group k-reset k-menu-group-md\",\n      onMouseOver: this.props.onMouseOver,\n      onMouseLeave: this.props.onMouseLeave,\n      onMouseDown: this.props.onMouseDown,\n      onBlur: this.props.onBlur,\n      onFocus: this.props.onFocus,\n      onClick: this.props.onClick,\n      onOriginalItemNeeded: this.props.onOriginalItemNeeded\n    }));\n  }\n  renderMenuIconIfApplicable() {\n    const {\n      icon: e,\n      svgIcon: t\n    } = this.props.item;\n    return e || t ? /* @__PURE__ */s.createElement(h, {\n      name: e,\n      icon: t,\n      key: \"0\"\n    }) : null;\n  }\n  renderArrowIfApplicable() {\n    return this.props.item.items.length > 0 ? /* @__PURE__ */s.createElement(\"span\", {\n      className: \"k-menu-expand-arrow\",\n      \"aria-hidden\": !0\n    }, /* @__PURE__ */s.createElement(v, {\n      itemId: this.props.item.id,\n      verticalMenu: this.props.isMenuVertical,\n      dir: m(this.props.isDirectionRightToLeft),\n      key: \"2\"\n    })) : null;\n  }\n  get itemRender() {\n    return this.props.item.render || this.props.itemRender;\n  }\n  get linkRender() {\n    return this.props.item.linkRender || this.props.linkRender;\n  }\n  get contentRender() {\n    return this.props.item.contentParentItemId ? this.props.item.contentRender : null;\n  }\n  get Opened() {\n    const e = this.props;\n    return e.item.items.length > 0 && I(e.item.id, e.lastItemIdToBeOpened) &&\n    // HACK: Wait for the second render because otherwise the scenario of\n    // popup inside popup throws an error (for example, hover of item with id '0_0').\n    !this.isFirstRender;\n  }\n  getPopupClassName() {\n    return p(\"k-menu-popup\", {\n      \"k-rtl\": this.props.isDirectionRightToLeft\n    });\n  }\n  getMenuItemClassName(e) {\n    return p(\"k-item\", \"k-menu-item\", {\n      \"k-first\": M(e.id),\n      \"k-last\": e.isLastFromSiblings,\n      \"k-disabled\": e.disabled\n    }, e.cssClass);\n  }\n}\nexport { O as MenuItemInternal, k as MenuItemInternalsList };", "map": {"version": 3, "names": ["s", "getActiveElement", "u", "IconWrap", "h", "classNames", "p", "Popup", "c", "shouldOpenItem", "I", "isFirstItemFromSiblings", "M", "getDOMElementId", "d", "convertBoolDirectionToString", "m", "getPopupSettings", "g", "MenuItemLink", "f", "MenuItemArrow", "v", "k", "Component", "constructor", "arguments", "onMouseOver", "e", "props", "parentItemId", "stopPropagation", "onMouseLeave", "render", "createElement", "className", "role", "id", "menuGuid", "renderChildItems", "items", "length", "map", "t", "O", "item", "animate", "isMenuVertical", "isDirectionRightToLeft", "focusedItemId", "lastItemIdToBeOpened", "tabbableItemId", "itemRender", "linkRender", "onMouseDown", "onBlur", "onFocus", "onClick", "onOriginalItemNeeded", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "opened", "componentDidMount", "itemElement", "focus", "preventScroll", "componentDidUpdate", "o", "n", "document", "contains", "separator", "Fragment", "ref", "i", "getMenuItemClassName", "style", "cssStyle", "tabIndex", "disabled", "Opened", "text", "contentRender", "renderContent", "renderMenuItemLink", "renderPopupIfOpened", "contentParentItemId", "itemId", "dir", "url", "renderMenuIconIfApplicable", "renderArrowIfApplicable", "anchorAlign", "popupAlign", "collision", "animationDirection", "r", "l", "openDuration", "closeDuration", "direction", "anchor", "show", "popupClass", "getPopupClassName", "icon", "svgIcon", "name", "verticalMenu", "isLastFromSiblings", "cssClass", "MenuItemInternal", "MenuItemInternalsList"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/menu/components/MenuItemInternal.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as s from \"react\";\nimport { getActiveElement as u, IconWrap as h, classNames as p } from \"@progress/kendo-react-common\";\nimport { Popup as c } from \"@progress/kendo-react-popup\";\nimport { shouldOpenItem as I, isFirstItemFromSiblings as M } from \"../utils/itemsIdsUtils.mjs\";\nimport { getDOMElementId as d, convertBoolDirectionToString as m, getPopupSettings as g } from \"../utils/misc.mjs\";\nimport { MenuItemLink as f } from \"./MenuItemLink.mjs\";\nimport { MenuItemArrow as v } from \"./MenuItemArrow.mjs\";\nclass k extends s.Component {\n  constructor() {\n    super(...arguments), this.onMouseOver = (e) => {\n      this.props.onMouseOver(this.props.parentItemId), e.stopPropagation();\n    }, this.onMouseLeave = (e) => {\n      this.props.onMouseLeave(this.props.parentItemId), e.stopPropagation();\n    };\n  }\n  render() {\n    const e = this.props.parentItemId;\n    return /* @__PURE__ */ s.createElement(\n      \"ul\",\n      {\n        className: this.props.className,\n        role: this.props.role ? this.props.role : e !== void 0 ? \"menu\" : \"menubar\",\n        id: e !== void 0 ? d(this.props.menuGuid, e) : void 0,\n        onMouseOver: e !== void 0 ? this.onMouseOver : void 0,\n        onMouseLeave: e !== void 0 ? this.onMouseLeave : void 0,\n        \"aria-orientation\": this.props[\"aria-orientation\"]\n      },\n      this.renderChildItems()\n    );\n  }\n  renderChildItems() {\n    return this.props.items.length > 0 ? this.props.items.map((e, t) => /* @__PURE__ */ s.createElement(\n      O,\n      {\n        item: e,\n        animate: this.props.animate,\n        isMenuVertical: this.props.isMenuVertical,\n        isDirectionRightToLeft: this.props.isDirectionRightToLeft,\n        focusedItemId: this.props.focusedItemId,\n        lastItemIdToBeOpened: this.props.lastItemIdToBeOpened,\n        tabbableItemId: this.props.tabbableItemId,\n        itemRender: this.props.itemRender,\n        linkRender: this.props.linkRender,\n        menuGuid: this.props.menuGuid,\n        onMouseOver: this.props.onMouseOver,\n        onMouseLeave: this.props.onMouseLeave,\n        onMouseDown: this.props.onMouseDown,\n        onBlur: this.props.onBlur,\n        onFocus: this.props.onFocus,\n        onClick: this.props.onClick,\n        onOriginalItemNeeded: this.props.onOriginalItemNeeded,\n        key: t\n      }\n    )) : null;\n  }\n}\nclass O extends s.Component {\n  constructor(e) {\n    super(e), this.isFirstRender = !0, this.onMouseOver = (t) => {\n      this.props.onMouseOver(this.props.item.id), t.stopPropagation();\n    }, this.onMouseLeave = (t) => {\n      this.props.onMouseLeave(this.props.item.id), t.stopPropagation();\n    }, this.state = { opened: !1 };\n  }\n  componentDidMount() {\n    const e = this.props.focusedItemId, t = this.props.item.id;\n    e && e === t && this.itemElement.focus({ preventScroll: !0 }), this.isFirstRender = !1;\n  }\n  componentDidUpdate(e) {\n    const t = this.props.focusedItemId, o = this.props.item.id;\n    if (t) {\n      const n = u(document);\n      e.focusedItemId !== t && t === o && // https://github.com/telerik/kendo-react/issues/216 :\n      // No need to focus the wrapping menu item DOM element\n      // when a child DOM element was clicked.\n      !this.itemElement.contains(n) && this.itemElement.focus({ preventScroll: !0 });\n    }\n  }\n  render() {\n    const e = this.props.item, t = e.id, o = d(this.props.menuGuid, t), n = e.separator;\n    return /* @__PURE__ */ s.createElement(s.Fragment, null, n ? /* @__PURE__ */ s.createElement(\n      \"li\",\n      {\n        className: \"k-separator k-item\",\n        \"aria-hidden\": !0,\n        key: o,\n        id: o,\n        ref: (i) => {\n          this.itemElement = i;\n        }\n      }\n    ) : /* @__PURE__ */ s.createElement(\n      \"li\",\n      {\n        id: o,\n        className: this.getMenuItemClassName(e),\n        style: e.cssStyle,\n        tabIndex: t === this.props.tabbableItemId ? 0 : -1,\n        onMouseOver: this.onMouseOver,\n        onMouseLeave: this.onMouseLeave,\n        onMouseDown: (i) => this.props.onMouseDown(i),\n        onBlur: (i) => this.props.onBlur(t, i),\n        onFocus: () => this.props.onFocus(t),\n        onClick: (i) => this.props.onClick(i, t),\n        role: \"menuitem\",\n        \"aria-disabled\": e.disabled ? !0 : void 0,\n        \"aria-haspopup\": e.items.length > 0 ? !0 : void 0,\n        \"aria-expanded\": e.items.length > 0 ? this.Opened : void 0,\n        \"aria-label\": e.text,\n        \"aria-owns\": this.Opened ? o : void 0,\n        ref: (i) => {\n          this.itemElement = i;\n        },\n        key: o\n      },\n      this.contentRender ? this.renderContent() : this.renderMenuItemLink()\n    ), this.renderPopupIfOpened());\n  }\n  renderContent() {\n    const e = this.props.item.contentParentItemId;\n    return /* @__PURE__ */ s.createElement(\"div\", { className: \"k-content\", role: \"presentation\" }, /* @__PURE__ */ s.createElement(this.contentRender, { item: this.props.onOriginalItemNeeded(e), itemId: e }));\n  }\n  renderMenuItemLink() {\n    const e = this.props.item;\n    if (this.linkRender)\n      return /* @__PURE__ */ s.createElement(\n        this.linkRender,\n        {\n          item: this.props.onOriginalItemNeeded(e.id),\n          itemId: e.id,\n          opened: this.Opened,\n          dir: m(this.props.isDirectionRightToLeft)\n        }\n      );\n    const t = this.itemRender ? /* @__PURE__ */ s.createElement(this.itemRender, { item: this.props.onOriginalItemNeeded(e.id), itemId: e.id, key: \"1\" }) : /* @__PURE__ */ s.createElement(\"span\", { className: \"k-menu-link-text\" }, e.text);\n    return /* @__PURE__ */ s.createElement(f, { url: e.url, opened: this.Opened }, this.renderMenuIconIfApplicable(), t, this.renderArrowIfApplicable());\n  }\n  renderPopupIfOpened() {\n    const e = this.props.item.id, t = this.props.animate, { anchorAlign: o, popupAlign: n, collision: i, animationDirection: r } = g(\n      e,\n      this.props.isMenuVertical,\n      this.props.isDirectionRightToLeft\n    ), l = t === !0 ? { openDuration: 300, closeDuration: 300, direction: r } : t === !1 ? !1 : {\n      openDuration: (t == null ? void 0 : t.openDuration) || 300,\n      closeDuration: (t == null ? void 0 : t.closeDuration) || 300,\n      direction: (t == null ? void 0 : t.direction) || r\n    };\n    return /* @__PURE__ */ s.createElement(\n      c,\n      {\n        anchor: this.itemElement,\n        show: this.Opened,\n        popupClass: this.getPopupClassName(),\n        anchorAlign: o,\n        popupAlign: n,\n        collision: i,\n        animate: l,\n        key: \"1\"\n      },\n      /* @__PURE__ */ s.createElement(\n        k,\n        {\n          parentItemId: e,\n          animate: this.props.animate,\n          items: this.props.item.items,\n          menuGuid: this.props.menuGuid,\n          focusedItemId: this.props.focusedItemId,\n          lastItemIdToBeOpened: this.props.lastItemIdToBeOpened,\n          tabbableItemId: this.props.tabbableItemId,\n          itemRender: this.props.itemRender,\n          linkRender: this.props.linkRender,\n          isMenuVertical: this.props.isMenuVertical,\n          isDirectionRightToLeft: this.props.isDirectionRightToLeft,\n          className: \"k-group k-menu-group k-reset k-menu-group-md\",\n          onMouseOver: this.props.onMouseOver,\n          onMouseLeave: this.props.onMouseLeave,\n          onMouseDown: this.props.onMouseDown,\n          onBlur: this.props.onBlur,\n          onFocus: this.props.onFocus,\n          onClick: this.props.onClick,\n          onOriginalItemNeeded: this.props.onOriginalItemNeeded\n        }\n      )\n    );\n  }\n  renderMenuIconIfApplicable() {\n    const { icon: e, svgIcon: t } = this.props.item;\n    return e || t ? /* @__PURE__ */ s.createElement(h, { name: e, icon: t, key: \"0\" }) : null;\n  }\n  renderArrowIfApplicable() {\n    return this.props.item.items.length > 0 ? /* @__PURE__ */ s.createElement(\"span\", { className: \"k-menu-expand-arrow\", \"aria-hidden\": !0 }, /* @__PURE__ */ s.createElement(\n      v,\n      {\n        itemId: this.props.item.id,\n        verticalMenu: this.props.isMenuVertical,\n        dir: m(this.props.isDirectionRightToLeft),\n        key: \"2\"\n      }\n    )) : null;\n  }\n  get itemRender() {\n    return this.props.item.render || this.props.itemRender;\n  }\n  get linkRender() {\n    return this.props.item.linkRender || this.props.linkRender;\n  }\n  get contentRender() {\n    return this.props.item.contentParentItemId ? this.props.item.contentRender : null;\n  }\n  get Opened() {\n    const e = this.props;\n    return e.item.items.length > 0 && I(e.item.id, e.lastItemIdToBeOpened) && // HACK: Wait for the second render because otherwise the scenario of\n    // popup inside popup throws an error (for example, hover of item with id '0_0').\n    !this.isFirstRender;\n  }\n  getPopupClassName() {\n    return p(\"k-menu-popup\", { \"k-rtl\": this.props.isDirectionRightToLeft });\n  }\n  getMenuItemClassName(e) {\n    return p(\n      \"k-item\",\n      \"k-menu-item\",\n      {\n        \"k-first\": M(e.id),\n        \"k-last\": e.isLastFromSiblings,\n        \"k-disabled\": e.disabled\n      },\n      e.cssClass\n    );\n  }\n}\nexport {\n  O as MenuItemInternal,\n  k as MenuItemInternalsList\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,SAASC,gBAAgB,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AACpG,SAASC,KAAK,IAAIC,CAAC,QAAQ,6BAA6B;AACxD,SAASC,cAAc,IAAIC,CAAC,EAAEC,uBAAuB,IAAIC,CAAC,QAAQ,4BAA4B;AAC9F,SAASC,eAAe,IAAIC,CAAC,EAAEC,4BAA4B,IAAIC,CAAC,EAAEC,gBAAgB,IAAIC,CAAC,QAAQ,mBAAmB;AAClH,SAASC,YAAY,IAAIC,CAAC,QAAQ,oBAAoB;AACtD,SAASC,aAAa,IAAIC,CAAC,QAAQ,qBAAqB;AACxD,MAAMC,CAAC,SAASvB,CAAC,CAACwB,SAAS,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,WAAW,GAAIC,CAAC,IAAK;MAC7C,IAAI,CAACC,KAAK,CAACF,WAAW,CAAC,IAAI,CAACE,KAAK,CAACC,YAAY,CAAC,EAAEF,CAAC,CAACG,eAAe,CAAC,CAAC;IACtE,CAAC,EAAE,IAAI,CAACC,YAAY,GAAIJ,CAAC,IAAK;MAC5B,IAAI,CAACC,KAAK,CAACG,YAAY,CAAC,IAAI,CAACH,KAAK,CAACC,YAAY,CAAC,EAAEF,CAAC,CAACG,eAAe,CAAC,CAAC;IACvE,CAAC;EACH;EACAE,MAAMA,CAAA,EAAG;IACP,MAAML,CAAC,GAAG,IAAI,CAACC,KAAK,CAACC,YAAY;IACjC,OAAO,eAAgB9B,CAAC,CAACkC,aAAa,CACpC,IAAI,EACJ;MACEC,SAAS,EAAE,IAAI,CAACN,KAAK,CAACM,SAAS;MAC/BC,IAAI,EAAE,IAAI,CAACP,KAAK,CAACO,IAAI,GAAG,IAAI,CAACP,KAAK,CAACO,IAAI,GAAGR,CAAC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,SAAS;MAC3ES,EAAE,EAAET,CAAC,KAAK,KAAK,CAAC,GAAGd,CAAC,CAAC,IAAI,CAACe,KAAK,CAACS,QAAQ,EAAEV,CAAC,CAAC,GAAG,KAAK,CAAC;MACrDD,WAAW,EAAEC,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAACD,WAAW,GAAG,KAAK,CAAC;MACrDK,YAAY,EAAEJ,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAACI,YAAY,GAAG,KAAK,CAAC;MACvD,kBAAkB,EAAE,IAAI,CAACH,KAAK,CAAC,kBAAkB;IACnD,CAAC,EACD,IAAI,CAACU,gBAAgB,CAAC,CACxB,CAAC;EACH;EACAA,gBAAgBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACV,KAAK,CAACW,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACW,KAAK,CAACE,GAAG,CAAC,CAACd,CAAC,EAAEe,CAAC,KAAK,eAAgB3C,CAAC,CAACkC,aAAa,CACjGU,CAAC,EACD;MACEC,IAAI,EAAEjB,CAAC;MACPkB,OAAO,EAAE,IAAI,CAACjB,KAAK,CAACiB,OAAO;MAC3BC,cAAc,EAAE,IAAI,CAAClB,KAAK,CAACkB,cAAc;MACzCC,sBAAsB,EAAE,IAAI,CAACnB,KAAK,CAACmB,sBAAsB;MACzDC,aAAa,EAAE,IAAI,CAACpB,KAAK,CAACoB,aAAa;MACvCC,oBAAoB,EAAE,IAAI,CAACrB,KAAK,CAACqB,oBAAoB;MACrDC,cAAc,EAAE,IAAI,CAACtB,KAAK,CAACsB,cAAc;MACzCC,UAAU,EAAE,IAAI,CAACvB,KAAK,CAACuB,UAAU;MACjCC,UAAU,EAAE,IAAI,CAACxB,KAAK,CAACwB,UAAU;MACjCf,QAAQ,EAAE,IAAI,CAACT,KAAK,CAACS,QAAQ;MAC7BX,WAAW,EAAE,IAAI,CAACE,KAAK,CAACF,WAAW;MACnCK,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG,YAAY;MACrCsB,WAAW,EAAE,IAAI,CAACzB,KAAK,CAACyB,WAAW;MACnCC,MAAM,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,MAAM;MACzBC,OAAO,EAAE,IAAI,CAAC3B,KAAK,CAAC2B,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAAC5B,KAAK,CAAC4B,OAAO;MAC3BC,oBAAoB,EAAE,IAAI,CAAC7B,KAAK,CAAC6B,oBAAoB;MACrDC,GAAG,EAAEhB;IACP,CACF,CAAC,CAAC,GAAG,IAAI;EACX;AACF;AACA,MAAMC,CAAC,SAAS5C,CAAC,CAACwB,SAAS,CAAC;EAC1BC,WAAWA,CAACG,CAAC,EAAE;IACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACgC,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjC,WAAW,GAAIgB,CAAC,IAAK;MAC3D,IAAI,CAACd,KAAK,CAACF,WAAW,CAAC,IAAI,CAACE,KAAK,CAACgB,IAAI,CAACR,EAAE,CAAC,EAAEM,CAAC,CAACZ,eAAe,CAAC,CAAC;IACjE,CAAC,EAAE,IAAI,CAACC,YAAY,GAAIW,CAAC,IAAK;MAC5B,IAAI,CAACd,KAAK,CAACG,YAAY,CAAC,IAAI,CAACH,KAAK,CAACgB,IAAI,CAACR,EAAE,CAAC,EAAEM,CAAC,CAACZ,eAAe,CAAC,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC8B,KAAK,GAAG;MAAEC,MAAM,EAAE,CAAC;IAAE,CAAC;EAChC;EACAC,iBAAiBA,CAAA,EAAG;IAClB,MAAMnC,CAAC,GAAG,IAAI,CAACC,KAAK,CAACoB,aAAa;MAAEN,CAAC,GAAG,IAAI,CAACd,KAAK,CAACgB,IAAI,CAACR,EAAE;IAC1DT,CAAC,IAAIA,CAAC,KAAKe,CAAC,IAAI,IAAI,CAACqB,WAAW,CAACC,KAAK,CAAC;MAAEC,aAAa,EAAE,CAAC;IAAE,CAAC,CAAC,EAAE,IAAI,CAACN,aAAa,GAAG,CAAC,CAAC;EACxF;EACAO,kBAAkBA,CAACvC,CAAC,EAAE;IACpB,MAAMe,CAAC,GAAG,IAAI,CAACd,KAAK,CAACoB,aAAa;MAAEmB,CAAC,GAAG,IAAI,CAACvC,KAAK,CAACgB,IAAI,CAACR,EAAE;IAC1D,IAAIM,CAAC,EAAE;MACL,MAAM0B,CAAC,GAAGnE,CAAC,CAACoE,QAAQ,CAAC;MACrB1C,CAAC,CAACqB,aAAa,KAAKN,CAAC,IAAIA,CAAC,KAAKyB,CAAC;MAAI;MACpC;MACA;MACA,CAAC,IAAI,CAACJ,WAAW,CAACO,QAAQ,CAACF,CAAC,CAAC,IAAI,IAAI,CAACL,WAAW,CAACC,KAAK,CAAC;QAAEC,aAAa,EAAE,CAAC;MAAE,CAAC,CAAC;IAChF;EACF;EACAjC,MAAMA,CAAA,EAAG;IACP,MAAML,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgB,IAAI;MAAEF,CAAC,GAAGf,CAAC,CAACS,EAAE;MAAE+B,CAAC,GAAGtD,CAAC,CAAC,IAAI,CAACe,KAAK,CAACS,QAAQ,EAAEK,CAAC,CAAC;MAAE0B,CAAC,GAAGzC,CAAC,CAAC4C,SAAS;IACnF,OAAO,eAAgBxE,CAAC,CAACkC,aAAa,CAAClC,CAAC,CAACyE,QAAQ,EAAE,IAAI,EAAEJ,CAAC,GAAG,eAAgBrE,CAAC,CAACkC,aAAa,CAC1F,IAAI,EACJ;MACEC,SAAS,EAAE,oBAAoB;MAC/B,aAAa,EAAE,CAAC,CAAC;MACjBwB,GAAG,EAAES,CAAC;MACN/B,EAAE,EAAE+B,CAAC;MACLM,GAAG,EAAGC,CAAC,IAAK;QACV,IAAI,CAACX,WAAW,GAAGW,CAAC;MACtB;IACF,CACF,CAAC,GAAG,eAAgB3E,CAAC,CAACkC,aAAa,CACjC,IAAI,EACJ;MACEG,EAAE,EAAE+B,CAAC;MACLjC,SAAS,EAAE,IAAI,CAACyC,oBAAoB,CAAChD,CAAC,CAAC;MACvCiD,KAAK,EAAEjD,CAAC,CAACkD,QAAQ;MACjBC,QAAQ,EAAEpC,CAAC,KAAK,IAAI,CAACd,KAAK,CAACsB,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;MAClDxB,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BK,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BsB,WAAW,EAAGqB,CAAC,IAAK,IAAI,CAAC9C,KAAK,CAACyB,WAAW,CAACqB,CAAC,CAAC;MAC7CpB,MAAM,EAAGoB,CAAC,IAAK,IAAI,CAAC9C,KAAK,CAAC0B,MAAM,CAACZ,CAAC,EAAEgC,CAAC,CAAC;MACtCnB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC3B,KAAK,CAAC2B,OAAO,CAACb,CAAC,CAAC;MACpCc,OAAO,EAAGkB,CAAC,IAAK,IAAI,CAAC9C,KAAK,CAAC4B,OAAO,CAACkB,CAAC,EAAEhC,CAAC,CAAC;MACxCP,IAAI,EAAE,UAAU;MAChB,eAAe,EAAER,CAAC,CAACoD,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;MACzC,eAAe,EAAEpD,CAAC,CAACY,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;MACjD,eAAe,EAAEb,CAAC,CAACY,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACwC,MAAM,GAAG,KAAK,CAAC;MAC1D,YAAY,EAAErD,CAAC,CAACsD,IAAI;MACpB,WAAW,EAAE,IAAI,CAACD,MAAM,GAAGb,CAAC,GAAG,KAAK,CAAC;MACrCM,GAAG,EAAGC,CAAC,IAAK;QACV,IAAI,CAACX,WAAW,GAAGW,CAAC;MACtB,CAAC;MACDhB,GAAG,EAAES;IACP,CAAC,EACD,IAAI,CAACe,aAAa,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC,CACtE,CAAC,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;EAChC;EACAF,aAAaA,CAAA,EAAG;IACd,MAAMxD,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgB,IAAI,CAAC0C,mBAAmB;IAC7C,OAAO,eAAgBvF,CAAC,CAACkC,aAAa,CAAC,KAAK,EAAE;MAAEC,SAAS,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAe,CAAC,EAAE,eAAgBpC,CAAC,CAACkC,aAAa,CAAC,IAAI,CAACiD,aAAa,EAAE;MAAEtC,IAAI,EAAE,IAAI,CAAChB,KAAK,CAAC6B,oBAAoB,CAAC9B,CAAC,CAAC;MAAE4D,MAAM,EAAE5D;IAAE,CAAC,CAAC,CAAC;EAC/M;EACAyD,kBAAkBA,CAAA,EAAG;IACnB,MAAMzD,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgB,IAAI;IACzB,IAAI,IAAI,CAACQ,UAAU,EACjB,OAAO,eAAgBrD,CAAC,CAACkC,aAAa,CACpC,IAAI,CAACmB,UAAU,EACf;MACER,IAAI,EAAE,IAAI,CAAChB,KAAK,CAAC6B,oBAAoB,CAAC9B,CAAC,CAACS,EAAE,CAAC;MAC3CmD,MAAM,EAAE5D,CAAC,CAACS,EAAE;MACZyB,MAAM,EAAE,IAAI,CAACmB,MAAM;MACnBQ,GAAG,EAAEzE,CAAC,CAAC,IAAI,CAACa,KAAK,CAACmB,sBAAsB;IAC1C,CACF,CAAC;IACH,MAAML,CAAC,GAAG,IAAI,CAACS,UAAU,GAAG,eAAgBpD,CAAC,CAACkC,aAAa,CAAC,IAAI,CAACkB,UAAU,EAAE;MAAEP,IAAI,EAAE,IAAI,CAAChB,KAAK,CAAC6B,oBAAoB,CAAC9B,CAAC,CAACS,EAAE,CAAC;MAAEmD,MAAM,EAAE5D,CAAC,CAACS,EAAE;MAAEsB,GAAG,EAAE;IAAI,CAAC,CAAC,GAAG,eAAgB3D,CAAC,CAACkC,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAmB,CAAC,EAAEP,CAAC,CAACsD,IAAI,CAAC;IAC1O,OAAO,eAAgBlF,CAAC,CAACkC,aAAa,CAACd,CAAC,EAAE;MAAEsE,GAAG,EAAE9D,CAAC,CAAC8D,GAAG;MAAE5B,MAAM,EAAE,IAAI,CAACmB;IAAO,CAAC,EAAE,IAAI,CAACU,0BAA0B,CAAC,CAAC,EAAEhD,CAAC,EAAE,IAAI,CAACiD,uBAAuB,CAAC,CAAC,CAAC;EACtJ;EACAN,mBAAmBA,CAAA,EAAG;IACpB,MAAM1D,CAAC,GAAG,IAAI,CAACC,KAAK,CAACgB,IAAI,CAACR,EAAE;MAAEM,CAAC,GAAG,IAAI,CAACd,KAAK,CAACiB,OAAO;MAAE;QAAE+C,WAAW,EAAEzB,CAAC;QAAE0B,UAAU,EAAEzB,CAAC;QAAE0B,SAAS,EAAEpB,CAAC;QAAEqB,kBAAkB,EAAEC;MAAE,CAAC,GAAG/E,CAAC,CAC9HU,CAAC,EACD,IAAI,CAACC,KAAK,CAACkB,cAAc,EACzB,IAAI,CAAClB,KAAK,CAACmB,sBACb,CAAC;MAAEkD,CAAC,GAAGvD,CAAC,KAAK,CAAC,CAAC,GAAG;QAAEwD,YAAY,EAAE,GAAG;QAAEC,aAAa,EAAE,GAAG;QAAEC,SAAS,EAAEJ;MAAE,CAAC,GAAGtD,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG;QAC1FwD,YAAY,EAAE,CAACxD,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwD,YAAY,KAAK,GAAG;QAC1DC,aAAa,EAAE,CAACzD,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACyD,aAAa,KAAK,GAAG;QAC5DC,SAAS,EAAE,CAAC1D,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0D,SAAS,KAAKJ;MACnD,CAAC;IACD,OAAO,eAAgBjG,CAAC,CAACkC,aAAa,CACpC1B,CAAC,EACD;MACE8F,MAAM,EAAE,IAAI,CAACtC,WAAW;MACxBuC,IAAI,EAAE,IAAI,CAACtB,MAAM;MACjBuB,UAAU,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACpCZ,WAAW,EAAEzB,CAAC;MACd0B,UAAU,EAAEzB,CAAC;MACb0B,SAAS,EAAEpB,CAAC;MACZ7B,OAAO,EAAEoD,CAAC;MACVvC,GAAG,EAAE;IACP,CAAC,EACD,eAAgB3D,CAAC,CAACkC,aAAa,CAC7BX,CAAC,EACD;MACEO,YAAY,EAAEF,CAAC;MACfkB,OAAO,EAAE,IAAI,CAACjB,KAAK,CAACiB,OAAO;MAC3BN,KAAK,EAAE,IAAI,CAACX,KAAK,CAACgB,IAAI,CAACL,KAAK;MAC5BF,QAAQ,EAAE,IAAI,CAACT,KAAK,CAACS,QAAQ;MAC7BW,aAAa,EAAE,IAAI,CAACpB,KAAK,CAACoB,aAAa;MACvCC,oBAAoB,EAAE,IAAI,CAACrB,KAAK,CAACqB,oBAAoB;MACrDC,cAAc,EAAE,IAAI,CAACtB,KAAK,CAACsB,cAAc;MACzCC,UAAU,EAAE,IAAI,CAACvB,KAAK,CAACuB,UAAU;MACjCC,UAAU,EAAE,IAAI,CAACxB,KAAK,CAACwB,UAAU;MACjCN,cAAc,EAAE,IAAI,CAAClB,KAAK,CAACkB,cAAc;MACzCC,sBAAsB,EAAE,IAAI,CAACnB,KAAK,CAACmB,sBAAsB;MACzDb,SAAS,EAAE,8CAA8C;MACzDR,WAAW,EAAE,IAAI,CAACE,KAAK,CAACF,WAAW;MACnCK,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG,YAAY;MACrCsB,WAAW,EAAE,IAAI,CAACzB,KAAK,CAACyB,WAAW;MACnCC,MAAM,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,MAAM;MACzBC,OAAO,EAAE,IAAI,CAAC3B,KAAK,CAAC2B,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAAC5B,KAAK,CAAC4B,OAAO;MAC3BC,oBAAoB,EAAE,IAAI,CAAC7B,KAAK,CAAC6B;IACnC,CACF,CACF,CAAC;EACH;EACAiC,0BAA0BA,CAAA,EAAG;IAC3B,MAAM;MAAEe,IAAI,EAAE9E,CAAC;MAAE+E,OAAO,EAAEhE;IAAE,CAAC,GAAG,IAAI,CAACd,KAAK,CAACgB,IAAI;IAC/C,OAAOjB,CAAC,IAAIe,CAAC,GAAG,eAAgB3C,CAAC,CAACkC,aAAa,CAAC9B,CAAC,EAAE;MAAEwG,IAAI,EAAEhF,CAAC;MAAE8E,IAAI,EAAE/D,CAAC;MAAEgB,GAAG,EAAE;IAAI,CAAC,CAAC,GAAG,IAAI;EAC3F;EACAiC,uBAAuBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAC/D,KAAK,CAACgB,IAAI,CAACL,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,eAAgBzC,CAAC,CAACkC,aAAa,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE,qBAAqB;MAAE,aAAa,EAAE,CAAC;IAAE,CAAC,EAAE,eAAgBnC,CAAC,CAACkC,aAAa,CACxKZ,CAAC,EACD;MACEkE,MAAM,EAAE,IAAI,CAAC3D,KAAK,CAACgB,IAAI,CAACR,EAAE;MAC1BwE,YAAY,EAAE,IAAI,CAAChF,KAAK,CAACkB,cAAc;MACvC0C,GAAG,EAAEzE,CAAC,CAAC,IAAI,CAACa,KAAK,CAACmB,sBAAsB,CAAC;MACzCW,GAAG,EAAE;IACP,CACF,CAAC,CAAC,GAAG,IAAI;EACX;EACA,IAAIP,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACvB,KAAK,CAACgB,IAAI,CAACZ,MAAM,IAAI,IAAI,CAACJ,KAAK,CAACuB,UAAU;EACxD;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACxB,KAAK,CAACgB,IAAI,CAACQ,UAAU,IAAI,IAAI,CAACxB,KAAK,CAACwB,UAAU;EAC5D;EACA,IAAI8B,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACtD,KAAK,CAACgB,IAAI,CAAC0C,mBAAmB,GAAG,IAAI,CAAC1D,KAAK,CAACgB,IAAI,CAACsC,aAAa,GAAG,IAAI;EACnF;EACA,IAAIF,MAAMA,CAAA,EAAG;IACX,MAAMrD,CAAC,GAAG,IAAI,CAACC,KAAK;IACpB,OAAOD,CAAC,CAACiB,IAAI,CAACL,KAAK,CAACC,MAAM,GAAG,CAAC,IAAI/B,CAAC,CAACkB,CAAC,CAACiB,IAAI,CAACR,EAAE,EAAET,CAAC,CAACsB,oBAAoB,CAAC;IAAI;IAC1E;IACA,CAAC,IAAI,CAACU,aAAa;EACrB;EACA6C,iBAAiBA,CAAA,EAAG;IAClB,OAAOnG,CAAC,CAAC,cAAc,EAAE;MAAE,OAAO,EAAE,IAAI,CAACuB,KAAK,CAACmB;IAAuB,CAAC,CAAC;EAC1E;EACA4B,oBAAoBA,CAAChD,CAAC,EAAE;IACtB,OAAOtB,CAAC,CACN,QAAQ,EACR,aAAa,EACb;MACE,SAAS,EAAEM,CAAC,CAACgB,CAAC,CAACS,EAAE,CAAC;MAClB,QAAQ,EAAET,CAAC,CAACkF,kBAAkB;MAC9B,YAAY,EAAElF,CAAC,CAACoD;IAClB,CAAC,EACDpD,CAAC,CAACmF,QACJ,CAAC;EACH;AACF;AACA,SACEnE,CAAC,IAAIoE,gBAAgB,EACrBzF,CAAC,IAAI0F,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}