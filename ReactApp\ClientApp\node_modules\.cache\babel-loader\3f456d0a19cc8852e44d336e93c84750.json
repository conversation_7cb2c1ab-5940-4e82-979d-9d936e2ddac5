{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { svgIconPropType as P, classNames as p, IconWrap as y } from \"@progress/kendo-react-common\";\nimport { chevronUpIcon as q, chevronDownIcon as D } from \"@progress/kendo-svg-icons\";\nimport { Reveal as F } from \"@progress/kendo-react-animation\";\nfunction K(_ref) {\n  let {\n    imageUrl: d,\n    icon: r,\n    svgIcon: e,\n    iconClass: o\n  } = _ref;\n  return d ? /* @__PURE__ */n.createElement(\"img\", {\n    role: \"presentation\",\n    className: \"k-panelbar-item-icon k-image\",\n    src: d\n  }) : r || e ? /* @__PURE__ */n.createElement(y, {\n    className: \"k-panelbar-item-icon\",\n    name: r,\n    icon: e\n  }) : o ? /* @__PURE__ */n.createElement(\"span\", {\n    role: \"presentation\",\n    className: \"k-panelbar-item-icon \" + o\n  }) : null;\n}\nconst l = class l extends n.PureComponent {\n  constructor(r) {\n    super(r), this.handleItemClick = () => {\n      const {\n        onSelect: e,\n        disabled: o,\n        id: c\n      } = this.props;\n      e && !o && e.call(void 0, {\n        uniquePrivateKey: this.props.uniquePrivateKey,\n        id: c,\n        target: this\n      });\n    }, this.childFactory = e => this.props.keepItemsMounted ? n.cloneElement(e, {\n      ...e.props,\n      in: this.props.expanded\n    }) : e, this.state = {\n      show: r.expanded || !1\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n        id: r,\n        children: e,\n        title: o,\n        uniquePrivateKey: c,\n        disabled: a,\n        selected: m,\n        focused: E,\n        expanded: s,\n        className: g,\n        level: u,\n        headerClassName: v,\n        animation: h,\n        keepItemsMounted: i\n      } = this.props,\n      x = {\n        role: \"treeitem\",\n        \"aria-disabled\": a,\n        \"aria-hidden\": !a && !s,\n        \"aria-selected\": !a && m,\n        \"aria-expanded\": !a && s && !!e\n      },\n      N = p(\"k-panelbar-item\", {\n        \"k-panelbar-header\": u === 0,\n        \"k-expanded\": s && !!e,\n        \"k-disabled\": a\n      }, `k-level-${u}`, g),\n      C = p(\"k-link\", {\n        \"k-selected\": !a && m,\n        \"k-focus\": E\n      }, v),\n      I = {\n        display: \"block\"\n      },\n      w = K(this.props),\n      A = !a && e ? /* @__PURE__ */n.createElement(y, {\n        name: s ? \"chevron-up\" : \"chevron-down\",\n        icon: s ? q : D,\n        className: p(\"k-panelbar-toggle\", s ? \"k-panelbar-collapse\" : \"k-panelbar-expand\")\n      }) : null;\n    let k = !1;\n    e && e[0] && Array.isArray(e) && (k = e[0].type === l);\n    const S = k ? /* @__PURE__ */n.createElement(\"ul\", {\n        role: \"group\",\n        \"aria-expanded\": s,\n        \"aria-hidden\": !s,\n        className: \"k-panelbar-group\",\n        style: {\n          display: i ? this.state.show ? \"block\" : \"none\" : \"block\"\n        }\n      }, e) : e,\n      b = !a && s || i ? S : null,\n      T = (h === void 0 || h) && !a && e ? /* @__PURE__ */n.createElement(F, {\n        transitionEnterDuration: 200,\n        transitionExitDuration: 200,\n        key: c + \"_animation\",\n        style: I,\n        children: b,\n        childFactory: i && this.childFactory,\n        unmountOnExit: !i,\n        onBeforeEnter: () => i && this.setState({\n          show: !0\n        }),\n        onAfterExited: () => i && this.setState({\n          show: !1\n        })\n      }) : b;\n    return /* @__PURE__ */n.createElement(\"li\", {\n      id: r,\n      className: N,\n      ...x\n    }, /* @__PURE__ */n.createElement(\"span\", {\n      className: C,\n      onClick: this.handleItemClick\n    }, w, /* @__PURE__ */n.createElement(\"span\", {\n      className: \"k-panelbar-item-text\"\n    }, o), A), T);\n  }\n};\nl.propTypes = {\n  animation: t.bool,\n  children: t.any,\n  className: t.string,\n  icon: t.string,\n  iconClass: t.string,\n  imageUrl: t.string,\n  svgIcon: P,\n  expanded: t.bool,\n  disabled: t.bool,\n  onSelect: t.func,\n  selected: t.bool,\n  level: t.number,\n  title: t.oneOfType([t.string, t.element]),\n  id: t.oneOfType([t.string, t.number]),\n  focused: t.bool,\n  keepItemsMounted: t.bool\n}, l.defaultProps = {\n  title: \"Untitled\"\n};\nlet f = l;\nexport { f as PanelBarItem };", "map": {"version": 3, "names": ["n", "t", "svgIconPropType", "P", "classNames", "p", "IconWrap", "y", "chevronUpIcon", "q", "chevronDownIcon", "D", "Reveal", "F", "K", "_ref", "imageUrl", "d", "icon", "r", "svgIcon", "e", "iconClass", "o", "createElement", "role", "className", "src", "name", "l", "PureComponent", "constructor", "handleItemClick", "onSelect", "disabled", "id", "c", "props", "call", "uniquePrivateKey", "target", "childFactory", "keepItemsMounted", "cloneElement", "in", "expanded", "state", "show", "render", "children", "title", "a", "selected", "m", "focused", "E", "s", "g", "level", "u", "headerClassName", "v", "animation", "h", "i", "x", "N", "C", "I", "display", "w", "A", "k", "Array", "isArray", "type", "S", "style", "b", "T", "transitionEnterDuration", "transitionExitDuration", "key", "unmountOnExit", "onBeforeEnter", "setState", "onAfterExited", "onClick", "propTypes", "bool", "any", "string", "func", "number", "oneOfType", "element", "defaultProps", "f", "PanelBarItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/panelbar/PanelBarItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport t from \"prop-types\";\nimport { svgIconPropType as P, classNames as p, IconWrap as y } from \"@progress/kendo-react-common\";\nimport { chevronUpIcon as q, chevronDownIcon as D } from \"@progress/kendo-svg-icons\";\nimport { Reveal as F } from \"@progress/kendo-react-animation\";\nfunction K({ imageUrl: d, icon: r, svgIcon: e, iconClass: o }) {\n  return d ? /* @__PURE__ */ n.createElement(\"img\", { role: \"presentation\", className: \"k-panelbar-item-icon k-image\", src: d }) : r || e ? /* @__PURE__ */ n.createElement(y, { className: \"k-panelbar-item-icon\", name: r, icon: e }) : o ? /* @__PURE__ */ n.createElement(\"span\", { role: \"presentation\", className: \"k-panelbar-item-icon \" + o }) : null;\n}\nconst l = class l extends n.PureComponent {\n  constructor(r) {\n    super(r), this.handleItemClick = () => {\n      const { onSelect: e, disabled: o, id: c } = this.props;\n      e && !o && e.call(void 0, {\n        uniquePrivateKey: this.props.uniquePrivateKey,\n        id: c,\n        target: this\n      });\n    }, this.childFactory = (e) => this.props.keepItemsMounted ? n.cloneElement(e, {\n      ...e.props,\n      in: this.props.expanded\n    }) : e, this.state = {\n      show: r.expanded || !1\n    };\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n      id: r,\n      children: e,\n      title: o,\n      uniquePrivateKey: c,\n      disabled: a,\n      selected: m,\n      focused: E,\n      expanded: s,\n      className: g,\n      level: u,\n      headerClassName: v,\n      animation: h,\n      keepItemsMounted: i\n    } = this.props, x = {\n      role: \"treeitem\",\n      \"aria-disabled\": a,\n      \"aria-hidden\": !a && !s,\n      \"aria-selected\": !a && m,\n      \"aria-expanded\": !a && s && !!e\n    }, N = p(\n      \"k-panelbar-item\",\n      {\n        \"k-panelbar-header\": u === 0,\n        \"k-expanded\": s && !!e,\n        \"k-disabled\": a\n      },\n      `k-level-${u}`,\n      g\n    ), C = p(\n      \"k-link\",\n      {\n        \"k-selected\": !a && m,\n        \"k-focus\": E\n      },\n      v\n    ), I = { display: \"block\" }, w = K(this.props), A = !a && e ? /* @__PURE__ */ n.createElement(\n      y,\n      {\n        name: s ? \"chevron-up\" : \"chevron-down\",\n        icon: s ? q : D,\n        className: p(\"k-panelbar-toggle\", s ? \"k-panelbar-collapse\" : \"k-panelbar-expand\")\n      }\n    ) : null;\n    let k = !1;\n    e && e[0] && Array.isArray(e) && (k = e[0].type === l);\n    const S = k ? /* @__PURE__ */ n.createElement(\n      \"ul\",\n      {\n        role: \"group\",\n        \"aria-expanded\": s,\n        \"aria-hidden\": !s,\n        className: \"k-panelbar-group\",\n        style: { display: i ? this.state.show ? \"block\" : \"none\" : \"block\" }\n      },\n      e\n    ) : e, b = !a && s || i ? S : null, T = (h === void 0 || h) && !a && e ? /* @__PURE__ */ n.createElement(\n      F,\n      {\n        transitionEnterDuration: 200,\n        transitionExitDuration: 200,\n        key: c + \"_animation\",\n        style: I,\n        children: b,\n        childFactory: i && this.childFactory,\n        unmountOnExit: !i,\n        onBeforeEnter: () => i && this.setState({ show: !0 }),\n        onAfterExited: () => i && this.setState({ show: !1 })\n      }\n    ) : b;\n    return /* @__PURE__ */ n.createElement(\"li\", { id: r, className: N, ...x }, /* @__PURE__ */ n.createElement(\"span\", { className: C, onClick: this.handleItemClick }, w, /* @__PURE__ */ n.createElement(\"span\", { className: \"k-panelbar-item-text\" }, o), A), T);\n  }\n};\nl.propTypes = {\n  animation: t.bool,\n  children: t.any,\n  className: t.string,\n  icon: t.string,\n  iconClass: t.string,\n  imageUrl: t.string,\n  svgIcon: P,\n  expanded: t.bool,\n  disabled: t.bool,\n  onSelect: t.func,\n  selected: t.bool,\n  level: t.number,\n  title: t.oneOfType([t.string, t.element]),\n  id: t.oneOfType([t.string, t.number]),\n  focused: t.bool,\n  keepItemsMounted: t.bool\n}, l.defaultProps = {\n  title: \"Untitled\"\n};\nlet f = l;\nexport {\n  f as PanelBarItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,eAAe,IAAIC,CAAC,EAAEC,UAAU,IAAIC,CAAC,EAAEC,QAAQ,IAAIC,CAAC,QAAQ,8BAA8B;AACnG,SAASC,aAAa,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,2BAA2B;AACpF,SAASC,MAAM,IAAIC,CAAC,QAAQ,iCAAiC;AAC7D,SAASC,CAACA,CAAAC,IAAA,EAAqD;EAAA,IAApD;IAAEC,QAAQ,EAAEC,CAAC;IAAEC,IAAI,EAAEC,CAAC;IAAEC,OAAO,EAAEC,CAAC;IAAEC,SAAS,EAAEC;EAAE,CAAC,GAAAR,IAAA;EAC3D,OAAOE,CAAC,GAAG,eAAgBjB,CAAC,CAACwB,aAAa,CAAC,KAAK,EAAE;IAAEC,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAE,8BAA8B;IAAEC,GAAG,EAAEV;EAAE,CAAC,CAAC,GAAGE,CAAC,IAAIE,CAAC,GAAG,eAAgBrB,CAAC,CAACwB,aAAa,CAACjB,CAAC,EAAE;IAAEmB,SAAS,EAAE,sBAAsB;IAAEE,IAAI,EAAET,CAAC;IAAED,IAAI,EAAEG;EAAE,CAAC,CAAC,GAAGE,CAAC,GAAG,eAAgBvB,CAAC,CAACwB,aAAa,CAAC,MAAM,EAAE;IAAEC,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAE,uBAAuB,GAAGH;EAAE,CAAC,CAAC,GAAG,IAAI;AAC9V;AACA,MAAMM,CAAC,GAAG,MAAMA,CAAC,SAAS7B,CAAC,CAAC8B,aAAa,CAAC;EACxCC,WAAWA,CAACZ,CAAC,EAAE;IACb,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACa,eAAe,GAAG,MAAM;MACrC,MAAM;QAAEC,QAAQ,EAAEZ,CAAC;QAAEa,QAAQ,EAAEX,CAAC;QAAEY,EAAE,EAAEC;MAAE,CAAC,GAAG,IAAI,CAACC,KAAK;MACtDhB,CAAC,IAAI,CAACE,CAAC,IAAIF,CAAC,CAACiB,IAAI,CAAC,KAAK,CAAC,EAAE;QACxBC,gBAAgB,EAAE,IAAI,CAACF,KAAK,CAACE,gBAAgB;QAC7CJ,EAAE,EAAEC,CAAC;QACLI,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAACC,YAAY,GAAIpB,CAAC,IAAK,IAAI,CAACgB,KAAK,CAACK,gBAAgB,GAAG1C,CAAC,CAAC2C,YAAY,CAACtB,CAAC,EAAE;MAC5E,GAAGA,CAAC,CAACgB,KAAK;MACVO,EAAE,EAAE,IAAI,CAACP,KAAK,CAACQ;IACjB,CAAC,CAAC,GAAGxB,CAAC,EAAE,IAAI,CAACyB,KAAK,GAAG;MACnBC,IAAI,EAAE5B,CAAC,CAAC0B,QAAQ,IAAI,CAAC;IACvB,CAAC;EACH;EACA;AACF;AACA;EACEG,MAAMA,CAAA,EAAG;IACP,MAAM;QACJb,EAAE,EAAEhB,CAAC;QACL8B,QAAQ,EAAE5B,CAAC;QACX6B,KAAK,EAAE3B,CAAC;QACRgB,gBAAgB,EAAEH,CAAC;QACnBF,QAAQ,EAAEiB,CAAC;QACXC,QAAQ,EAAEC,CAAC;QACXC,OAAO,EAAEC,CAAC;QACVV,QAAQ,EAAEW,CAAC;QACX9B,SAAS,EAAE+B,CAAC;QACZC,KAAK,EAAEC,CAAC;QACRC,eAAe,EAAEC,CAAC;QAClBC,SAAS,EAAEC,CAAC;QACZrB,gBAAgB,EAAEsB;MACpB,CAAC,GAAG,IAAI,CAAC3B,KAAK;MAAE4B,CAAC,GAAG;QAClBxC,IAAI,EAAE,UAAU;QAChB,eAAe,EAAE0B,CAAC;QAClB,aAAa,EAAE,CAACA,CAAC,IAAI,CAACK,CAAC;QACvB,eAAe,EAAE,CAACL,CAAC,IAAIE,CAAC;QACxB,eAAe,EAAE,CAACF,CAAC,IAAIK,CAAC,IAAI,CAAC,CAACnC;MAChC,CAAC;MAAE6C,CAAC,GAAG7D,CAAC,CACN,iBAAiB,EACjB;QACE,mBAAmB,EAAEsD,CAAC,KAAK,CAAC;QAC5B,YAAY,EAAEH,CAAC,IAAI,CAAC,CAACnC,CAAC;QACtB,YAAY,EAAE8B;MAChB,CAAC,EACD,WAAWQ,CAAC,EAAE,EACdF,CACF,CAAC;MAAEU,CAAC,GAAG9D,CAAC,CACN,QAAQ,EACR;QACE,YAAY,EAAE,CAAC8C,CAAC,IAAIE,CAAC;QACrB,SAAS,EAAEE;MACb,CAAC,EACDM,CACF,CAAC;MAAEO,CAAC,GAAG;QAAEC,OAAO,EAAE;MAAQ,CAAC;MAAEC,CAAC,GAAGxD,CAAC,CAAC,IAAI,CAACuB,KAAK,CAAC;MAAEkC,CAAC,GAAG,CAACpB,CAAC,IAAI9B,CAAC,GAAG,eAAgBrB,CAAC,CAACwB,aAAa,CAC3FjB,CAAC,EACD;QACEqB,IAAI,EAAE4B,CAAC,GAAG,YAAY,GAAG,cAAc;QACvCtC,IAAI,EAAEsC,CAAC,GAAG/C,CAAC,GAAGE,CAAC;QACfe,SAAS,EAAErB,CAAC,CAAC,mBAAmB,EAAEmD,CAAC,GAAG,qBAAqB,GAAG,mBAAmB;MACnF,CACF,CAAC,GAAG,IAAI;IACR,IAAIgB,CAAC,GAAG,CAAC,CAAC;IACVnD,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAIoD,KAAK,CAACC,OAAO,CAACrD,CAAC,CAAC,KAAKmD,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,CAACsD,IAAI,KAAK9C,CAAC,CAAC;IACtD,MAAM+C,CAAC,GAAGJ,CAAC,GAAG,eAAgBxE,CAAC,CAACwB,aAAa,CAC3C,IAAI,EACJ;QACEC,IAAI,EAAE,OAAO;QACb,eAAe,EAAE+B,CAAC;QAClB,aAAa,EAAE,CAACA,CAAC;QACjB9B,SAAS,EAAE,kBAAkB;QAC7BmD,KAAK,EAAE;UAAER,OAAO,EAAEL,CAAC,GAAG,IAAI,CAAClB,KAAK,CAACC,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG;QAAQ;MACrE,CAAC,EACD1B,CACF,CAAC,GAAGA,CAAC;MAAEyD,CAAC,GAAG,CAAC3B,CAAC,IAAIK,CAAC,IAAIQ,CAAC,GAAGY,CAAC,GAAG,IAAI;MAAEG,CAAC,GAAG,CAAChB,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,KAAK,CAACZ,CAAC,IAAI9B,CAAC,GAAG,eAAgBrB,CAAC,CAACwB,aAAa,CACtGX,CAAC,EACD;QACEmE,uBAAuB,EAAE,GAAG;QAC5BC,sBAAsB,EAAE,GAAG;QAC3BC,GAAG,EAAE9C,CAAC,GAAG,YAAY;QACrByC,KAAK,EAAET,CAAC;QACRnB,QAAQ,EAAE6B,CAAC;QACXrC,YAAY,EAAEuB,CAAC,IAAI,IAAI,CAACvB,YAAY;QACpC0C,aAAa,EAAE,CAACnB,CAAC;QACjBoB,aAAa,EAAEA,CAAA,KAAMpB,CAAC,IAAI,IAAI,CAACqB,QAAQ,CAAC;UAAEtC,IAAI,EAAE,CAAC;QAAE,CAAC,CAAC;QACrDuC,aAAa,EAAEA,CAAA,KAAMtB,CAAC,IAAI,IAAI,CAACqB,QAAQ,CAAC;UAAEtC,IAAI,EAAE,CAAC;QAAE,CAAC;MACtD,CACF,CAAC,GAAG+B,CAAC;IACL,OAAO,eAAgB9E,CAAC,CAACwB,aAAa,CAAC,IAAI,EAAE;MAAEW,EAAE,EAAEhB,CAAC;MAAEO,SAAS,EAAEwC,CAAC;MAAE,GAAGD;IAAE,CAAC,EAAE,eAAgBjE,CAAC,CAACwB,aAAa,CAAC,MAAM,EAAE;MAAEE,SAAS,EAAEyC,CAAC;MAAEoB,OAAO,EAAE,IAAI,CAACvD;IAAgB,CAAC,EAAEsC,CAAC,EAAE,eAAgBtE,CAAC,CAACwB,aAAa,CAAC,MAAM,EAAE;MAAEE,SAAS,EAAE;IAAuB,CAAC,EAAEH,CAAC,CAAC,EAAEgD,CAAC,CAAC,EAAEQ,CAAC,CAAC;EACnQ;AACF,CAAC;AACDlD,CAAC,CAAC2D,SAAS,GAAG;EACZ1B,SAAS,EAAE7D,CAAC,CAACwF,IAAI;EACjBxC,QAAQ,EAAEhD,CAAC,CAACyF,GAAG;EACfhE,SAAS,EAAEzB,CAAC,CAAC0F,MAAM;EACnBzE,IAAI,EAAEjB,CAAC,CAAC0F,MAAM;EACdrE,SAAS,EAAErB,CAAC,CAAC0F,MAAM;EACnB3E,QAAQ,EAAEf,CAAC,CAAC0F,MAAM;EAClBvE,OAAO,EAAEjB,CAAC;EACV0C,QAAQ,EAAE5C,CAAC,CAACwF,IAAI;EAChBvD,QAAQ,EAAEjC,CAAC,CAACwF,IAAI;EAChBxD,QAAQ,EAAEhC,CAAC,CAAC2F,IAAI;EAChBxC,QAAQ,EAAEnD,CAAC,CAACwF,IAAI;EAChB/B,KAAK,EAAEzD,CAAC,CAAC4F,MAAM;EACf3C,KAAK,EAAEjD,CAAC,CAAC6F,SAAS,CAAC,CAAC7F,CAAC,CAAC0F,MAAM,EAAE1F,CAAC,CAAC8F,OAAO,CAAC,CAAC;EACzC5D,EAAE,EAAElC,CAAC,CAAC6F,SAAS,CAAC,CAAC7F,CAAC,CAAC0F,MAAM,EAAE1F,CAAC,CAAC4F,MAAM,CAAC,CAAC;EACrCvC,OAAO,EAAErD,CAAC,CAACwF,IAAI;EACf/C,gBAAgB,EAAEzC,CAAC,CAACwF;AACtB,CAAC,EAAE5D,CAAC,CAACmE,YAAY,GAAG;EAClB9C,KAAK,EAAE;AACT,CAAC;AACD,IAAI+C,CAAC,GAAGpE,CAAC;AACT,SACEoE,CAAC,IAAIC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}