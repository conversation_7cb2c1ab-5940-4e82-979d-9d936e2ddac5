const { override, fixBabelImports, addWebpackAlias } = require('customize-cra');
const copyWebPack = require('copy-webpack-plugin');
const addLessLoader = require('customize-cra-less-loader');
const path = require('path');

const addCopyWebPack = (config) => {
  config.plugins.push(
    new copyWebPack({
      patterns: [
        {
          from: 'src/styles',
          to: 'styles',
          globOptions: { ignore: ['**/*.less'] },
        },
        { from: './Web.config', to: './' },
      ],
    })
  );
  return config;
};

module.exports = override(
  addCopyWebPack,
  fixBabelImports('import', {
    libraryName: 'antd',
    libraryDirectory: 'es',
    style: true,
  }),
  addWebpackAlias({
    '@': path.resolve(__dirname, './src'),
    '@app': path.resolve(__dirname, './src/app'),
    '@pages': path.resolve(__dirname, './src/app/pages'),
  }),
  addLessLoader({
    lessLoaderOptions: {
      lessOptions: {
        javascriptEnabled: true,
        math: 'always',
        modifyVars: {
          '@primary-color': '#419cb9',
          '@link-color': '#0e678e',
          '@font-size-base': '14px',
        },
      },
    },
  })
);
