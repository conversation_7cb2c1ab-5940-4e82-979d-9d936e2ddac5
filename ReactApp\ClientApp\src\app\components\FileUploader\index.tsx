import { useAppSelector } from '@app/hooks/useAppSelector';
import { selectAppState } from '@app/appSlice';
import { warningNotification } from '@app/utils/antNotifications';
import { ERROR_TYPE_CANCEL } from '@app/utils/http/index';
import { ERROR_CODE_409 } from '@app/utils/http/statusCodes';
import logger from '@app/utils/logger';
import { notification } from 'antd';
import { RcFile, UploadFile } from 'antd/lib/upload/interface';
import React, { useEffect, useRef, useState } from 'react';
import { FileRecord } from '../forms/UploaderSubmit/types';
import {
  FILE_INVALID,
  FILE_IS_TOO_LARGE,
  LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,
  RETRYING_MESSAGE,
  TOO_MANY_FILES,
  UPLOAD_FAILED_MESSAGE
} from './constants/errors';
import DragAndDrop from './DragAndDrop';
import useUploader from './hooks/useUploader';
import { FileUploaderProps } from './types';
import UploadProgress from './UploadProgress';
import confirmDiscard from './utils/confirmDiscard';
import { validateFileName, validateSize } from './validators';

const MAX_RETRY_COUNT = 3;
export const FULL_PERCENTAGE = 100;
export const MAXIMUM_FILE_COUNT = 20;

const FileUploader = ({siteId, onCloseModal = () => null, onUpload, onRemove, onUploadComplete}: FileUploaderProps) => {
  const {files, action} = useUploader();
  const {
    add,
    updatePercent,
    updateError,
    increaseChunkCounter,
    isCompleted,
    changeToBeProceedStatus,
    incrementRetryCount,
    resetPercent,
    remove,
    removeAll,
    retry,
    retryAll,
    setReferenceNumber,
  } = action;
  const isShowingError = useRef(false);
  const filesCount = Object.keys(files).length;
  const hasUploadingFiles = !!filesCount;
  const hasUrlUploadPermission = useAppSelector(selectAppState).fileAreaSettings.urlFileUpload;

  useEffect(() => {
    Object.entries(files).forEach(([uid, info]) => {
      if (!info.toBeProceed) {
        fileUpload(uid);
      }
    });
  }, [files]);

  const fileUpload = (uid: string) => {
    if (files[uid].error?.message === FILE_IS_TOO_LARGE) {
      return;
    }
    const {onSuccess, onError, file, onProgress} = files[uid].uploadOptions;
    const upFile = file.slice(files[uid].chunkStartSize, files[uid].chunkEndSize) as File;
    changeToBeProceedStatus(uid, true);
    onUpload({
      fileName: file.name,
      file: upFile,
      siteId: siteId,
      referenceNo: files[uid]?.referenceNumber,
      chunkId: files[uid]?.chunkCounter,
      totalChunkCount: files[uid]?.chunkCount,
      onUploadProgress: (event: any) => {
        const currentProgress = (event.loaded / event.total / files[uid].chunkCount) * FULL_PERCENTAGE;
        const percent = (files[uid].percent as number) + currentProgress;

        onProgress({percent}, file);
        updatePercent(uid, percent, currentProgress);
      },
      cancelToken: files[uid].cancelTokenSource?.token,
    })
      .then((res: any) => {
        if (res.error) throw res.error;
        setReferenceNumber(uid, res.data.referenceNumber);
        if (files[uid].chunkCounter === files[uid].chunkCount) {
          isCompleted(uid, true);
          onSuccess(res.data.success, file);
        } else {
          increaseChunkCounter(uid);
        }
      })
      .catch((error: any) => {
        if (error.code !== ERROR_TYPE_CANCEL) {
          if (files[uid].retryCount < MAX_RETRY_COUNT) {
            if (files[uid].retryCount === 1) {
              warningNotification([''], RETRYING_MESSAGE);
            }
            incrementRetryCount(uid);
            changeToBeProceedStatus(uid, false);
          } else {
            resetPercent(uid);
            if (error.message) {
              logger.error('File Area Module', 'Uploader Component', error.message);
            } else {
              logger.error('File Area Module', 'Uploader Component', error);
            }

            if (error.statusCode && error.statusCode === ERROR_CODE_409) {
              updateError(uid, {
                name: LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,
                message: LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,
              });
            } else {
              updateError(uid, {
                name: UPLOAD_FAILED_MESSAGE,
                message: UPLOAD_FAILED_MESSAGE,
              });
            }
            onError(error);
          }
        }
      });
  };

  const requestUpload = (options: any) => {
    const {file} = options;
    const castedFile = file as unknown as UploadFile;
    if (!validateSize(file.size)) {
      add(castedFile, options, Error(FILE_IS_TOO_LARGE));
      return false;
    }
    if (!validateFileName(file.name)) {
      add(castedFile, options, Error(FILE_INVALID));
      return false;
    }
    add(castedFile, options);
    return true;
  };

  const onDelete = (uid: string) => {
    if (!files[uid].completed) {
      onRemove({
        siteId,
        referenceNumber: files[uid].referenceNumber ? [files[uid].referenceNumber] : [],
        removeChunks: true,
      });
    } else {
      onRemove({
        siteId,
        referenceNumber: files[uid].referenceNumber ? [files[uid].referenceNumber] : [],
      });
    }
    remove(uid);
  };

  const onDeleteAll = () => {
    confirmDiscard(() => {
      const completedReferencesreferences = Object.entries(files)
        .filter(([_, info]) => info.completed && info.referenceNumber)
        .map(([_, info]) => info.referenceNumber);
      const chunckedReferencesreferences = Object.entries(files)
        .filter(([_, info]) => !info.completed && info.referenceNumber)
        .map(([_, info]) => info.referenceNumber);

      if (!!completedReferencesreferences.length) {
        onRemove({siteId, referenceNumber: completedReferencesreferences});
      }
      if (!!chunckedReferencesreferences.length) {
        onRemove({siteId, referenceNumber: chunckedReferencesreferences, removeChunks: true});
      }
      removeAll();
      onCloseModal?.();
    });
  };

  const onRetry = (uid: string) => {
    if (files[uid]) {
      if (files[uid].error?.message === FILE_IS_TOO_LARGE) {
        updateError(uid, {
          name: FILE_IS_TOO_LARGE,
          message: FILE_IS_TOO_LARGE,
        });
      } else {
        retry(uid);
        fileUpload(uid);
      }
    }
  };

  const onRetryAll = () => {
    retryAll();
  };

  const onComplete = () => {
    onUploadComplete(files);
    removeAll();
  };

  const validateFiles = (_file: RcFile, fileList: RcFile[]) => {
    debugger
    if (fileList.length > MAXIMUM_FILE_COUNT) {
      if (!isShowingError.current) {
        isShowingError.current = true;
        notification.error({
          message: TOO_MANY_FILES,
          onClose: () => (isShowingError.current = false),
          className: 'yjErrorMsg',
        });
      }
      return false;
    }
    return true;
  };

  return (
    <>
      <DragAndDrop
        style={{display: hasUploadingFiles ? 'none' : 'block'}}
        customRequest={requestUpload}
        beforeUpload={validateFiles}
        hasUrlUploadPermission={hasUrlUploadPermission}
      />
      {hasUploadingFiles && <UploadProgress files={files} onRetry={onRetry} onRetryAll={onRetryAll} onDelete={onDelete}
                                            onDeleteAll={onDeleteAll} onComplete={onComplete}/>}
    </>
  );
};

export default FileUploader;
