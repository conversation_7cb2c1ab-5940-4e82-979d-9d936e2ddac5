{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { classNames as t } from \"@progress/kendo-react-common\";\nimport * as a from \"react\";\nconst r = a.forwardRef((e, n) => /* @__PURE__ */a.createElement(\"div\", {\n  ref: n,\n  ...e,\n  className: t(\"k-expander-content-wrapper\", e.className)\n}, /* @__PURE__ */a.createElement(\"div\", {\n  className: \"k-expander-content\"\n}, e.children)));\nr.displayName = \"KendoReactExpansionPanelContent\";\nexport { r as ExpansionPanelContent };", "map": {"version": 3, "names": ["classNames", "t", "a", "r", "forwardRef", "e", "n", "createElement", "ref", "className", "children", "displayName", "ExpansionPanelContent"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/expansionpanel/ExpansionPanelContent.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport { classNames as t } from \"@progress/kendo-react-common\";\nimport * as a from \"react\";\nconst r = a.forwardRef(\n  (e, n) => /* @__PURE__ */ a.createElement(\"div\", { ref: n, ...e, className: t(\"k-expander-content-wrapper\", e.className) }, /* @__PURE__ */ a.createElement(\"div\", { className: \"k-expander-content\" }, e.children))\n);\nr.displayName = \"KendoReactExpansionPanelContent\";\nexport {\n  r as ExpansionPanelContent\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,OAAO,KAAKC,CAAC,MAAM,OAAO;AAC1B,MAAMC,CAAC,GAAGD,CAAC,CAACE,UAAU,CACpB,CAACC,CAAC,EAAEC,CAAC,KAAK,eAAgBJ,CAAC,CAACK,aAAa,CAAC,KAAK,EAAE;EAAEC,GAAG,EAAEF,CAAC;EAAE,GAAGD,CAAC;EAAEI,SAAS,EAAER,CAAC,CAAC,4BAA4B,EAAEI,CAAC,CAACI,SAAS;AAAE,CAAC,EAAE,eAAgBP,CAAC,CAACK,aAAa,CAAC,KAAK,EAAE;EAAEE,SAAS,EAAE;AAAqB,CAAC,EAAEJ,CAAC,CAACK,QAAQ,CAAC,CACrN,CAAC;AACDP,CAAC,CAACQ,WAAW,GAAG,iCAAiC;AACjD,SACER,CAAC,IAAIS,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}