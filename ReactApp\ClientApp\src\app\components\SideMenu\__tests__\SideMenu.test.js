import SideMenu from "..";
import React from "react";
import { MemoryRouter } from "react-router";
import { render } from "@testing-library/react";

const menuConfig = [
  {
    path: "/onboarding/",
    title: "Organization Onboarding",
    key: "OrganizationOnboarding",
    guard: [],
    privilaged: true,
    icon: "testClass",
    children: [
      {
        path: "/onboarding/license-management",
        title: "License Management",
        key: "LicenseManagement",
        icon: "testClass",
        guard: [],
      },
    ],
  },
  {
    path: "/filearea/",
    title: "File Area",
    key: "FileArea",
    guard: [],
    privilaged: false,
    icon: "testClass",
    children: [
      {
        path: "/filearea/FileAreaMenu1",
        title: "File Area Menu 1",
        key: "FileAreaMenu1",
        icon: "testClass",
        guard: [],
      },
    ],
  },
];

describe("<SideMenu />", () => {
  it("should have given submenu item", () => {
    const { container } = render(
      <MemoryRouter initialEntries={["/onboarding/organization-management"]}>
        <SideMenu menu={menuConfig} />
      </MemoryRouter>
    );
    expect(container.innerHTML).not.toBe(null);
  });
});
