{"ast": null, "code": "import React from 'react';\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortalControls = props => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n};\n_c = PortalControls;\nexport default PortalControls;\nvar _c;\n$RefreshReg$(_c, \"PortalControls\");", "map": {"version": 3, "names": ["React", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "PortalControls", "props", "_c", "$RefreshReg$"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/src/app/pages/PortalControls/index.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst PortalControls = (props: any) => {\r\n\r\n  return (\r\n    <>\r\n    \r\n    </>\r\n  )\r\n};\r\n\r\nexport default PortalControls;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,QAAA,IAAAC,SAAA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAIC,KAAU,IAAK;EAErC,oBACEF,OAAA,CAAAF,SAAA,mBAEE,CAAC;AAEP,CAAC;AAACK,EAAA,GAPIF,cAAc;AASpB,eAAeA,cAAc;AAAC,IAAAE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}