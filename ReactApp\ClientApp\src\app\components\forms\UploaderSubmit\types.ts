import { FileAreaSettings } from '@app/types/fileAreaTypes';
import { FormInstance } from 'antd/lib/form';
import { Store } from 'antd/lib/form/interface';

export type FileRecord = {
  referenceNumber: string;
  title: string;
  checked?: boolean;
  error?: string;
  created?: string;
};

export type FileEvents = {
  onTitleUpdate: (value: string, index: number) => void;
  onFileSelect: (value: boolean, index: number) => void;
  onFileSelectAll: (value: boolean) => void;
  onFileRemove: (referenceNumber: string) => void;
  onSaveSuccess: (files: FileRecord[]) => void;
};

export type UrlEvents = {
  onUrlUpdate: (value: string) => void;
  onTitleUpdate: (value: string) => void;
};

export type FileUploadFormData = {
  files?: FileRecord[];
  siteId: string;
};

type UploaderSubmitCommonProps = {
  fileList: FileRecord[];
  disabledForm?: boolean;
  forManageFiles?: boolean;
  fileEvents?: FileEvents;
  urlEvents?: UrlEvents;
  onFormChange?: (event: any) => void;
};

export type UploaderSubmitProps = UploaderSubmitCommonProps & {
  siteId: string;
  permissions: FileAreaSettings;
  form: FormInstance;
  onFinish: (values: Store, fileList: FileRecord[]) => void;
};
export type UploaderSubmitContainerProps = UploaderSubmitCommonProps & {
  siteId: string;
  form: FormInstance;
  onFinished: (data: any) => void;
};

export type UploaderSubmitFileListProps = UploaderSubmitCommonProps & {
  fileList: FileRecord[];
  fileEvents?: FileEvents;
  forManageFiles?: boolean;
  onDataChange?: (event: any) => void;
};
