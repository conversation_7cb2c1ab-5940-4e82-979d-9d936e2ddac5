{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport i from \"prop-types\";\nimport { Fade as d } from \"@progress/kendo-react-animation\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst l = class l extends n.Component {\n  constructor() {\n    super(...arguments), this.contentId = this.props.contentPanelId, this.childFactory = e => n.cloneElement(e, {\n      ...e.props,\n      in: e.props.children.props.id === String(this.contentId + this.props.selected)\n    });\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const {\n        children: e,\n        selected: t,\n        contentPanelId: r,\n        keepTabsMounted: s,\n        navItemId: o,\n        renderAllContent: p\n      } = this.props,\n      a = e && typeof t == \"number\" && n.Children.toArray(e)[t],\n      m = c(\"k-tabstrip-content\", \"k-active\", a && a.props.contentClassName);\n    return p ? this.renderAllContent(e) : /* @__PURE__ */n.createElement(\"div\", {\n      className: m,\n      style: this.props.style,\n      id: r,\n      role: \"tabpanel\",\n      \"aria-hidden\": s,\n      \"aria-labelledby\": `${o}-${(t == null ? void 0 : t.toString()) || \"\"}`,\n      tabIndex: 0\n    }, this.renderContent(e));\n  }\n  renderContent(e) {\n    return this.props.keepTabsMounted ? n.Children.map(this.props.children, (t, r) => this.renderChild(t, r)) : this.renderChild(n.Children.toArray(e)[this.props.selected], this.props.selected);\n  }\n  renderAllContent(e) {\n    return n.Children.map(e, (t, r) => {\n      const s = t,\n        o = r === this.props.selected,\n        p = c(\"k-tabstrip-content\", {\n          \"k-active\": o\n        }, s.props.contentClassName);\n      return /* @__PURE__ */n.createElement(\"div\", {\n        className: p,\n        style: this.props.style,\n        id: `${this.props.contentPanelId}-${r}`,\n        role: \"tabpanel\",\n        \"aria-hidden\": !o,\n        hidden: !o,\n        \"aria-labelledby\": `${this.props.navItemId}-${r}`\n      }, this.props.animation ? /* @__PURE__ */n.createElement(d, {\n        key: `${r}-${o}`,\n        appear: !0,\n        enter: !0,\n        exit: !1\n      }, s.props.children) : s.props.children);\n    });\n  }\n  renderChild(e, t) {\n    const r = t === this.props.selected,\n      s = {\n        style: {\n          display: r ? void 0 : \"none\"\n        }\n      },\n      o = {\n        position: \"initial\",\n        display: r ? void 0 : \"none\"\n      };\n    return e.props.disabled ? null : this.props.animation ? /* @__PURE__ */n.createElement(d, {\n      appear: !0,\n      exit: this.props.keepTabsMounted,\n      style: o,\n      childFactory: this.props.keepTabsMounted ? this.childFactory : void 0\n    }, /* @__PURE__ */n.createElement(\"div\", {\n      ...s,\n      id: String(this.contentId + t),\n      key: t\n    }, e.props.children)) : /* @__PURE__ */n.createElement(\"div\", {\n      ...s,\n      key: t\n    }, e.props.children);\n  }\n};\nl.propTypes = {\n  animation: i.bool,\n  children: i.oneOfType([i.element, i.arrayOf(i.element)]),\n  selected: i.number,\n  style: i.object\n};\nlet h = l;\nexport { h as TabStripContent };", "map": {"version": 3, "names": ["n", "i", "Fade", "d", "classNames", "c", "l", "Component", "constructor", "arguments", "contentId", "props", "contentPanelId", "childFactory", "e", "cloneElement", "in", "children", "id", "String", "selected", "render", "t", "r", "keepTabsMounted", "s", "navItemId", "o", "renderAllContent", "p", "a", "Children", "toArray", "m", "contentClassName", "createElement", "className", "style", "role", "toString", "tabIndex", "renderContent", "map", "<PERSON><PERSON><PERSON><PERSON>", "hidden", "animation", "key", "appear", "enter", "exit", "display", "position", "disabled", "propTypes", "bool", "oneOfType", "element", "arrayOf", "number", "object", "h", "TabStripContent"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/tabstrip/TabStripContent.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as n from \"react\";\nimport i from \"prop-types\";\nimport { Fade as d } from \"@progress/kendo-react-animation\";\nimport { classNames as c } from \"@progress/kendo-react-common\";\nconst l = class l extends n.Component {\n  constructor() {\n    super(...arguments), this.contentId = this.props.contentPanelId, this.childFactory = (e) => n.cloneElement(e, {\n      ...e.props,\n      in: e.props.children.props.id === String(this.contentId + this.props.selected)\n    });\n  }\n  /**\n   * @hidden\n   */\n  render() {\n    const { children: e, selected: t, contentPanelId: r, keepTabsMounted: s, navItemId: o, renderAllContent: p } = this.props, a = e && typeof t == \"number\" && n.Children.toArray(e)[t], m = c(\n      \"k-tabstrip-content\",\n      \"k-active\",\n      a && a.props.contentClassName\n    );\n    return p ? this.renderAllContent(e) : /* @__PURE__ */ n.createElement(\n      \"div\",\n      {\n        className: m,\n        style: this.props.style,\n        id: r,\n        role: \"tabpanel\",\n        \"aria-hidden\": s,\n        \"aria-labelledby\": `${o}-${(t == null ? void 0 : t.toString()) || \"\"}`,\n        tabIndex: 0\n      },\n      this.renderContent(e)\n    );\n  }\n  renderContent(e) {\n    return this.props.keepTabsMounted ? n.Children.map(this.props.children, (t, r) => this.renderChild(t, r)) : this.renderChild(\n      n.Children.toArray(e)[this.props.selected],\n      this.props.selected\n    );\n  }\n  renderAllContent(e) {\n    return n.Children.map(e, (t, r) => {\n      const s = t, o = r === this.props.selected, p = c(\n        \"k-tabstrip-content\",\n        { \"k-active\": o },\n        s.props.contentClassName\n      );\n      return /* @__PURE__ */ n.createElement(\n        \"div\",\n        {\n          className: p,\n          style: this.props.style,\n          id: `${this.props.contentPanelId}-${r}`,\n          role: \"tabpanel\",\n          \"aria-hidden\": !o,\n          hidden: !o,\n          \"aria-labelledby\": `${this.props.navItemId}-${r}`\n        },\n        this.props.animation ? /* @__PURE__ */ n.createElement(d, { key: `${r}-${o}`, appear: !0, enter: !0, exit: !1 }, s.props.children) : s.props.children\n      );\n    });\n  }\n  renderChild(e, t) {\n    const r = t === this.props.selected, s = {\n      style: {\n        display: r ? void 0 : \"none\"\n      }\n    }, o = {\n      position: \"initial\",\n      display: r ? void 0 : \"none\"\n    };\n    return e.props.disabled ? null : this.props.animation ? /* @__PURE__ */ n.createElement(\n      d,\n      {\n        appear: !0,\n        exit: this.props.keepTabsMounted,\n        style: o,\n        childFactory: this.props.keepTabsMounted ? this.childFactory : void 0\n      },\n      /* @__PURE__ */ n.createElement(\"div\", { ...s, id: String(this.contentId + t), key: t }, e.props.children)\n    ) : /* @__PURE__ */ n.createElement(\"div\", { ...s, key: t }, e.props.children);\n  }\n};\nl.propTypes = {\n  animation: i.bool,\n  children: i.oneOfType([i.element, i.arrayOf(i.element)]),\n  selected: i.number,\n  style: i.object\n};\nlet h = l;\nexport {\n  h as TabStripContent\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,IAAI,IAAIC,CAAC,QAAQ,iCAAiC;AAC3D,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAG,MAAMA,CAAC,SAASN,CAAC,CAACO,SAAS,CAAC;EACpCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC,EAAE,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,cAAc,EAAE,IAAI,CAACC,YAAY,GAAIC,CAAC,IAAKd,CAAC,CAACe,YAAY,CAACD,CAAC,EAAE;MAC5G,GAAGA,CAAC,CAACH,KAAK;MACVK,EAAE,EAAEF,CAAC,CAACH,KAAK,CAACM,QAAQ,CAACN,KAAK,CAACO,EAAE,KAAKC,MAAM,CAAC,IAAI,CAACT,SAAS,GAAG,IAAI,CAACC,KAAK,CAACS,QAAQ;IAC/E,CAAC,CAAC;EACJ;EACA;AACF;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,MAAM;QAAEJ,QAAQ,EAAEH,CAAC;QAAEM,QAAQ,EAAEE,CAAC;QAAEV,cAAc,EAAEW,CAAC;QAAEC,eAAe,EAAEC,CAAC;QAAEC,SAAS,EAAEC,CAAC;QAAEC,gBAAgB,EAAEC;MAAE,CAAC,GAAG,IAAI,CAAClB,KAAK;MAAEmB,CAAC,GAAGhB,CAAC,IAAI,OAAOQ,CAAC,IAAI,QAAQ,IAAItB,CAAC,CAAC+B,QAAQ,CAACC,OAAO,CAAClB,CAAC,CAAC,CAACQ,CAAC,CAAC;MAAEW,CAAC,GAAG5B,CAAC,CACzL,oBAAoB,EACpB,UAAU,EACVyB,CAAC,IAAIA,CAAC,CAACnB,KAAK,CAACuB,gBACf,CAAC;IACD,OAAOL,CAAC,GAAG,IAAI,CAACD,gBAAgB,CAACd,CAAC,CAAC,GAAG,eAAgBd,CAAC,CAACmC,aAAa,CACnE,KAAK,EACL;MACEC,SAAS,EAAEH,CAAC;MACZI,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,KAAK;MACvBnB,EAAE,EAAEK,CAAC;MACLe,IAAI,EAAE,UAAU;MAChB,aAAa,EAAEb,CAAC;MAChB,iBAAiB,EAAE,GAAGE,CAAC,IAAI,CAACL,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACiB,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE;MACtEC,QAAQ,EAAE;IACZ,CAAC,EACD,IAAI,CAACC,aAAa,CAAC3B,CAAC,CACtB,CAAC;EACH;EACA2B,aAAaA,CAAC3B,CAAC,EAAE;IACf,OAAO,IAAI,CAACH,KAAK,CAACa,eAAe,GAAGxB,CAAC,CAAC+B,QAAQ,CAACW,GAAG,CAAC,IAAI,CAAC/B,KAAK,CAACM,QAAQ,EAAE,CAACK,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACoB,WAAW,CAACrB,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACoB,WAAW,CAC1H3C,CAAC,CAAC+B,QAAQ,CAACC,OAAO,CAAClB,CAAC,CAAC,CAAC,IAAI,CAACH,KAAK,CAACS,QAAQ,CAAC,EAC1C,IAAI,CAACT,KAAK,CAACS,QACb,CAAC;EACH;EACAQ,gBAAgBA,CAACd,CAAC,EAAE;IAClB,OAAOd,CAAC,CAAC+B,QAAQ,CAACW,GAAG,CAAC5B,CAAC,EAAE,CAACQ,CAAC,EAAEC,CAAC,KAAK;MACjC,MAAME,CAAC,GAAGH,CAAC;QAAEK,CAAC,GAAGJ,CAAC,KAAK,IAAI,CAACZ,KAAK,CAACS,QAAQ;QAAES,CAAC,GAAGxB,CAAC,CAC/C,oBAAoB,EACpB;UAAE,UAAU,EAAEsB;QAAE,CAAC,EACjBF,CAAC,CAACd,KAAK,CAACuB,gBACV,CAAC;MACD,OAAO,eAAgBlC,CAAC,CAACmC,aAAa,CACpC,KAAK,EACL;QACEC,SAAS,EAAEP,CAAC;QACZQ,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,KAAK;QACvBnB,EAAE,EAAE,GAAG,IAAI,CAACP,KAAK,CAACC,cAAc,IAAIW,CAAC,EAAE;QACvCe,IAAI,EAAE,UAAU;QAChB,aAAa,EAAE,CAACX,CAAC;QACjBiB,MAAM,EAAE,CAACjB,CAAC;QACV,iBAAiB,EAAE,GAAG,IAAI,CAAChB,KAAK,CAACe,SAAS,IAAIH,CAAC;MACjD,CAAC,EACD,IAAI,CAACZ,KAAK,CAACkC,SAAS,GAAG,eAAgB7C,CAAC,CAACmC,aAAa,CAAChC,CAAC,EAAE;QAAE2C,GAAG,EAAE,GAAGvB,CAAC,IAAII,CAAC,EAAE;QAAEoB,MAAM,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE,CAAC;MAAE,CAAC,EAAExB,CAAC,CAACd,KAAK,CAACM,QAAQ,CAAC,GAAGQ,CAAC,CAACd,KAAK,CAACM,QAC/I,CAAC;IACH,CAAC,CAAC;EACJ;EACA0B,WAAWA,CAAC7B,CAAC,EAAEQ,CAAC,EAAE;IAChB,MAAMC,CAAC,GAAGD,CAAC,KAAK,IAAI,CAACX,KAAK,CAACS,QAAQ;MAAEK,CAAC,GAAG;QACvCY,KAAK,EAAE;UACLa,OAAO,EAAE3B,CAAC,GAAG,KAAK,CAAC,GAAG;QACxB;MACF,CAAC;MAAEI,CAAC,GAAG;QACLwB,QAAQ,EAAE,SAAS;QACnBD,OAAO,EAAE3B,CAAC,GAAG,KAAK,CAAC,GAAG;MACxB,CAAC;IACD,OAAOT,CAAC,CAACH,KAAK,CAACyC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAACzC,KAAK,CAACkC,SAAS,GAAG,eAAgB7C,CAAC,CAACmC,aAAa,CACrFhC,CAAC,EACD;MACE4C,MAAM,EAAE,CAAC,CAAC;MACVE,IAAI,EAAE,IAAI,CAACtC,KAAK,CAACa,eAAe;MAChCa,KAAK,EAAEV,CAAC;MACRd,YAAY,EAAE,IAAI,CAACF,KAAK,CAACa,eAAe,GAAG,IAAI,CAACX,YAAY,GAAG,KAAK;IACtE,CAAC,EACD,eAAgBb,CAAC,CAACmC,aAAa,CAAC,KAAK,EAAE;MAAE,GAAGV,CAAC;MAAEP,EAAE,EAAEC,MAAM,CAAC,IAAI,CAACT,SAAS,GAAGY,CAAC,CAAC;MAAEwB,GAAG,EAAExB;IAAE,CAAC,EAAER,CAAC,CAACH,KAAK,CAACM,QAAQ,CAC3G,CAAC,GAAG,eAAgBjB,CAAC,CAACmC,aAAa,CAAC,KAAK,EAAE;MAAE,GAAGV,CAAC;MAAEqB,GAAG,EAAExB;IAAE,CAAC,EAAER,CAAC,CAACH,KAAK,CAACM,QAAQ,CAAC;EAChF;AACF,CAAC;AACDX,CAAC,CAAC+C,SAAS,GAAG;EACZR,SAAS,EAAE5C,CAAC,CAACqD,IAAI;EACjBrC,QAAQ,EAAEhB,CAAC,CAACsD,SAAS,CAAC,CAACtD,CAAC,CAACuD,OAAO,EAAEvD,CAAC,CAACwD,OAAO,CAACxD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC;EACxDpC,QAAQ,EAAEnB,CAAC,CAACyD,MAAM;EAClBrB,KAAK,EAAEpC,CAAC,CAAC0D;AACX,CAAC;AACD,IAAIC,CAAC,GAAGtD,CAAC;AACT,SACEsD,CAAC,IAAIC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}