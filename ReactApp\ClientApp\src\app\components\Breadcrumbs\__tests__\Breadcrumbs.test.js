import React from "react";
import { BrowserRouter } from "react-router-dom";
import { Provider } from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import { render } from "@testing-library/react";
import Breadcrumbs from "..";
const samplePageInnerPath = "/sample-page/inner";

const _dummyComponent = <div>Page</div>;

const _dummyRoute = [
  {
    path: "/sample-page",
    title: "Sample Page",
    component: _dummyComponent,
    exact: false,
    guard: [],
    useInBreadcrumbs: true,
    routes: [
      {
        path: samplePageInnerPath,
        title: "Inner Page",
        component: _dummyComponent,
        exact: false,
        guard: [],
        useInBreadcrumbs: true,
      },
    ],
  },
];

const ReduxProvider = ({ children, store }) => <Provider store={store}>{children}</Provider>;
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);
const getCustomizedBreadCrumb = (breadComponent) => {
  const INITIAL_STATE = { configuration: {} };
  const store = mockStore(INITIAL_STATE);
  return <ReduxProvider store={store}>{breadComponent}</ReduxProvider>;
};

describe("<Breadcrumbs/>", () => {
  it("should render Breadcrumbs component", () => {
    const { container } = render(
      getCustomizedBreadCrumb(
        <BrowserRouter>
          <Breadcrumbs routes={_dummyRoute} />
        </BrowserRouter>
      )
    );
    expect(container.innerHTML).not.toBe(null);
  });
});
