@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjUploadFailMessage {
  &:extend(.yjuploadfailmessage);

  background-color: lighten(@color-danger, 50%);
  border: 1px solid lighten(@color-danger, 45%);
  border-radius: 4px;
  color: @color-danger;
  font-size: @font-size-base/1.1;
  padding: .3em .8em;
}

.yjUploadMainActionButtonWrapper {
  background-color: fade(@color-secondary, 10%);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: @font-size-lg;
  padding: .5em;
}

.yjUploadProgressContainer {
  background: @color-bg-upload-progress-wrapper;
  border: 1px dashed #DDDDDD;
  border-radius: 10px;
  //position: relative;
  width: 100%;

  &.collpased {
    height: 20vh;
  }

  .yjProgressHeaderButtonWrapper {

    .yjProgressHeaderButtonLeft {
      margin-right: 10px;

      button {
        &:extend(.yjuploadmainactionbuttonwrapper);

        margin: 0 5px;

        &:first-child {
          background-color: transparent;
          border: none;
          color: @color-secondary;
        }

        &:last-child {
          background-color: transparent;
          border: none;
          color: @color-danger;
        }
      }
    }

    .yjProgressHeaderButtonRight {

      button {
        &:extend(.yjuploadmainactionbuttonwrapper);

        color: @text-color;
        padding: .2em .5em;
      }
    }

    .flex-mixin(center, flex, space-evenly);
  }

  .yjProgressWrapper {
    border-top: 1px solid lighten(@color-accent-border, 50%);
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, .15);
    max-height: 50vh;
    overflow: hidden auto;
    padding: .8em;
    transition: max-height, .5s;
  }

  .yjProgressWrapperCollapsed {
    max-height: 16vh;
    transition: max-height .5s;
  }

  .yjProgressErrorMessageWrapper {
    &:extend(.yjuploadfailmessage);

    padding: .4em 1em;

    .font-mixin(@font-primary, @yjff-semibold);
  }
}

.yjProgressWrapperHeader {
  padding: .5em 1em;
  width: 100%;

  h6 {
    color: @text-color;
    font-size: @font-size-lg;
    margin: 0;
    text-transform: @yj-transform;

    span {
      color: @color-secondary;
    }

    .font-mixin(@font-primary, @yjff-bold);
  }

  .flex-mixin(center, flex, space-between);
}

.yjProgressWrapperContent {
  background: fade(#48B22E, 36%);
  border: 1px solid @color-border;
  border-radius: 3px;
  box-shadow: 0 0 4px 0 lighten(@text-color, 65%);
  margin: 5px 5px 12px;
  padding: .5em 1em;

  &:hover {
    background-color: fade(@color-primary, 25%);
    box-shadow: 0 0 4px 0 lighten(@color-primary, 50%);
  }

  .yjProgressFileDetailsContainer {

    span {
      font-size: @font-size-base/1.2;
      margin-right: 15px;

      &:last-child {
        margin-right: 0;
      }

      .font-mixin(@font-primary, @yjff-semibold);
    }

    .yjFileDetailsWrapper {
      .flex-mixin(center, flex, space-between);
    }

    .fileName {
      color: @text-color;
      display: block;
      font-size: @font-size-base;
      max-width: 60vw;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .fileSize {
      color: @color-secondary;
    }

    .progressValue {
      background: #38B445;
      border-radius: 2px;
      color: @color-white;
      padding: .2em .5em;
    }

    .flex-mixin(center, flex, space-between);
  }
}

.yjFileProgressWrapper {
  background: @color-bg-upload-progress-wrapper;
  //position: absolute;
  width: 100%;
  z-index: 1000;
}

.yjFileDetailsButtonWrapper {

  button {
    background: transparent;
    border: none;
    box-shadow: none;

    &:hover {
      background: fade(@color-secondary, 6%);
    }

    &:active {
      background: transparent;
    }

    &:focus {
      background: transparent;
    }

    &:disabled {
      background-color: transparent;

      &:hover {
        background: fade(@color-secondary, 6%);
      }
    }
  }

  .refreshIcon {
    color: @color-secondary;
  }

  .deleteIcon {
    color: @color-danger;
  }
}

.attRequired {
  color: @color-danger;
}