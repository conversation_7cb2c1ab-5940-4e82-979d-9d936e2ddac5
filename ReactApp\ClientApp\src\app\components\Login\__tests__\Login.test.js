import React from "react";
import { mount } from "enzyme";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";

import Login from "../index";
import * as Notifications from "../../../utils/antNotifications";
// required to facilitate missing window events
import "../../../../unit-test-utils";

const ReduxProvider = ({ children, store }) => <Provider store={store}>{children}</Provider>;
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

describe("Login Component", () => {
  it("should render without crashing", () => {
    const INITIAL_STATE = {
      auth: {
        isAuthenticated: false,
        errors: ["Sample Error"],
      },
    };
    const store = mockStore(INITIAL_STATE);

    const wrapper = mount(
      <ReduxProvider store={store}>
        <Login />
      </ReduxProvider>
    );
    expect(wrapper.html()).not.toBeNull();
  });

  it("should redirect user to licence management if already signed in ", () => {
    const mockPushFn = jest.fn();
    const INITIAL_STATE = {
      auth: {
        isAuthenticated: true,
        errors: [],
      },
    };
    const store = mockStore(INITIAL_STATE);

    mount(
      <ReduxProvider store={store}>
        <Login history={{ push: mockPushFn }} />
      </ReduxProvider>
    );
    expect(mockPushFn).toBeCalledWith("/dashboard");
  });

  it("should should show error if any error available", () => {
    const INITIAL_STATE = {
      auth: {
        isAuthenticated: false,
        errors: ["Unit test error 01", "Unit test error 02"],
      },
    };
    const store = mockStore(INITIAL_STATE);

    const errorNotificationMock = jest.fn();
    // override the default implementation with jest mock
    Notifications.errorNotification = jest.fn();
    // supply the mock implementation
    Notifications.errorNotification.mockImplementation(errorNotificationMock);

    mount(
      <ReduxProvider store={store}>
        <Login />
      </ReduxProvider>
    );
    expect(errorNotificationMock).toBeCalledWith([""], "Unit test error 01");
  });
});
