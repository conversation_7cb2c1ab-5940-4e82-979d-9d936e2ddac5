{"ast": null, "code": "/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport i from \"prop-types\";\nimport { classNames as l } from \"@progress/kendo-react-common\";\nconst s = e => {\n  const a = t.useRef(null),\n    c = t.useCallback(n => {\n      e.onClick && e.onClick.call(void 0, {\n        syntheticEvent: n,\n        item: e.item,\n        title: e.title\n      });\n    }, [e.item, e.title, e.onClick]);\n  return t.useEffect(() => {\n    a.current && e.focused && a.current.focus();\n  }, [e.focused]), /* @__PURE__ */t.createElement(\"span\", {\n    style: e.style,\n    tabIndex: e.tabIndex,\n    className: l(\"k-actionsheet-item\", \"k-cursor-pointer\", e.disabled && \"k-disabled\", e.className),\n    ref: a,\n    role: \"button\",\n    \"aria-disabled\": e.disabled,\n    onClick: c\n  }, /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-actionsheet-action\"\n  }, e.icon && /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-icon-wrap\"\n  }, e.icon), (e.title || e.description) && /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-actionsheet-item-text\"\n  }, e.title && /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-actionsheet-item-title\"\n  }, e.title), e.description && /* @__PURE__ */t.createElement(\"span\", {\n    className: \"k-actionsheet-item-description\"\n  }, e.description))));\n};\ns.propTypes = {\n  className: i.string,\n  style: i.object,\n  description: i.string,\n  disabled: i.bool,\n  group: i.oneOf([\"top\", \"bottom\"]),\n  icon: i.element,\n  title: i.string\n};\nexport { s as ActionSheetItem };", "map": {"version": 3, "names": ["t", "i", "classNames", "l", "s", "e", "a", "useRef", "c", "useCallback", "n", "onClick", "call", "syntheticEvent", "item", "title", "useEffect", "current", "focused", "focus", "createElement", "style", "tabIndex", "className", "disabled", "ref", "role", "icon", "description", "propTypes", "string", "object", "bool", "group", "oneOf", "element", "ActionSheetItem"], "sources": ["D:/Zone24x7/Workspaces/FrontEnd-Portal/ReactApp/ClientApp/node_modules/@progress/kendo-react-layout/actionsheet/ActionSheetItem.mjs"], "sourcesContent": ["/**\n * @license\n *-------------------------------------------------------------------------------------------\n * Copyright © 2025 Progress Software Corporation. All rights reserved.\n * Licensed under commercial license. See LICENSE.md in the package root for more information\n *-------------------------------------------------------------------------------------------\n */\nimport * as t from \"react\";\nimport i from \"prop-types\";\nimport { classNames as l } from \"@progress/kendo-react-common\";\nconst s = (e) => {\n  const a = t.useRef(null), c = t.useCallback(\n    (n) => {\n      e.onClick && e.onClick.call(void 0, {\n        syntheticEvent: n,\n        item: e.item,\n        title: e.title\n      });\n    },\n    [e.item, e.title, e.onClick]\n  );\n  return t.useEffect(() => {\n    a.current && e.focused && a.current.focus();\n  }, [e.focused]), /* @__PURE__ */ t.createElement(\n    \"span\",\n    {\n      style: e.style,\n      tabIndex: e.tabIndex,\n      className: l(\n        \"k-actionsheet-item\",\n        \"k-cursor-pointer\",\n        e.disabled && \"k-disabled\",\n        e.className\n      ),\n      ref: a,\n      role: \"button\",\n      \"aria-disabled\": e.disabled,\n      onClick: c\n    },\n    /* @__PURE__ */ t.createElement(\"span\", { className: \"k-actionsheet-action\" }, e.icon && /* @__PURE__ */ t.createElement(\"span\", { className: \"k-icon-wrap\" }, e.icon), (e.title || e.description) && /* @__PURE__ */ t.createElement(\"span\", { className: \"k-actionsheet-item-text\" }, e.title && /* @__PURE__ */ t.createElement(\"span\", { className: \"k-actionsheet-item-title\" }, e.title), e.description && /* @__PURE__ */ t.createElement(\"span\", { className: \"k-actionsheet-item-description\" }, e.description)))\n  );\n};\ns.propTypes = {\n  className: i.string,\n  style: i.object,\n  description: i.string,\n  disabled: i.bool,\n  group: i.oneOf([\"top\", \"bottom\"]),\n  icon: i.element,\n  title: i.string\n};\nexport {\n  s as ActionSheetItem\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,CAAC,MAAM,OAAO;AAC1B,OAAOC,CAAC,MAAM,YAAY;AAC1B,SAASC,UAAU,IAAIC,CAAC,QAAQ,8BAA8B;AAC9D,MAAMC,CAAC,GAAIC,CAAC,IAAK;EACf,MAAMC,CAAC,GAAGN,CAAC,CAACO,MAAM,CAAC,IAAI,CAAC;IAAEC,CAAC,GAAGR,CAAC,CAACS,WAAW,CACxCC,CAAC,IAAK;MACLL,CAAC,CAACM,OAAO,IAAIN,CAAC,CAACM,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,EAAE;QAClCC,cAAc,EAAEH,CAAC;QACjBI,IAAI,EAAET,CAAC,CAACS,IAAI;QACZC,KAAK,EAAEV,CAAC,CAACU;MACX,CAAC,CAAC;IACJ,CAAC,EACD,CAACV,CAAC,CAACS,IAAI,EAAET,CAAC,CAACU,KAAK,EAAEV,CAAC,CAACM,OAAO,CAC7B,CAAC;EACD,OAAOX,CAAC,CAACgB,SAAS,CAAC,MAAM;IACvBV,CAAC,CAACW,OAAO,IAAIZ,CAAC,CAACa,OAAO,IAAIZ,CAAC,CAACW,OAAO,CAACE,KAAK,CAAC,CAAC;EAC7C,CAAC,EAAE,CAACd,CAAC,CAACa,OAAO,CAAC,CAAC,EAAE,eAAgBlB,CAAC,CAACoB,aAAa,CAC9C,MAAM,EACN;IACEC,KAAK,EAAEhB,CAAC,CAACgB,KAAK;IACdC,QAAQ,EAAEjB,CAAC,CAACiB,QAAQ;IACpBC,SAAS,EAAEpB,CAAC,CACV,oBAAoB,EACpB,kBAAkB,EAClBE,CAAC,CAACmB,QAAQ,IAAI,YAAY,EAC1BnB,CAAC,CAACkB,SACJ,CAAC;IACDE,GAAG,EAAEnB,CAAC;IACNoB,IAAI,EAAE,QAAQ;IACd,eAAe,EAAErB,CAAC,CAACmB,QAAQ;IAC3Bb,OAAO,EAAEH;EACX,CAAC,EACD,eAAgBR,CAAC,CAACoB,aAAa,CAAC,MAAM,EAAE;IAAEG,SAAS,EAAE;EAAuB,CAAC,EAAElB,CAAC,CAACsB,IAAI,IAAI,eAAgB3B,CAAC,CAACoB,aAAa,CAAC,MAAM,EAAE;IAAEG,SAAS,EAAE;EAAc,CAAC,EAAElB,CAAC,CAACsB,IAAI,CAAC,EAAE,CAACtB,CAAC,CAACU,KAAK,IAAIV,CAAC,CAACuB,WAAW,KAAK,eAAgB5B,CAAC,CAACoB,aAAa,CAAC,MAAM,EAAE;IAAEG,SAAS,EAAE;EAA0B,CAAC,EAAElB,CAAC,CAACU,KAAK,IAAI,eAAgBf,CAAC,CAACoB,aAAa,CAAC,MAAM,EAAE;IAAEG,SAAS,EAAE;EAA2B,CAAC,EAAElB,CAAC,CAACU,KAAK,CAAC,EAAEV,CAAC,CAACuB,WAAW,IAAI,eAAgB5B,CAAC,CAACoB,aAAa,CAAC,MAAM,EAAE;IAAEG,SAAS,EAAE;EAAiC,CAAC,EAAElB,CAAC,CAACuB,WAAW,CAAC,CAAC,CAC3f,CAAC;AACH,CAAC;AACDxB,CAAC,CAACyB,SAAS,GAAG;EACZN,SAAS,EAAEtB,CAAC,CAAC6B,MAAM;EACnBT,KAAK,EAAEpB,CAAC,CAAC8B,MAAM;EACfH,WAAW,EAAE3B,CAAC,CAAC6B,MAAM;EACrBN,QAAQ,EAAEvB,CAAC,CAAC+B,IAAI;EAChBC,KAAK,EAAEhC,CAAC,CAACiC,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;EACjCP,IAAI,EAAE1B,CAAC,CAACkC,OAAO;EACfpB,KAAK,EAAEd,CAAC,CAAC6B;AACX,CAAC;AACD,SACE1B,CAAC,IAAIgC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}